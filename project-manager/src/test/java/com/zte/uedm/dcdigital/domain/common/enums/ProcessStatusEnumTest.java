package com.zte.uedm.dcdigital.domain.common.enums;

/* Started by AICoder, pid:f6e43de072m217314dd409246059a44986e3fd15 */
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

public class ProcessStatusEnumTest {

    @Test
    public void testGetStatus() {
        assertEquals("running", ProcessStatusEnum.RUNNING.getStatus());
        assertEquals("terminated", ProcessStatusEnum.TERMINATED.getStatus());
        assertEquals("completed", ProcessStatusEnum.COMPLETED.getStatus());
        assertEquals("canceled", ProcessStatusEnum.CANCELED.getStatus());
    }

    @Test
    public void testGetProcessStatus_ValidStatus() {
        assertEquals(ProcessStatusEnum.RUNNING, ProcessStatusEnum.getProcessStatus("running"));
        assertEquals(ProcessStatusEnum.TERMINATED, ProcessStatusEnum.getProcessStatus("terminated"));
        assertEquals(ProcessStatusEnum.COMPLETED, ProcessStatusEnum.getProcessStatus("completed"));
        assertEquals(ProcessStatusEnum.CANCELED, ProcessStatusEnum.getProcessStatus("canceled"));
    }

    @Test
    public void testGetProcessStatus_InvalidStatus() {
        assertNull(ProcessStatusEnum.getProcessStatus("invalid"));
    }

    @Test
    public void testGetProcessStatus_NullAndEmptyString() {
        assertNull(ProcessStatusEnum.getProcessStatus(null));
        assertNull(ProcessStatusEnum.getProcessStatus(""));
    }

    @Test
    public void testGetProcessStatus_CaseInsensitive() {
        assertEquals(ProcessStatusEnum.RUNNING, ProcessStatusEnum.getProcessStatus("RuNnInG"));
        assertEquals(ProcessStatusEnum.TERMINATED, ProcessStatusEnum.getProcessStatus("TeRmInAtEd"));
        assertEquals(ProcessStatusEnum.COMPLETED, ProcessStatusEnum.getProcessStatus("CoMpLeTeD"));
        assertEquals(ProcessStatusEnum.CANCELED, ProcessStatusEnum.getProcessStatus("CaNcElEd"));
    }
}
/* Ended by AICoder, pid:f6e43de072m217314dd409246059a44986e3fd15 */
