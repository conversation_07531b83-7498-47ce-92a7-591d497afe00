package com.zte.uedm.dcdigital.application.biddingdocument.executor.impl;
/* Started by AICoder, pid:af4caj6897x990f143ec0809b0684c6d7b20341a */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.BiddingDocumentClarificationDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BiddingDocumentClarificationQueryServiceImplTest {

    @Mock
    private BiddingDocumentClarificationDomainService biddingDocumentClarificationDomainService;

    @InjectMocks
    private BiddingDocumentClarificationQueryServiceImpl queryService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryListByCondition() {
        BiddingDocumentClarificationQueryDto queryDto = new BiddingDocumentClarificationQueryDto();
        PageVO<BiddingDocumentClarificationVo> expectedPageVO = new PageVO<>();
        when(biddingDocumentClarificationDomainService.queryListByCondition(any(BiddingDocumentClarificationQueryDto.class)))
                .thenReturn(expectedPageVO);

        PageVO<BiddingDocumentClarificationVo> result = queryService.queryListByCondition(queryDto);

        assertNotNull(result);
        assertEquals(expectedPageVO, result);
        verify(biddingDocumentClarificationDomainService, times(1)).queryListByCondition(queryDto);
    }

    @Test
    void testQueryDetailById() {
        String id = "1";
        BiddingDocumentClarificationDetailVo expectedDetailVo = new BiddingDocumentClarificationDetailVo();
        when(biddingDocumentClarificationDomainService.queryDetailById(id)).thenReturn(expectedDetailVo);

        BiddingDocumentClarificationDetailVo result = queryService.queryDetailById(id);

        assertNotNull(result);
        assertEquals(expectedDetailVo, result);
        verify(biddingDocumentClarificationDomainService, times(1)).queryDetailById(id);
    }
}

/* Ended by AICoder, pid:af4caj6897x990f143ec0809b0684c6d7b20341a */
