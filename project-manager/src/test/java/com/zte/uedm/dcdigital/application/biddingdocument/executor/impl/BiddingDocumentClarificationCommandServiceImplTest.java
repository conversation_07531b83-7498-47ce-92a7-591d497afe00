package com.zte.uedm.dcdigital.application.biddingdocument.executor.impl;

/* Started by AICoder, pid:5af48s93f8laa3314ef00b41c115f82776f1ce39 */
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.service.BiddingDocumentClarificationDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BiddingDocumentClarificationCommandServiceImplTest {

    @Mock
    private BiddingDocumentClarificationDomainService biddingDocumentClarificationDomainService;

    @InjectMocks
    private BiddingDocumentClarificationCommandServiceImpl commandService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testAdd() {
        BiddingDocumentClarificationAddDto addDto = new BiddingDocumentClarificationAddDto();
        addDto.setFileName("test.txt");
        addDto.setClarification("This is a test clarification");

        doNothing().when(biddingDocumentClarificationDomainService).add(any());

        assertDoesNotThrow(() -> commandService.add(addDto));
        verify(biddingDocumentClarificationDomainService, times(1)).add(any());
    }

   @Test
    void testAddWithInvalidParams() {
        BiddingDocumentClarificationAddDto addDto = new BiddingDocumentClarificationAddDto();
        assertThrows(BusinessException.class, () -> commandService.add(addDto));

        String str = "aaaaa";
        for (int i = 0; i < 50; i++) {
            str += "aaaaa";
        }
        addDto.setFileName(str);
        assertThrows(BusinessException.class, () -> commandService.add(addDto));

        addDto.setFileName("validName");
        assertThrows(BusinessException.class, () -> commandService.add(addDto));

        addDto.setClarification("valid Clarification");
        addDto.setFileIdList(Arrays.asList("1", "2", "3", "4", "5","6"));
        assertThrows(BusinessException.class, () -> commandService.add(addDto));
    }

    @Test
    void testEdit() {
        BiddingDocumentClarificationEditDto editDto = new BiddingDocumentClarificationEditDto();
        editDto.setId("1");
        editDto.setFileName("test.txt");
        editDto.setClarification("xxx");
        editDto.setFileIdList(Arrays.asList("1"));

        doNothing().when(biddingDocumentClarificationDomainService).update(any());

        assertDoesNotThrow(() -> commandService.edit(editDto));
        verify(biddingDocumentClarificationDomainService, times(1)).update(any());
    }

    @Test
    void testDeleteById() {
        String id = "1";

        doNothing().when(biddingDocumentClarificationDomainService).deleteById(id);

        assertDoesNotThrow(() -> commandService.deleteById(id));
        verify(biddingDocumentClarificationDomainService, times(1)).deleteById(id);
    }

    @Test
    void testExport() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        String projectId = "1";
        BiddingDocumentClarificationQueryDto dto = new BiddingDocumentClarificationQueryDto();
        dto.setProjectId(projectId);

        doNothing().when(biddingDocumentClarificationDomainService).export(dto, response);

        assertDoesNotThrow(() -> commandService.export(dto, response));
        verify(biddingDocumentClarificationDomainService, times(1)).export(dto, response);
    }

    @Test
    void testQueryProductCategory() {
        String projectId = "1";
        List<ProductCategoryInfoVo> expectedList = Arrays.asList(new ProductCategoryInfoVo(), new ProductCategoryInfoVo());

        when(biddingDocumentClarificationDomainService.queryProductCategory(projectId,true)).thenReturn(expectedList);

        List<ProductCategoryInfoVo> result = commandService.queryProductCategory(projectId,true);
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(biddingDocumentClarificationDomainService, times(1)).queryProductCategory(projectId,true);
    }
}

/* Ended by AICoder, pid:5af48s93f8laa3314ef00b41c115f82776f1ce39 */
