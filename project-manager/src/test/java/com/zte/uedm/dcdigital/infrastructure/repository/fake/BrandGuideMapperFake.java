/* Started by AICoder, pid:q708ct1c3fz9e8d14bec0b1231b443299b968d7e */
package com.zte.uedm.dcdigital.infrastructure.repository.fake;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BrandGuideMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BrandGuidePo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProductVo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class BrandGuideMapperFake implements BrandGuideMapper {
    @Override
    public List<BrandGuideProductVo> queryByTimeFrame(String startTime, String endTime) {
        List<BrandGuideProductVo> queryList = new ArrayList<>();
        BrandGuideProductVo vo1 = new BrandGuideProductVo();
        vo1.setId("1");
        vo1.setId("category1");
        BrandGuideProductVo vo2 = new BrandGuideProductVo();
        vo2.setId("2");
        vo2.setId("category2");
        queryList.add(vo1);
        queryList.add(vo2);
        return queryList;
    }

    @Override
    public int insert(BrandGuidePo entity) {
        return 0;
    }

    @Override
    public int deleteById(Serializable id) {
        return 0;
    }

    @Override
    public int deleteById(BrandGuidePo entity) {
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> columnMap) {
        return 0;
    }

    @Override
    public int delete(Wrapper<BrandGuidePo> queryWrapper) {
        return 0;
    }

    @Override
    public int deleteBatchIds(Collection<?> idList) {
        return 0;
    }

    @Override
    public int updateById(BrandGuidePo entity) {
        return 0;
    }

    @Override
    public int update(BrandGuidePo entity, Wrapper<BrandGuidePo> updateWrapper) {
        return 0;
    }

    @Override
    public BrandGuidePo selectById(Serializable id) {
        return null;
    }

    @Override
    public List<BrandGuidePo> selectBatchIds(Collection<? extends Serializable> idList) {
        return null;
    }

    @Override
    public List<BrandGuidePo> selectByMap(Map<String, Object> columnMap) {
        return null;
    }

    @Override
    public Long selectCount(Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }

    @Override
    public List<BrandGuidePo> selectList(Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }

    @Override
    public List<Map<String, Object>> selectMaps(Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }

    @Override
    public List<Object> selectObjs(Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<BrandGuidePo>> P selectPage(P page, Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }

    @Override
    public <P extends IPage<Map<String, Object>>> P selectMapsPage(P page, Wrapper<BrandGuidePo> queryWrapper) {
        return null;
    }
}
/* Ended by AICoder, pid:q708ct1c3fz9e8d14bec0b1231b443299b968d7e */