package com.zte.uedm.dcdigital.domain.common.valueobj;

import com.zte.uedm.dcdigital.common.util.PojoTestUtil;
import com.zte.uedm.dcdigital.domain.aggregate.model.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectBillMaterialPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssociationDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectScoreChartDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.TimeRangeDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BeanTest {

    @Test
    void test() {
        Exception ex = null;
        try {
            PojoTestUtil.TestForPojo(ChartDataPoint.class);
            PojoTestUtil.TestForPojo(ProjectScoreChartDto.class);
            PojoTestUtil.TestForPojo(TimeRangeDto.class);
            PojoTestUtil.TestForPojo(ProductScoreLineChartDataVo.class);
            PojoTestUtil.TestForPojo(ProductScoreLineChartVo.class);
            PojoTestUtil.TestForPojo(ProjectAverageScoreVo.class);
            PojoTestUtil.TestForPojo(ProjectProductsVo.class);
            PojoTestUtil.TestForPojo(ProjectScoreResultVo.class);
            PojoTestUtil.TestForPojo(ProjectScoreVo.class);
            PojoTestUtil.TestForPojo(ProjectStageScoreEntity.class);
            PojoTestUtil.TestForPojo(BiddingDocumentClarificationExportEntity.class);
            PojoTestUtil.TestForPojo(BiddingDocumentClarificationObj.class);
            PojoTestUtil.TestForPojo(LaunchBiddingEntity.class);
            PojoTestUtil.TestForPojo(BaseEntity.class);
            PojoTestUtil.TestForPojo(BillOfQuantityVo.class);
            PojoTestUtil.TestForPojo(TemplateImportVo.class);
            PojoTestUtil.TestForPojo(AssociatedMaterialQueryDto.class);
            PojoTestUtil.TestForPojo(AssociatedMaterialVo.class);
            PojoTestUtil.TestForPojo(MaterialAssociationDto.class);
            PojoTestUtil.TestForPojo(ProjectBillMaterialEntity.class);
            PojoTestUtil.TestForPojo(ProjectBillMaterialPo.class);
            PojoTestUtil.TestForPojo(BillAssociationCountEntity.class);
        } catch (Exception e) {
            ex = e;
            Assertions.assertEquals(null, ex);
        }
    }

}