/* Started by AICoder, pid:mc50fgc239u212e14e2009e2c1d52b28cd617688 */
package com.zte.uedm.dcdigital.interfaces.web.ft;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.repository.BrandGuideRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.fake.BrandGuideMapperFake;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BrandGuideMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.persistence.BrandGuideRepositoryImpl;
import com.zte.uedm.dcdigital.interfaces.web.controller.BrandGuideBillboardUportalController;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideBillboardQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProductVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;


public class BrandGuideBillboardUportalControllerFTest {

    @InjectMocks
    private BrandGuideBillboardUportalController controller;

    @Resource
    private BrandGuideMapper brandGuideMapper = new BrandGuideMapperFake();

    @Resource
    private BrandGuideRepository brandGuideRepository = new BrandGuideRepositoryImpl();

    @Mock
    private ProductService productService;

    @Before
    public void setUp() throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        FtMockitoAnnotations.initMocks(this);
    }

    @Test
    public void EPM_39876_given_有效的startTime_endTime为空_when_查询品牌引导产品_then_参数校验失败_提示参数无效(){
        //
        //given
        //
        String startTime = "2025-02-01 00:00:00";
        String endTime = "";
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        billboardQueryDto.setStartTime(startTime);
        billboardQueryDto.setEndTime(endTime);
        //when
        try {
            controller.queryWithTimeRange(billboardQueryDto);
        } catch (BusinessException e) {
            //then
            assertEquals("invalid parameter", e.getMessage());
        }
    }

    @Test
    public void EPM_39874_given_错误格式的startTime和endTime_when_查询品牌引导产品_then_参数解析失败_提示参数无效(){
        //
        //given
        //
        String startTime = "2025-02-01";
        String endTime = "2025-02-01";
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        billboardQueryDto.setStartTime(startTime);
        billboardQueryDto.setEndTime(endTime);
        //when
        try {
            controller.queryWithTimeRange(billboardQueryDto);
        } catch (BusinessException e) {
            //then
            assertEquals("Time format error", e.getMessage());
        }

    }

    @Test
    public void EPM_39877_given_有效的startTime和endTime_开始时间晚于结束时间_when_查询品牌引导产品_then_参数校验失败_提示参数无效(){
        //
        //given
        //
        String startTime = "2025-02-01 00:00:00";
        String endTime = "2025-01-01 00:00:00";
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        billboardQueryDto.setStartTime(startTime);
        billboardQueryDto.setEndTime(endTime);

        try {
            controller.queryWithTimeRange(billboardQueryDto);
        } catch (BusinessException e) {
            //then
            assertEquals("The start time cannot be later than the end time", e.getMessage());
        }

    }


    @Test
    public void EPM_39882_given_有效的startTime和endTime_时间范围超过一年_when_查询品牌引导产品_then_参数校验失败_提示参数无效(){
        //
        //given
        //

        String startTime = "2025-02-01 00:00:00";
        String endTime = "2026-03-01 00:00:00";
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        billboardQueryDto.setEndTime(endTime);
        billboardQueryDto.setStartTime(startTime);
        //when
        try {
            controller.queryWithTimeRange(billboardQueryDto);
        } catch (BusinessException e) {
            //then
            assertEquals("Time range error", e.getMessage());
        }

    }

    @Test
    public void EPM_39872_given_有效的startTime和endTime_时间校验通过_when_查询品牌引导产品_then_查询时间范围内的数据_返回查询数据(){
        //
        //given
        //
        List<ProductCategoryInfoVo> productCategoryInfoVos = new ArrayList<>();
        ProductCategoryInfoVo info1 = new ProductCategoryInfoVo();
        info1.setId("category1");
        info1.setPathName("Path1");
        productCategoryInfoVos.add(info1);
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        billboardQueryDto.setEndTime("2025-02-02 00:00:00");
        billboardQueryDto.setStartTime("2025-02-01 00:00:00");
        //when
        when(productService.selectProductCategoryList(anyList())).thenReturn(productCategoryInfoVos);
        //then
        //
        BaseResult<List<BrandGuideProductVo>> result = controller.queryWithTimeRange(billboardQueryDto);
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(2, result.getData().size());

    }

    @Test
    public void EPM_39871_given_startTime和endTime均为空_查询默认时间范围_when_查询品牌引导产品_then_查询默认范围的数据_返回查询数据(){
        //
        //given
        //
        List<ProductCategoryInfoVo> productCategoryInfoVos = new ArrayList<>();
        ProductCategoryInfoVo info1 = new ProductCategoryInfoVo();
        info1.setId("category1");
        info1.setPathName("Path1");
        productCategoryInfoVos.add(info1);
        BrandGuideBillboardQueryDto billboardQueryDto = new BrandGuideBillboardQueryDto();
        //when
        when(productService.selectProductCategoryList(anyList())).thenReturn(productCategoryInfoVos);
        //then
        //
        BaseResult<List<BrandGuideProductVo>> result = controller.queryWithTimeRange(billboardQueryDto);
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(2, result.getData().size());
    }
}
/* Ended by AICoder, pid:mc50fgc239u212e14e2009e2c1d52b28cd617688 */