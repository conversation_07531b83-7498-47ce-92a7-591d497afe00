package com.zte.uedm.dcdigital.application.project.impl;

/* Started by AICoder, pid:gca85j7bdaj4be714be90898608baa500e88b3ad */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.BillAssociationCountEntity;
import com.zte.uedm.dcdigital.domain.service.BillOfQuantityDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectBillMaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BillOfQuantityQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BillOfQuantityVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BillOfQuantityQueryServiceImplTest {

    @Mock
    private BillOfQuantityDomainService billOfQuantityDomainService;

    @InjectMocks
    private BillOfQuantityQueryServiceImpl billOfQuantityQueryService;
    @Mock
    private ProjectBillMaterialDomainService projectBillMaterialDomainService;

    private BillOfQuantityQueryDto queryDto;
    private PageVO<BillOfQuantityVo> pageVO;
    private BillOfQuantityVo billOfQuantityVo;

    @Before
    public void setUp() {
        queryDto = new BillOfQuantityQueryDto();
        pageVO = new PageVO<>();
        billOfQuantityVo = new BillOfQuantityVo();
    }

    @Test
    public void testQueryByCondition() {
        List<BillOfQuantityVo> list = new ArrayList<>();
        BillOfQuantityVo vo = new BillOfQuantityVo();
        vo.setId("bill-id");
        list.add(vo);
        PageVO<BillOfQuantityVo> page = new PageVO<>(1, list);

        when(billOfQuantityDomainService.queryByCondition(any(BillOfQuantityQueryDto.class))).thenReturn(page);
        Mockito.when(projectBillMaterialDomainService.countAssociatedMaterialNum(Mockito.any())).thenReturn(buildBillAssociationCountEntityList());


        PageVO<BillOfQuantityVo> result = billOfQuantityQueryService.queryByCondition(queryDto);
        Assertions.assertEquals(5, result.getList().get(0).getAssociatedMaterialNum());
        verify(projectBillMaterialDomainService, times(1)).countAssociatedMaterialNum(Mockito.any());
    }

    private List<BillAssociationCountEntity> buildBillAssociationCountEntityList() {
        List<BillAssociationCountEntity> list = new ArrayList<>();
        BillAssociationCountEntity entity = new BillAssociationCountEntity();
        entity.setBillId("bill-id");
        entity.setAssociatedNum(5);
        list.add(entity);
        return list;
    }

    @Test
    public void testQueryById() {
        String id = "testId";
        when(billOfQuantityDomainService.queryById(id)).thenReturn(billOfQuantityVo);

        BillOfQuantityVo result = billOfQuantityQueryService.queryById(id);

        assertEquals(billOfQuantityVo, result);
        verify(billOfQuantityDomainService, times(1)).queryById(id);
    }
}
/* Ended by AICoder, pid:gca85j7bdaj4be714be90898608baa500e88b3ad */