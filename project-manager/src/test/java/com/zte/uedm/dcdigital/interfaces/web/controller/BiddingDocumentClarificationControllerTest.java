package com.zte.uedm.dcdigital.interfaces.web.controller;

/* Started by AICoder, pid:pab4dj5cc842b00141620a8c4104e63d91012f2e */
import com.zte.uedm.dcdigital.application.biddingdocument.executor.BiddingDocumentClarificationCommandService;
import com.zte.uedm.dcdigital.application.biddingdocument.executor.BiddingDocumentClarificationQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.QueryProductCategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BiddingDocumentClarificationControllerTest {

    @Mock
    private BiddingDocumentClarificationQueryService queryService;

    @Mock
    private BiddingDocumentClarificationCommandService commandService;

    @InjectMocks
    private BiddingDocumentClarificationController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryListByCondition() {
        BiddingDocumentClarificationQueryDto queryDto = new BiddingDocumentClarificationQueryDto();
        PageVO<BiddingDocumentClarificationVo> expectedPageVO = new PageVO<>();
        when(queryService.queryListByCondition(any(BiddingDocumentClarificationQueryDto.class)))
                .thenReturn(expectedPageVO);

        BaseResult<PageVO<BiddingDocumentClarificationVo>> result = controller.queryListByCondition(queryDto);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        assertEquals(expectedPageVO, result.getData());
        verify(queryService, times(1)).queryListByCondition(queryDto);
    }

    @Test
    void testQueryDetailById() {
        String id = "1";
        BiddingDocumentClarificationDetailVo expectedDetailVo = new BiddingDocumentClarificationDetailVo();
        when(queryService.queryDetailById(id)).thenReturn(expectedDetailVo);

        BaseResult<BiddingDocumentClarificationDetailVo> result = controller.queryDetailById(id);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        assertEquals(expectedDetailVo, result.getData());
        verify(queryService, times(1)).queryDetailById(id);
    }

    @Test
    void testAdd() {
        BiddingDocumentClarificationAddDto addDto = new BiddingDocumentClarificationAddDto();

        BaseResult<Void> result = controller.add(addDto);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        verify(commandService, times(1)).add(addDto);
    }

    @Test
    void testEdit() {
        BiddingDocumentClarificationEditDto editDto = new BiddingDocumentClarificationEditDto();

        BaseResult<Void> result = controller.edit(editDto);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        verify(commandService, times(1)).edit(editDto);
    }

    @Test
    void testDeleteById() {
        String id = "1";

        BaseResult<Void> result = controller.deleteById(id);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        verify(commandService, times(1)).deleteById(id);
    }

    @Test
    void testExport() throws Exception {
        HttpServletResponse response = mock(HttpServletResponse.class);
        BiddingDocumentClarificationQueryDto dto = new BiddingDocumentClarificationQueryDto();
        dto.setProjectId("1");
        doNothing().when(commandService).export(dto, response);

        assertDoesNotThrow(() -> controller.export(dto, response));
        verify(commandService, times(1)).export(dto, response);
    }

    @Test
    void testQueryProductCategory() {
        String projectId = "1";
        QueryProductCategoryDto dto = new QueryProductCategoryDto();
        dto.setProjectId(projectId);
        dto.setFlag(true);
        List<ProductCategoryInfoVo> expectedList = Collections.singletonList(new ProductCategoryInfoVo());
        when(commandService.queryProductCategory(projectId,true)).thenReturn(expectedList);

        BaseResult<List<ProductCategoryInfoVo>> result = controller.queryProductCategory(dto);

        assertNotNull(result);
        assertEquals(BaseResult.success().getCode(), result.getCode());
        assertEquals(expectedList, result.getData());
        verify(commandService, times(1)).queryProductCategory(projectId,true);
    }
}

/* Ended by AICoder, pid:pab4dj5cc842b00141620a8c4104e63d91012f2e */
