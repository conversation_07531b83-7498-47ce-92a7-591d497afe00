package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;


@Getter
@Setter
@ToString
public class LegacyIssuesEditDto extends LegacyIssuesAddDto{

    @LogMark(range = {OperationMethodEnum.UPDATE})
    @NotBlank(message = "The Legacy Issues ID cannot be empty")
    private String id;
}
