package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
/* Started by AICoder, pid:550eeh0007xd5b2144190bcac00c9a2d5491b128 */
public class ProjectAreaQueryDto {
    /**
     * Name of the area.
     */
    private String areaName;

    /**
     * Parent ID of the area.
     */
    private String parentId;

    /**
     * Page number for pagination.
     */
    private Integer pageNum;

    /**
     * Page size for pagination.
     */
    private Integer pageSize;
}
/* Ended by AICoder, pid:550eeh0007xd5b2144190bcac00c9a2d5491b128 */
