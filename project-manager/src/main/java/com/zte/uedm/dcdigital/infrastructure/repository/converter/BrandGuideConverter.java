package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.infrastructure.repository.po.BrandGuideDetailPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BrandGuidePo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProjectPhaseVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
@Mapper
public interface BrandGuideConverter {

    BrandGuideConverter INSTANCE = Mappers.getMapper(BrandGuideConverter.class);

    @Mappings({
            @Mapping(source = "id", target = "brandGuideId"),
            @Mapping(source = "stage", target = "stage"),
            @Mapping(source = "recordUser", target = "recordUser"),
            @Mapping(source = "recordTime", target = "recordTime"),
            @Mapping(source = "version", target = "version"),
            @Mapping(source = "projectId", target = "projectId"),
    })
    BrandGuideProjectPhaseVo brandGuidePoToBrandGuideProjectPhaseVo(BrandGuidePo guidePo);
    @Mappings({
            @Mapping(source = "id", target = "brandGuideDetailId"),
            @Mapping(source = "brand", target = "guideBrand"),
            @Mapping(source = "brandId", target = "guideBrandId"),
            @Mapping(source = "techSpec", target = "techSpec"),
    })
    BrandGuideDetailVo brandGuideDetailPoToBrandGuideDetailVo(BrandGuideDetailPo brandGuideDetailPo);
}
