package com.zte.uedm.dcdigital.domain.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum SupplyTypeEnums {

    PARTY_A_SUPPLY("1", "{\"zh-CN\":\"甲供\",\"en-US\":\"Party A Supply\"}"),
    ENGINEERING_SUPPLY("2", "{\"zh-CN\":\"工程\",\"en-US\":\"Engineering Supply\"}"),
    PRODUCTION_LINE_SUPPLY("3", "{\"zh-CN\":\"产线\",\"en-US\":\"Production Line Supply\"}"),
    ;
    private String code;
    private String name;
    SupplyTypeEnums(String code,String name) {
        this.code = code;
        this.name = name;
    }

    // 使用静态映射来快速查找枚举值
    public static final Map<String, SupplyTypeEnums> SUPPLY_TYPE_ENUMS_MAP = new HashMap<>();

    static {
        for (SupplyTypeEnums statusEnums : values()) {
            SUPPLY_TYPE_ENUMS_MAP.put(statusEnums.code, statusEnums);
        }
    }

    /***
     * EXCEL文件导入的转换映射，将Excel中的中文名称映射到对应的采购模式枚举。
     */
    public static final Map<String, SupplyTypeEnums> EXCEL_SUPPLY_MAP = new HashMap<>();

    static {
        // 初始化映射关系
        EXCEL_SUPPLY_MAP.put("甲供", SupplyTypeEnums.PARTY_A_SUPPLY);
        EXCEL_SUPPLY_MAP.put("工程", SupplyTypeEnums.ENGINEERING_SUPPLY);
        EXCEL_SUPPLY_MAP.put("产线", SupplyTypeEnums.PRODUCTION_LINE_SUPPLY);
    }
    public static SupplyTypeEnums getByCnName(String name) {
        return EXCEL_SUPPLY_MAP.get(name);
    }
    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static SupplyTypeEnums getSupplyTypeEnums(String code) {
        return SUPPLY_TYPE_ENUMS_MAP.get(code);
    }
    public static boolean isNotExist(String code) {
        return !SUPPLY_TYPE_ENUMS_MAP.containsKey(code);
    }
    /* Started by AICoder, pid: */
    /**
     * 根据类型和语言环境获取name信息。
     *
     * @param code 流程注释的唯一标识符。
     * @return 特定语言环境下的备注信息。
     */
    public static String getNameByCode(String code) {
        for (SupplyTypeEnums commentEnum : values()) {
            if (commentEnum.getCode().equals(code)) {
                return commentEnum.getName();
            }
        }
        throw new IllegalArgumentException("No matching SupplyTypeEnums for code: " + code);
    }

    /* Ended by AICoder, pid: */
}
