/* Started by AICoder, pid:4555bi589f020f914155087d70338b54fe7067e8 */
package com.zte.uedm.dcdigital.domain.aggregate.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DeepenDesignFilesRelationEntity {
    /**
     * 流程flowId(关联文件id)
     */
    private String flowId;

    /**
     * 项目id
     */
    private String itemId;

    /**
     * 产品小类id
     */
    private String productCategoryId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 提交人
     */
    private String submitUser;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 验证结果: 0-未上传附件,1-已上传附件
     */
    private String approvalResult;
}

/* Ended by AICoder, pid:4555bi589f020f914155087d70338b54fe7067e8 */