package com.zte.uedm.dcdigital.application.project;

/* Started by AICoder, pid:ae2998680bme88514ebb0b56e047cc52c558395f */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.project.BrandGuidanceVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectProductSupportsVo;
import com.zte.uedm.dcdigital.common.bean.project.ShortBrandUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;

import java.util.List;
import java.util.Set;

/**
 * 项目查询服务接口，定义了与项目相关的查询方法。
 */
public interface ProjectQueryService {

    /**
     * 获取所有项目阶段列表。
     *
     * @return 包含所有项目阶段的列表。
     */
    List<StageVo> getProjectStage();

    /**
     * 根据客户名称获取客户列表。
     *
     * @param name 客户名称。
     * @return 包含匹配客户的列表。
     */
    List<CustomerVo> getProjectCustomers(String name);

    /**
     * 根据查询条件获取项目列表。
     *
     * @param queryDto 查询条件。
     * @return 包含匹配项目的分页结果。
     */
    PageVO<ProjectVo> getProjectList(ProjectQueryDto queryDto);

    List<ProjectVo> getAllProject();

    /**
     * 根据项目ID获取项目详情。
     *
     * @param id 项目ID。
     * @return 包含项目详情的对象。
     */
    ProjectDetailVo getProjectDetail(String id);

    /**
     * 根据地区名称获取地区树。
     *
     * @param areaName 地区名称。
     * @return 包含地区树的列表。
     */
    List<AreaTreeVo> getAreaTree(String areaName);

    /**
     * 根据关键字获取地区和项目树。
     *
     * @param keyword 关键字。
     * @return 包含地区和项目树的列表。
     */
    List<AreaProjectTreeVo> getAreaProjectTree(String keyword);

    /* Started by AICoder, pid:mdffdy66f0606201468c0907c0db250f20c7f234 */
    /**
     * 根据项目ID查询项目的详细信息。
     *
     * @param id 项目ID，用于查询具体的项目详情。
     * @return 包含查询结果的 ProjectDetailInfoVo 对象。如果未找到项目，返回的对象将包含空值。
     */
    ProjectDetailInfoVo queryProjectDetail(String id);
    /* Ended by AICoder, pid:mdffdy66f0606201468c0907c0db250f20c7f234 */

    /* Started by AICoder, pid:m83775cf9bn7dbd14f780b84d0bcf40e2a07477a */
    /**
     * 根据指定的ID获取启动招标的信息。
     *
     * @param id 招标记录的唯一标识符，用于查询特定的招标信息。
     * @return LaunchBiddingVo 包含招标详细信息的对象。如果未找到对应的招标记录，则可能返回null或空对象（具体取决于实现）。
     */
    LaunchBiddingVo queryBiddingInformationById(String id);

    List<String> queryProjectIdByAreaId(String id);

    List<BrandGuidanceVo> queryGuidedSubcategory(String projectId);

    List<ProjectProductSupportsVo> queryProjectProductSupport(String projectId, List<String> productCategoryIds);

    /* Ended by AICoder, pid:m83775cf9bn7dbd14f780b84d0bcf40e2a07477a */
}
/* Ended by AICoder, pid:ae2998680bme88514ebb0b56e047cc52c558395f */
