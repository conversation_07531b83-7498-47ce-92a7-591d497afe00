/* Started by AICoder, pid:670daqb62fr28f3146300b6e90870c8cd55827a1 */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.biddingdocument.executor.BiddingDocumentClarificationCommandService;
import com.zte.uedm.dcdigital.application.biddingdocument.executor.BiddingDocumentClarificationQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductTreeVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BiddingDocumentClarificationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingDocumentClarificationPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.QueryProductCategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/uportal/bidding-document-clarification")
@Controller
@Slf4j
public class BiddingDocumentClarificationController {

    @Autowired
    private BiddingDocumentClarificationQueryService queryService;

    @Autowired
    private BiddingDocumentClarificationCommandService commandService;


    @POST
    @Path("/query")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "条件查询标书澄清列表", notes = "条件查询标书澄清列表")
    public BaseResult<PageVO<BiddingDocumentClarificationVo>> queryListByCondition(BiddingDocumentClarificationQueryDto queryDto) {
        PageVO<BiddingDocumentClarificationVo> result = queryService.queryListByCondition(queryDto);
        return BaseResult.success(result);
    }

    @GET
    @Path("/detail")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据ID查询详情", notes = "根据ID查询详情")
    public BaseResult<BiddingDocumentClarificationDetailVo> queryDetailById(@QueryParam("id") String id) {
        BiddingDocumentClarificationDetailVo result = queryService.queryDetailById(id);
        return BaseResult.success(result);
    }

    @POST
    @Path("/add")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增标书澄清", notes = "新增标书澄清")
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",
            operation = "BiddingDocumentClarificationAddDescription", targetClass = BiddingDocumentClarificationAddDto.class)
    public BaseResult<Void> add(@Valid BiddingDocumentClarificationAddDto addDto) {
        commandService.add(addDto);
        return BaseResult.success();
    }

    @POST
    @Path("/edit")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "编辑标书澄清", notes = "编辑标书澄清")
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-project-manager",
            operation = "BiddingDocumentClarificationEditDescription", targetClass = BiddingDocumentClarificationEditDto.class)
    public BaseResult<Void> edit(@Valid BiddingDocumentClarificationEditDto editDto) {
        commandService.edit(editDto);
        return BaseResult.success();
    }

    @POST
    @Path("/delete")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据ID删除", notes = "根据ID删除")
    @DcOperationLog(method = OperationMethodEnum.DELETE, module = "module-project-manager",mapperName = BiddingDocumentClarificationMapper.class,
            operation = "BiddingDocumentClarificationDeleteDescription", targetClass = BiddingDocumentClarificationPo.class)
    public BaseResult<Void> deleteById(@QueryParam("id") String id) {
        commandService.deleteById(id);
        return BaseResult.success();
    }

    @POST
    @Path("/export")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = "根据项目ID导出", notes = "根据项目ID导出")
    public void export(BiddingDocumentClarificationQueryDto dto, @Context HttpServletResponse response) {
        commandService.export(dto, response);
    }

    @POST
    @Path("/query-productCategory")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据项目ID查询产品分类", notes = "根据项目ID查询产品分类")
    public BaseResult<List<ProductCategoryInfoVo>> queryProductCategory(QueryProductCategoryDto dto) {
        List<ProductCategoryInfoVo> result = commandService.queryProductCategory(dto.getProjectId(),dto.isFlag());
        return BaseResult.success(result);
    }

    @POST
    @Path("/query-productCategory-tree")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据项目ID查询产品分类", notes = "根据项目ID查询产品分类")
    public BaseResult<List<ProductTreeVo>> queryProductCategoryTree(QueryProductCategoryDto dto) {
        List<ProductTreeVo> result = commandService.queryProductCategoryTree(dto.getProjectId(),dto.isFlag());
        return BaseResult.success(result);
    }
}

/* Ended by AICoder, pid:670daqb62fr28f3146300b6e90870c8cd55827a1 */