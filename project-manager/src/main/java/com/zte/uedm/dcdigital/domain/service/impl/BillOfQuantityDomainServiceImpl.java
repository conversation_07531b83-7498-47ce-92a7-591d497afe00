/* Started by AICoder, pid:i373bkb65aj0d2b14b540b427359db819c8827f6 */
package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.basis.util.base.http.ConnectModeOptional;
import com.zte.uedm.basis.util.base.http.HttpServletUtil;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import com.zte.uedm.component.kafka.producer.service.KafkaSenderService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalAddDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerVo;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.BillOfQuantityInnerVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.FileDownloadUtil;
import com.zte.uedm.dcdigital.common.util.RpcUtil;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.aggregate.event.BillOfQuantityListener;
import com.zte.uedm.dcdigital.domain.aggregate.model.BillAssociationCountEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.BillOfQuantityEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectBillMaterialEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectBillMaterialRepository;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.InstallationSupervisionNameEnums;
import com.zte.uedm.dcdigital.domain.common.enums.SupplyTypeEnums;
import com.zte.uedm.dcdigital.domain.common.enums.TemplateCheckResultEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.domain.common.valueobj.TemplateObj;
import com.zte.uedm.dcdigital.domain.repository.BillOfQuantityRepository;
import com.zte.uedm.dcdigital.domain.service.BillOfQuantityDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectBillMaterialDomainService;
import com.zte.uedm.dcdigital.domain.strategy.ExcelMergeHandler;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.BillOfQuantityConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectBillMaterialConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BillOfQuantityPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.BillOfQuantityVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ExportBillQuantitiesVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TemplateImportVo;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationResultOptional;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.dto.MsgLogDto;
import com.zte.uedm.dcdigital.sdk.system.rpc.MsgRpc;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BillOfQuantityDomainServiceImpl implements BillOfQuantityDomainService {

    @Autowired
    private BillOfQuantityRepository billOfQuantityRepository;
    @Autowired
    private AuthService authService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProcessService processService;

    @Autowired
    private ProjectBillMaterialRepository projectBillMaterialRepository;

    @Autowired
    private KafkaSenderService kafkaSenderService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private MsgRpc msgRpc;

    @Autowired
    private ProjectBillMaterialDomainService projectBillMaterialDomainService;
    //从第2行开始合并
    private static final int mergeRowIndex=1;
    //需要合并的单元格列
    private static final int[] mergeCols={0,1,2,3,4,5,6,7,8,9,10,11,12};

    /* Started by AICoder, pid:2aab983f9dvaf01145460b6f30d9f51f5bb13d05 */
    /**
     * 检查具有相同名称和特性的记录数。
     *
     * @param id            记录ID，用于排除当前记录
     * @param name          工程量清单名称
     * @param characteristic 特征描述
     * @param ascriptionId  归属项目ID
     */
    private void checkNameCharacteristic(String id, String name, String characteristic, String ascriptionId) {
        billOfQuantityRepository.checkNameCharacteristic(id, name, characteristic, ascriptionId);
    }
    /* Ended by AICoder, pid:2aab983f9dvaf01145460b6f30d9f51f5bb13d05 */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void add(BillOfQuantityAddDto billOfQuantityAddDto) {
        checkNameCharacteristic(null,billOfQuantityAddDto.getName(),billOfQuantityAddDto.getCharacteristic(),
                billOfQuantityAddDto.getAscriptionId());
        BillOfQuantityEntity quantityEntity = BillOfQuantityConvert.INSTANCE.addDtoToEntity(billOfQuantityAddDto);
        String userId = authService.getUserId();
//        if (StringUtils.isNotBlank(billOfQuantityAddDto.getProductSubcategory())) {
//            //创建任务
//            checkAndCreateTask(billOfQuantityAddDto.getAscriptionId(), billOfQuantityAddDto.getProductSubcategory(), userId);
//        }
        String currentTime = DateTimeUtils.getCurrentTime();
        populateAuditFields(quantityEntity, userId, currentTime);
        billOfQuantityRepository.add(quantityEntity);
        //新增工程量清单需要通知关联的需求单的SE
        noticeTheProductSE(billOfQuantityAddDto.getAscriptionId(), billOfQuantityAddDto.getProductSubcategory(),billOfQuantityAddDto.getName());


    }

    /* Started by AICoder, pid:l40253b062m197614801097ba049526b4d158e14 */
// 通知关联的需求单的SE
    private void noticeTheProductSE(String projectId, String productSubcategory, String BillOfQuantityAddName) {
        log.info("noticeTheProductSE params projectId:{},productSubcategory:{},BillOfQuantityAddName:{}",projectId,productSubcategory,BillOfQuantityAddName);
        if (StringUtils.isBlank(productSubcategory)) {
            return;
        }
        //根据商机id+产品小类id判断是否有关联的需求单
        List<DemandManagementInnerVo> demandDataVos = getDemandDataByCondition(projectId, productSubcategory);
        if (CollectionUtils.isEmpty(demandDataVos)) {
            //未找到关联的需求单则不发送通知
            return;
        }
        // 更新需求单的清单条目发生变化时间
        demandDataVos.forEach(item -> {
            DemandManagementInnerDto innerDto = new DemandManagementInnerDto();
            innerDto.setId(item.getId());
            innerDto.setBillQuantitieTime(DateTimeUtils.getCurrentTime());
            productService.updateDemandManagementInfo(innerDto);
        });
        //收集产品小类id
        List<String> productCategoryIds = demandDataVos.stream()
                .map(DemandManagementInnerVo::getProductCategoryId)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                String::valueOf,
                                v -> v,
                                (oldValue, newValue) -> newValue
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        Map<String, ProductCategoryInfoVo> productCategoryInfoMap = productService.selectProductCategoryList(productCategoryIds).stream().collect(Collectors.toMap(ProductCategoryInfoVo::getId, Function.identity()));
        // 根据需求单信息收集市场支持产品se
        Map<String, DemandManagementInnerVo> demandMap = demandDataVos.stream().collect(Collectors.toMap(DemandManagementInnerVo::getId, Function.identity(), (oldValue, newValue) -> newValue));
        log.info("demandMap params ProcessorMap:{}", demandMap);
        String currentTime = DateTimeUtils.getCurrentTime();
        demandMap.forEach((key, value) -> {
            DemandManagementInnerVo demandEntity=demandMap.getOrDefault(key, new DemandManagementInnerVo()); // 安全获取
            String notifyUser = demandEntity.getProcessor();
            MsgLogDto msgLogDto = new MsgLogDto();
            String categoryName=productCategoryInfoMap.getOrDefault(demandEntity.getProductCategoryId(),new ProductCategoryInfoVo()).getPathName();
            msgLogDto.setMsgName(currentTime+" "+categoryName+" 工程量清单发生变化：" + BillOfQuantityAddName);
            msgLogDto.setMsgContent("名称：" + BillOfQuantityAddName);
            msgLogDto.setMsgType(4);
            msgLogDto.setNotifyType(1);
            msgLogDto.setNotifyUsers(notifyUser);
            log.info("msgLogDto:{}", msgLogDto);
            RpcUtil.call(msgRpc.add(msgLogDto));
        });
    }

    // 根据商机id与产品小类id查询需求单数据
    private List<DemandManagementInnerVo> getDemandDataByCondition(String projectId, String productSubcategory) {
        DemandManagementInnerDto innerDto = new DemandManagementInnerDto();
        innerDto.setProjectId(projectId);
        innerDto.setProductCategoryId(productSubcategory);
        return productService.getDemandManagementList(innerDto);
    }

    /**
     * @param productCategoryIds 产品小类id集合
     * @return key为产品小类,value为产品SE
     */
    private Map<String, List<String>> getProductSEsByCategoryIds(List<String> productCategoryIds) {
        // 根据产品小类查询产品SE
        Map<String, List<String>> stringListMap = new HashMap<>();
        for (String productCategoryId : productCategoryIds) {
            List<String> strings = productService.selectProductSeByCategoryId(productCategoryId);
            if (CollectionUtils.isNotEmpty(strings)) {
                stringListMap.put(productCategoryId, strings);
            }
        }
        return stringListMap;
    }

    /* Ended by AICoder, pid:l40253b062m197614801097ba049526b4d158e14 */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void edit(BillOfQuantityEditDto billOfQuantityEditDto) {
        checkNameCharacteristic(billOfQuantityEditDto.getId(),billOfQuantityEditDto.getName(),
                billOfQuantityEditDto.getCharacteristic(),billOfQuantityEditDto.getAscriptionId());
        BillOfQuantityEntity quantityEntity = BillOfQuantityConvert.INSTANCE.editDtoToEntity(billOfQuantityEditDto);
        String id = quantityEntity.getId();
        BillOfQuantityEntity existing = billOfQuantityRepository.queryById(id);
        if (existing == null) {
            //不存在
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        //新增的产品小类创建相应的待办任务
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        populateAuditFields(quantityEntity, userId, currentTime);
        String oldSubcategory = existing.getProductSubcategory();
        String newSubcategory = billOfQuantityEditDto.getProductSubcategory();
        //删除不再涉及的产品小类的待办任务
        if (!StringUtils.equals(oldSubcategory, newSubcategory)) {
            if (StringUtils.isNotBlank(oldSubcategory)) {
                checkAndCancelTask(existing.getAscriptionId(), oldSubcategory, GlobalConstants.BOQ_CHANGE_SUBCATEGORY);
            }
            if (StringUtils.isNotBlank(newSubcategory)) {
                checkAndCreateTask(existing.getAscriptionId(), newSubcategory, userId);
            }
        }
        billOfQuantityRepository.update(quantityEntity);
        //编辑工程量清单需要通知关联的需求单的SE
        noticeTheProductSE(billOfQuantityEditDto.getAscriptionId(), billOfQuantityEditDto.getProductSubcategory(),billOfQuantityEditDto.getName());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void batchAdd(List<BillOfQuantityAddDto> billOfQuantityAddDtoList) {
        List<BillOfQuantityEntity> quantityEntities = BillOfQuantityConvert.INSTANCE.listDtoToEntity(billOfQuantityAddDtoList);
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        Set<String> productSubcategoryIds = new HashSet<>();
        for (BillOfQuantityEntity quantityEntity : quantityEntities) {
            populateAuditFields(quantityEntity, userId, currentTime);
            String productSubcategory = quantityEntity.getProductSubcategory();
            if (StringUtils.isNotBlank(productSubcategory)) {
                productSubcategoryIds.add(productSubcategory);
            }
        }
        //TODO 创建任务
        //String projectId = quantityEntities.get(0).getAscriptionId();
        //Set<String> existSubcategoryIds = getAllProductCategory(projectId);
        //新增的产品小类,去掉已存在的
        //productSubcategoryIds.removeAll(existSubcategoryIds);
        //构建日志记录
        OperationLogBean operationLogBean = buildOperationLogBean(GlobalConstants.BOQ_BATCH_IMPORT, OperationTypeOptional.OPERATION_TYPE_ADD, userId);
        String detail = buildLogDetail(quantityEntities);
        try {
            //createToDoTasks(projectId, new ArrayList<>(productSubcategoryIds), userId);
            for (BillOfQuantityEntity quantityEntity:quantityEntities) {
                BillOfQuantityPo billOfQuantityPo =  billOfQuantityRepository.getByNameCharacteristic(
                        quantityEntity.getName(),quantityEntity.getCharacteristic(),quantityEntity.getAscriptionId());
                log.info("billOfQuantityPo:{}",JSON.toJSONString(billOfQuantityPo));
                if(null==billOfQuantityPo) {
                    billOfQuantityRepository.add(quantityEntity);
                    noticeTheProductSE(quantityEntity.getAscriptionId(), quantityEntity.getProductSubcategory(),quantityEntity.getName());
                }else {
                    quantityEntity.setId(billOfQuantityPo.getId());
                    billOfQuantityRepository.update(quantityEntity);
                    noticeTheProductSE(quantityEntity.getAscriptionId(), quantityEntity.getProductSubcategory(),quantityEntity.getName());
                }
            }
            //billOfQuantityRepository.batchAdd(quantityEntities);
            sendSuccessLogMessage(operationLogBean, detail);
        } catch (Exception e) {
            log.error("Batch import of bill of quantities failed.", e);
            sendFailLogMessage(operationLogBean, e.getMessage(), detail);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void batchUpdate(BillOfQuantityBatchEditDto batchEditDto) {
        List<String> ids = batchEditDto.getIds();
        List<BillOfQuantityEntity> billOfQuantityEntityList = billOfQuantityRepository.queryByIds(ids);
        if (CollectionUtils.isEmpty(billOfQuantityEntityList)) {
            //不存在
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        permissionUtil.checkPermission(billOfQuantityEntityList.get(0).getAscriptionId(), PermissionEnum.PROJECT_BID_DECOMPOSITION_BOQ_EDIT);
        //原配置的产品小类
        //涉及产品小类变化的
        String newProductSub = batchEditDto.getProductSubcategory();
        String userId = authService.getUserId();
        Set<String> productSubcategoryIds = new HashSet<>();
        Set<String> changeIds = new HashSet<>();

        editChangeParameter(billOfQuantityEntityList, batchEditDto, newProductSub, productSubcategoryIds, changeIds, userId);

        String projectId = billOfQuantityEntityList.get(0).getAscriptionId();
        //除变更外的所有产品小类
        BillOfQuantityQueryDto queryDto = new BillOfQuantityQueryDto();
        queryDto.setId(projectId);
        Set<String> otherExist = billOfQuantityRepository.queryByCondition(queryDto).stream()
                .filter(quantityEntity -> !changeIds.contains(quantityEntity.getId()))
                .map(BillOfQuantityVo::getProductSubcategory)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        updateChangeParameterAndSendLog(billOfQuantityEntityList, newProductSub, productSubcategoryIds, otherExist, userId, projectId);
        //编辑工程量清单需要通知关联的需求单的SE
        billOfQuantityEntityList.forEach(item->{
             noticeTheProductSE(item.getAscriptionId(), item.getProductSubcategory(),item.getName());

        });

    }

    private void updateChangeParameterAndSendLog(List<BillOfQuantityEntity> billOfQuantityEntityList, String newProductSub,
                                                 Set<String> productSubcategoryIds, Set<String> otherExist, String userId, String projectId) {
        String detail = buildLogDetail(billOfQuantityEntityList);
        //构建日志记录
        OperationLogBean operationLogBean = buildOperationLogBean(GlobalConstants.BOQ_BATCH_EDIT, OperationTypeOptional.OPERATION_TYPE_UPDATE, userId);
        try {
            if (StringUtils.isNotBlank(newProductSub)) {
                //产品小类是新增的，即项目下没有，第一次新增
                if (!productSubcategoryIds.contains(newProductSub) && !otherExist.contains(newProductSub)) {
                    checkAndCreateTask(projectId, newProductSub, userId);
                } else {
                    //产品小类是修改的，即项目下有，修改
                    productSubcategoryIds.remove(newProductSub);
                }
            }
            //所涉及的产品小类,去掉有交集的
            productSubcategoryIds.removeAll(otherExist);
            //删除任务
            cancelToDoTasks(projectId, new ArrayList<>(productSubcategoryIds), GlobalConstants.BOQ_CHANGE_SUBCATEGORY);
            //更新数据
            billOfQuantityRepository.updateOptionsForBills(billOfQuantityEntityList);
            //上送日志
            sendSuccessLogMessage(operationLogBean, detail);
        } catch (Exception e) {
            log.error("Batch update of bill of quantities failed.", e);
            sendFailLogMessage(operationLogBean, e.getMessage(), detail);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }

    private void editChangeParameter(List<BillOfQuantityEntity> billOfQuantityEntityList, BillOfQuantityBatchEditDto batchEditDto, String newProductSub, Set<String> productSubcategoryIds, Set<String> changeIds, String userId) {
        String currentTime = DateTimeUtils.getCurrentTime();
        boolean changeSupply = StringUtils.isNotBlank(batchEditDto.getSupply());
        boolean changeOneTimeUnloading = StringUtils.isNotBlank(batchEditDto.getOneTimeUnloading());
        boolean changeEquipmentPositioning = StringUtils.isNotBlank(batchEditDto.getEquipmentPositioning());
        boolean changeInstallationSupervision = StringUtils.isNotBlank(batchEditDto.getInstallationSupervision());
        boolean changeCommissioning = StringUtils.isNotBlank(batchEditDto.getCommissioning());
        boolean changePatrolInspection = StringUtils.isNotBlank(batchEditDto.getPatrolInspection());
        // false代表没有变化， true 代表有变化
        for (BillOfQuantityEntity quantityEntity : billOfQuantityEntityList) {
            if (changeSupply) {
                quantityEntity.setSupply(batchEditDto.getSupply());
                String productSubcategory = quantityEntity.getProductSubcategory();
                if (StringUtils.isNotBlank(productSubcategory)) {
                    productSubcategoryIds.add(productSubcategory);
                    changeIds.add(quantityEntity.getId());
                }
                quantityEntity.setProductSubcategory(newProductSub);
            }
            //有变化才更新
            if (changeOneTimeUnloading) {
                quantityEntity.setOneTimeUnloading(batchEditDto.getOneTimeUnloading());
            }
            if (changeEquipmentPositioning) {
                quantityEntity.setEquipmentPositioning(batchEditDto.getEquipmentPositioning());
            }
            if (changeInstallationSupervision) {
                quantityEntity.setInstallationSupervision(batchEditDto.getInstallationSupervision());
            }
            if (changeCommissioning) {
                quantityEntity.setCommissioning(batchEditDto.getCommissioning());
            }
            if (changePatrolInspection) {
                quantityEntity.setPatrolInspection(batchEditDto.getPatrolInspection());
            }
            quantityEntity.setUpdateBy(userId);
            quantityEntity.setUpdateTime(currentTime);
        }
    }


    private String buildLogDetail(List<BillOfQuantityEntity> quantityEntities) {
        try {
            JsonUtils jsonUtils = JsonUtils.getInstance();
            return jsonUtils.objectToJson(quantityEntities);
        } catch (UedmException e) {
            log.error("Data conversion failed.", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void deleteById(String id) {
        BillOfQuantityEntity billOfQuantity = billOfQuantityRepository.queryById(id);
        if (billOfQuantity != null) {
            permissionUtil.checkPermission(billOfQuantity.getAscriptionId(), PermissionEnum.PROJECT_BID_DECOMPOSITION_BOQ_DELETE);
            String productSubcategory = billOfQuantity.getProductSubcategory();
            //TODO 更新任务
            if (StringUtils.isNotBlank(productSubcategory)) {
                checkAndCancelTask(billOfQuantity.getAscriptionId(), productSubcategory, GlobalConstants.BOQ_DELETE_SUBCATEGORY);
            }
            //删除数据
            billOfQuantityRepository.deleteById(id);
            projectBillMaterialRepository.deleteByBillId(id);
            //删除工程量清单需要通知关联的需求单的SE
            noticeTheProductSE(billOfQuantity.getAscriptionId(), billOfQuantity.getProductSubcategory(),billOfQuantity.getName());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<String> ids) {
        List<BillOfQuantityEntity> billOfQuantityEntityList = billOfQuantityRepository.queryByIds(ids);
        if (CollectionUtils.isNotEmpty(billOfQuantityEntityList)) {
            //同一个项目下
            String projectId = billOfQuantityEntityList.get(0).getAscriptionId();
            permissionUtil.checkPermission(projectId, PermissionEnum.PROJECT_BID_DECOMPOSITION_BOQ_DELETE);
            //项目下已存在的所有产品小类(排除要删除的清单)
            Set<String> existSubcategoryIds = getExcludeIdProductCategory(projectId, new HashSet<>(ids));
            //删除的产品小类
            Set<String> oldProductSubList = billOfQuantityEntityList.stream()
                    .map(BillOfQuantityEntity::getProductSubcategory)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            //构建日志记录
            String userId = authService.getUserId();
            OperationLogBean operationLogBean = buildOperationLogBean(GlobalConstants.BOQ_BATCH_DELETE, OperationTypeOptional.OPERATION_TYPE_DEL, userId);
            String detail = buildLogDetail(billOfQuantityEntityList);
            try {
                //TODO 更新任务
                billOfQuantityRepository.deleteByIds(ids);
                //清除关联物料
                projectBillMaterialRepository.deleteByBillIds(ids);
                //清除重复的
                oldProductSubList.removeAll(existSubcategoryIds);
                cancelToDoTasks(projectId, new ArrayList<>(oldProductSubList), GlobalConstants.BOQ_DELETE_SUBCATEGORY);
                sendSuccessLogMessage(operationLogBean, detail);
            } catch (Exception e) {
                log.error("Batch delete of bill of quantities failed.", e);
                sendFailLogMessage(operationLogBean, e.getMessage(), detail);
                throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
            }
            billOfQuantityEntityList.forEach(item->{
                //删除工程量清单需要通知关联的需求单的SE
                noticeTheProductSE(item.getAscriptionId(), item.getProductSubcategory(),item.getName());
            });
        }
    }

    @Override
    public PageVO<BillOfQuantityVo> queryByCondition(BillOfQuantityQueryDto queryDto) {
        Page<BillOfQuantityVo> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        if(StringUtils.isNotBlank(queryDto.getProductSubcategory())||!queryDto.getProductSubcategory().equals("null")){
           List<String> productSubcategoryList  =  productService.selectByCategoryId(queryDto.getProductSubcategory());
           queryDto.setProductSubcategoryList(productSubcategoryList);
        }
        List<BillOfQuantityVo> voList = billOfQuantityRepository.queryByCondition(queryDto);
        if (CollectionUtils.isNotEmpty(voList)) {
            //TODO  参数补充，枚举转换
            List<String> collect = voList.stream().map(BillOfQuantityVo::getProductSubcategory).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<ProductCategoryInfoVo> productCategoryInfoVos = productService.selectProductCategoryList(collect);
            Map<String, ProductCategoryInfoVo> productCategoryInfoVoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productCategoryInfoVos)) {
                productCategoryInfoVoMap = productCategoryInfoVos.stream().collect(Collectors.toMap(ProductCategoryInfoVo::getId, Function.identity()));
            }

            Map<String, ProductCategoryInfoVo> finalProductCategoryInfoVoMap = productCategoryInfoVoMap;
            voList.forEach(p -> {
                ProductCategoryInfoVo productCategoryInfoVo = finalProductCategoryInfoVoMap.get(p.getProductSubcategory());
                conversionAndSupplement(p, productCategoryInfoVo);
            });
        }

        return new PageVO<>(page.getTotal(), voList);
    }


    /* Started by AICoder, pid:fcef1d7e97zf1d014a250876f07fd5230bb2b615 */
    @Override
    public PageVO<BillOfQuantityVo> queryByConditionFilter(BillOfQuantityQueryDto queryDto) {
        Page<BillOfQuantityVo> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<BillOfQuantityVo> voList = billOfQuantityRepository.queryByConditionFilter(queryDto);
        if (CollectionUtils.isNotEmpty(voList)) {
            // TODO 参数补充，枚举转换
            List<String> collect = voList.stream().map(BillOfQuantityVo::getProductSubcategory).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<ProductCategoryInfoVo> productCategoryInfoVos = productService.selectProductCategoryList(collect);
            Map<String, ProductCategoryInfoVo> productCategoryInfoVoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productCategoryInfoVos)) {
                productCategoryInfoVoMap = productCategoryInfoVos.stream().collect(Collectors.toMap(ProductCategoryInfoVo::getId, Function.identity()));
            }

            Map<String, ProductCategoryInfoVo> finalProductCategoryInfoVoMap = productCategoryInfoVoMap;
            voList.forEach(p -> {
                ProductCategoryInfoVo productCategoryInfoVo = finalProductCategoryInfoVoMap.get(p.getProductSubcategory());
                conversionAndSupplement(p, productCategoryInfoVo);
            });
        }

        return new PageVO<>(page.getTotal(), voList);
    }

    /* Ended by AICoder, pid:fcef1d7e97zf1d014a250876f07fd5230bb2b615 */

    private void conversionAndSupplement(BillOfQuantityVo vo, ProductCategoryInfoVo productCategoryInfoVo) {
        if (productCategoryInfoVo != null) {
            vo.setPathName(productCategoryInfoVo.getPathName());
        }
        //TODO  产品小类id，补充名称路径
        SupplyTypeEnums supplyTypeEnums = SupplyTypeEnums.getSupplyTypeEnums(vo.getSupply());
        if (supplyTypeEnums != null) {
            vo.setSupplyName(I18nUtil.getI18nFromString(supplyTypeEnums.getName()));
        }
        SupplyTypeEnums oneTime = SupplyTypeEnums.getSupplyTypeEnums(vo.getOneTimeUnloading());
        if (oneTime != null) {
            vo.setOneTimeUnloadingName(I18nUtil.getI18nFromString(oneTime.getName()));
        }
        SupplyTypeEnums equipment = SupplyTypeEnums.getSupplyTypeEnums(vo.getEquipmentPositioning());
        if (equipment != null) {
            vo.setEquipmentPositioningName(I18nUtil.getI18nFromString(equipment.getName()));
        }
        InstallationSupervisionNameEnums install = InstallationSupervisionNameEnums.getInstallationSupervisionNameEnums(vo.getInstallationSupervision());
        if (install != null) {
            vo.setInstallationSupervisionName(I18nUtil.getI18nFromString(install.getName()));
        }
        SupplyTypeEnums commissioning = SupplyTypeEnums.getSupplyTypeEnums(vo.getCommissioning());
        if (commissioning != null) {
            vo.setCommissioningName(I18nUtil.getI18nFromString(commissioning.getName()));
        }
        SupplyTypeEnums patrolInspection = SupplyTypeEnums.getSupplyTypeEnums(vo.getPatrolInspection());
        if (patrolInspection != null) {
            vo.setPatrolInspectionName(I18nUtil.getI18nFromString(patrolInspection.getName()));
        }
    }

    @Override
    public BillOfQuantityVo queryById(String id) {
        BillOfQuantityEntity quantityEntity = billOfQuantityRepository.queryById(id);
        BillOfQuantityVo billOfQuantityVo = BillOfQuantityConvert.INSTANCE.entityToVo(quantityEntity);
        if (billOfQuantityVo == null) {
            return null;
        }
        List<ProductCategoryInfoVo> productCategoryInfoVos = productService.selectProductCategoryList(Collections.singletonList(billOfQuantityVo.getProductSubcategory()));
        ProductCategoryInfoVo productCategoryInfoVo = null;
        if (CollectionUtils.isNotEmpty(productCategoryInfoVos)) {
            productCategoryInfoVo = productCategoryInfoVos.get(0);
        }
        conversionAndSupplement(billOfQuantityVo, productCategoryInfoVo);
        return billOfQuantityVo;
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        try {
            // 定义模板文件路径
            String configFilepath = GlobalConstants.TEMPLATE_PATH + File.separator + GlobalConstants.TEMPLATE_FILE_NAME;
            // 如果是类路径资源，使用ClassPathResource获取路径
            ClassPathResource resource = new ClassPathResource(configFilepath);
            String filePath = resource.getFile().getAbsolutePath();
            // 下载文件
            FileDownloadUtil.downloadFile(filePath, response, GlobalConstants.TEMPLATE_FILE_NAME);
        } catch (Exception e) {
            log.error("Error exporting template:", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    @Override
    public List<TemplateImportVo> importTemplate(FormDataMultiPart file) {
        FormDataBodyPart part = file.getField("file");
        if (part == null) {
            throw new BusinessException(StatusCode.FAILED);
        }
        BillOfQuantityListener materialReadListener = new BillOfQuantityListener();
        try (InputStream inputStream = part.getValueAs(InputStream.class)) {
            EasyExcel.read(inputStream, materialReadListener)
                    .head(TemplateObj.class)
                    .charset(StandardCharsets.UTF_8)
                    .sheet()
                    .sheetName(GlobalConstants.BOQ_SHEET_NAME)
                    .doRead();
        } catch (Exception e) {
            log.error("Reading Excel file fail", e);
            throw new BusinessException(ProjectStatusCode.READING_EXCEL_FAIL);
        }
        return getAllCheckData(materialReadListener);
    }
    /* Started by AICoder, pid:o97a8vab969a76d140240aeff1b5c91651d97b14 */

    /* Started by AICoder, pid:x528eddf79l995b1485f0bc331e4d53395996179 */
    @Override
    public void exportBillOfQuantity(BillOfQuantityExportDto exportDto, HttpServletResponse response) {
        // 设置响应参数
        setResponseHeader(response, "export-bill-quantities.xlsx_");

        // 整理需要导出的数据
        List<ExportBillQuantitiesVo> exportBillQuantitiesVoList = getExportBillQuantitiesVoLists(exportDto);

        try {
            // 获取模板文件输入流
            InputStream templateStream = new ClassPathResource(GlobalConstants.TEMPLATE_PATH + File.separator + GlobalConstants.EXPORT_BILL_QUANTITIES_FILE_NAME).getInputStream();

            // 使用EasyExcel写入数据到HttpServletResponse
            EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(setStyle())
                    .registerWriteHandler(new ExcelMergeHandler(mergeRowIndex, mergeCols))
                    .withTemplate(templateStream)
                    .sheet()
                    .doWrite(exportBillQuantitiesVoList);
        } catch (IOException e) {
            log.error("export productCoreParamList data is filed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    @Override
    public BillOfQuantityInnerVo queryBillOfQuantitiesById(String id) {
        BillOfQuantityEntity quantityEntity = billOfQuantityRepository.queryById(id);
        if (quantityEntity == null) {
            return null;
        }
        BillOfQuantityInnerVo billOfQuantityInnerVo = new BillOfQuantityInnerVo();
        billOfQuantityInnerVo.setId(quantityEntity.getId());
        billOfQuantityInnerVo.setCharacteristic(quantityEntity.getCharacteristic());
        billOfQuantityInnerVo.setName(quantityEntity.getName());
        return billOfQuantityInnerVo;
    }

    // 设置响应头通用方法
    private void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            String fileNameStr = fileName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
            String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        } catch (Exception e) {
            log.error("set response header error", e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
    }

    // 设置导出excel文件部分内容样式
    private HorizontalCellStyleStrategy setStyle() {
        // 定义样式：自动换行
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 关键：开启自动换行
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Microsoft YaHei"); // 字体
        writeFont.setFontHeightInPoints((short) 12); // 字体大小
        contentWriteCellStyle.setWriteFont(writeFont);

        // 注册样式策略（全局生效）
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                null, // 头样式（默认）
                contentWriteCellStyle // 内容样式（自动换行）
        );
        return styleStrategy;
    }

    private Map<String, BillOfQuantityVo> countQuantitiesMaterialsAssociated(List<String> billIds) {
        List<BillOfQuantityEntity> billList = billOfQuantityRepository.queryByIds(billIds);
        List<BillOfQuantityVo> billVoList = ProjectBillMaterialConvert.INSTANCE.convertListVo(billList);

        if (CollectionUtils.isNotEmpty(billVoList)) {
            List<BillAssociationCountEntity> billCountList = projectBillMaterialDomainService.countAssociatedMaterialNum(billIds);
            Map<String, Integer> countMap = billCountList.stream()
                    .collect(Collectors.toMap(BillAssociationCountEntity::getBillId, BillAssociationCountEntity::getAssociatedNum, (a, b) -> a));

            billVoList.forEach(item -> {
                Integer val = countMap.get(item.getId());
                item.setAssociatedMaterialNum(val == null ? 0 : val);
            });
        }

        // 将billList转成billMap, key为工程量工程量清单id,value为BillOfQuantityVo对象
        return billVoList.stream()
                .collect(Collectors.toMap(BillOfQuantityVo::getId, Function.identity()));
    }

    /* Started by AICoder, pid:u371b78c4fbb67514f200913d1b442294d31e285 */
    private List<ExportBillQuantitiesVo> getExportBillQuantitiesVoLists(BillOfQuantityExportDto exportDto) {
        List<ExportBillQuantitiesVo> exportBillQuantitiesVoList = new ArrayList<>();
        List<BillOfQuantityVo> billOfQuantityVos;
        BillOfQuantityQueryDto queryDto = new BillOfQuantityQueryDto();

        if (CollectionUtils.isEmpty(exportDto.getBillIds())) {
            // 用户未勾选工程量清单(根据项目id查询所有工程量清单)
            queryDto.setId(exportDto.getProjectId());
            // 根据项目id查询所有工程量清单信息
            billOfQuantityVos = billOfQuantityRepository.queryByCondition(queryDto);
        } else {
            // 只查询用户勾选的工程量清单信息
            billOfQuantityVos = ProjectBillMaterialConvert.INSTANCE.convertListVo(billOfQuantityRepository.queryByIds(exportDto.getBillIds()));
        }

        // 收集工程量清单id
        List<String> billIds = billOfQuantityVos.stream().map(BillOfQuantityVo::getId).collect(Collectors.toList());
        // 统计工程量清单关联物料数量
        Map<String, BillOfQuantityVo> billOfQuantityVoMap = countQuantitiesMaterialsAssociated(billIds);

        // 收集产品小类id
        Map<String, BillOfQuantityVo> latestProductCategoryMap = billOfQuantityVos.stream()
                .collect(Collectors.toMap(
                        BillOfQuantityVo::getProductSubcategory, // 键为 productSubcategory
                        bill -> bill,                            // 值为 BillOfQuantityVo 对象本身
                        (existing, replacement) -> replacement,  // 合并函数：保留后出现的值
                        LinkedHashMap::new                        // 使用 LinkedHashMap 以保持插入顺序
                ));

        List<String> collectProductCategoryIds = new ArrayList<>(latestProductCategoryMap.keySet());

        // 根据产品小类id查询产品小类信息
        List<ProductCategoryInfoVo> productCategoryInfoVos = productService.selectProductCategoryList(collectProductCategoryIds);

        // 构造一个map集合, key: 产品小类id, value: 产品小类路径名称
        Map<String, ProductCategoryInfoVo> productCategoryInfoVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productCategoryInfoVos)) {
            productCategoryInfoVoMap = productCategoryInfoVos.stream().collect(Collectors.toMap(ProductCategoryInfoVo::getId, Function.identity()));
        }

        // 遍历工程量清单列表
        for (BillOfQuantityVo billOfQuantityVo : billOfQuantityVos) {
            // 遍历查询当前工程量清单关联的物料信息
            List<String> billId = new ArrayList<>();
            billId.add(billOfQuantityVo.getId());

            List<ProjectBillMaterialEntity> entityList = projectBillMaterialDomainService.queryMapAssociatedMaterials(billId);
            if (CollectionUtils.isEmpty(entityList)) {
                // 如果没有关联的物料数据, 只保存工程量清单信息即可
                ExportBillQuantitiesVo exportBillQuantitiesVo = new ExportBillQuantitiesVo();
                exportBillQuantitiesVo.setBillName(billOfQuantityVo.getName());
                exportBillQuantitiesVo.setAssociatedNum(billOfQuantityVoMap.get(billOfQuantityVo.getId()).getAssociatedMaterialNum().toString());
                exportBillQuantitiesVo.setCharacteristic(billOfQuantityVo.getCharacteristic());
                exportBillQuantitiesVo.setUnitOfMeasurement(billOfQuantityVo.getUnitOfMeasurement());
                exportBillQuantitiesVo.setQuantity(billOfQuantityVo.getQuantity());
                exportBillQuantitiesVo.setSubProject(billOfQuantityVo.getSubProject());

                // 直接获取 pathName 或者返回空字符串作为默认值
                String pathName = Optional.ofNullable(productCategoryInfoVoMap.get(billOfQuantityVo.getProductSubcategory()))
                        .map(ProductCategoryInfoVo::getPathName)
                        .orElse("");
                exportBillQuantitiesVo.setProductSubcategory(pathName);

                // 以下部分属性需要进行国际化处理
                setPassCheckResult(exportBillQuantitiesVo, billOfQuantityVo);
                exportBillQuantitiesVoList.add(exportBillQuantitiesVo);
                // 跳过本次循环
                continue;
            }

            // 收集物料id
            List<String> materialIds = entityList.stream().map(ProjectBillMaterialEntity::getMaterialId).collect(Collectors.toList());

            // 根据物料id合集查询物料信息
            List<MaterialWithExtendInfoVo> materialVos = productService.queryMaterialByIds(materialIds);

            for (MaterialWithExtendInfoVo material : materialVos) {
                ExportBillQuantitiesVo exportBillQuantitiesVo = new ExportBillQuantitiesVo();
                exportBillQuantitiesVo.setBillName(billOfQuantityVo.getName());
                exportBillQuantitiesVo.setAssociatedNum(billOfQuantityVoMap.get(billOfQuantityVo.getId()).getAssociatedMaterialNum().toString());
                exportBillQuantitiesVo.setCharacteristic(billOfQuantityVo.getCharacteristic());
                exportBillQuantitiesVo.setUnitOfMeasurement(billOfQuantityVo.getUnitOfMeasurement());
                exportBillQuantitiesVo.setQuantity(billOfQuantityVo.getQuantity());
                exportBillQuantitiesVo.setSubProject(billOfQuantityVo.getSubProject());

                // 直接获取 pathName 或者返回空字符串作为默认值
                String pathName = Optional.ofNullable(productCategoryInfoVoMap.get(billOfQuantityVo.getProductSubcategory()))
                        .map(ProductCategoryInfoVo::getPathName)
                        .orElse("");
                exportBillQuantitiesVo.setProductSubcategory(pathName);

                // 以下部分工程量清单属性需要进行国际化处理
                setPassCheckResult(exportBillQuantitiesVo, billOfQuantityVo);

                // 下面是物料相关信息----------------------------
                exportBillQuantitiesVo.setMaterialName(material.getMaterialName());

                // 销售代码与产品代码
                if (material.getPdmInfo() != null) {
                    exportBillQuantitiesVo.setSalesCode(material.getPdmInfo().getSalesCode());
                    exportBillQuantitiesVo.setProductCode(material.getPdmInfo().getProductionCode());
                }
                if (material.getOthInfo() != null) {
                    exportBillQuantitiesVo.setSalesCode(material.getOthInfo().getSalesCode());
                    exportBillQuantitiesVo.setProductCode(material.getOthInfo().getProductionCode());
                }

                Integer amount = entityList.stream()
                        .filter(entity -> exportDto.getProjectId().equals(entity.getProjectId()) &&
                                billOfQuantityVo.getId().equals(entity.getBillId()) &&
                                material.getId().equals(entity.getMaterialId()))
                        .map(entity -> entity.getAmount() != null ? entity.getAmount() : GlobalConstants.ONE) // 处理null情况
                        .findFirst()
                        .orElse(GlobalConstants.ONE); // 如果没有找到匹配项，则返回1

                exportBillQuantitiesVo.setAmount(amount);
                exportBillQuantitiesVoList.add(exportBillQuantitiesVo);
            }
        }
        return exportBillQuantitiesVoList;
    }

    /* Ended by AICoder, pid:u371b78c4fbb67514f200913d1b442294d31e285 */


    /* Ended by AICoder, pid:x528eddf79l995b1485f0bc331e4d53395996179 */

    /* Ended by AICoder, pid:o97a8vab969a76d140240aeff1b5c91651d97b14 */
    public List<TemplateImportVo> getAllCheckData(BillOfQuantityListener materialReadListener) {
        List<TemplateImportVo> parseDataList = materialReadListener.getParseDataList();
        log.info("TemplateImportVo  parseDataList size========={}", parseDataList.size());
        List<TemplateImportVo> pathDataList = parseDataList.stream().filter(p -> StringUtils.isNotBlank(p.getPathName())).collect(Collectors.toList());
        List<TemplateImportVo> noProduct = parseDataList.stream().filter(p -> StringUtils.isBlank(p.getPathName())).collect(Collectors.toList());
        noProduct.forEach(this::setPassCheckResult);
        List<TemplateImportVo> result = new ArrayList<>();
        List<TemplateImportVo> templateImportVos = checkImportData(pathDataList);
        result.addAll(templateImportVos);
        result.addAll(noProduct);
        return result;
    }
    //属性需要进行国际化处理方法
    /* Started by AICoder, pid:k2c1b0e669c0e3f1416f0b50a021523ee8768d22 */
// 属性需要进行国际化处理方法
    private void setPassCheckResult(ExportBillQuantitiesVo exportBillQuantitiesVo, BillOfQuantityVo billOfQuantityVo) {
        Locale currentLocale = Locale.CHINA;

        if (StringUtils.isNotBlank(billOfQuantityVo.getSupply())) {
            String strName = SupplyTypeEnums.getNameByCode(billOfQuantityVo.getSupply());
            String localizedName = I18nUtil.getI18nFromString(strName, currentLocale);
            exportBillQuantitiesVo.setSupply(localizedName);
        }

        if (StringUtils.isNotBlank(billOfQuantityVo.getOneTimeUnloading())) {
            String localizedName = I18nUtil.getI18nFromString(SupplyTypeEnums.getNameByCode(billOfQuantityVo.getOneTimeUnloading()), currentLocale);
            exportBillQuantitiesVo.setOneTimeUnloading(localizedName);
        }

        if (StringUtils.isNotBlank(billOfQuantityVo.getEquipmentPositioning())) {
            String localizedMenuName = I18nUtil.getI18nFromString(SupplyTypeEnums.getNameByCode(billOfQuantityVo.getEquipmentPositioning()), currentLocale);
            exportBillQuantitiesVo.setEquipmentPositioning(localizedMenuName);
        }

        if (StringUtils.isNotBlank(billOfQuantityVo.getInstallationSupervision())) {
            String localizedMenuName = I18nUtil.getI18nFromString(InstallationSupervisionNameEnums.getNameByCode(billOfQuantityVo.getInstallationSupervision()), currentLocale);
            exportBillQuantitiesVo.setInstallationSupervision(localizedMenuName);
        }

        if (StringUtils.isNotBlank(billOfQuantityVo.getCommissioning())) {
            String localizedMenuName = I18nUtil.getI18nFromString(SupplyTypeEnums.getNameByCode(billOfQuantityVo.getCommissioning()), currentLocale);
            exportBillQuantitiesVo.setCommissioning(localizedMenuName);
        }

        if (StringUtils.isNotBlank(billOfQuantityVo.getPatrolInspection())) {
            String localizedMenuName = I18nUtil.getI18nFromString(SupplyTypeEnums.getNameByCode(billOfQuantityVo.getPatrolInspection()), currentLocale);
            exportBillQuantitiesVo.setPatrolInspection(localizedMenuName);
        }
    }

    /* Ended by AICoder, pid:k2c1b0e669c0e3f1416f0b50a021523ee8768d22 */
    private List<TemplateImportVo> checkImportData(List<TemplateImportVo> pathDataList) {
        if (CollectionUtils.isEmpty(pathDataList)) {
            return Collections.emptyList();
        }
        Set<String> pathList = pathDataList.stream()
                .map(TemplateImportVo::getPathName)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        //TODO 增加根据路径查询产品小类的PRC调用
        List<ProductCategoryInfoVo> productCategoryInfoVos = productService.queryProductCategoryByPath(pathList);
        if (CollectionUtils.isEmpty(productCategoryInfoVos)) {
            String i18nFromString = I18nUtil.getI18n(GlobalConstants.PRODUCT_SUBCATEGORY_NOT_EXIST);
            pathDataList.forEach(p -> setCheckResultAndErrorReason(p, i18nFromString));
            return pathDataList;
        }
        Map<String, ProductCategoryInfoVo> productCategoryInfoVoMap = productCategoryInfoVos.stream()
                .collect(Collectors.toMap(ProductCategoryInfoVo::getPathName, Function.identity(), (exist, replace) -> exist));
        Locale language = I18nUtil.getLanguage();
        return pathDataList.stream().parallel().unordered()
                .map(templateImportVo -> checkTemplateData(templateImportVo, productCategoryInfoVoMap, language)).collect(Collectors.toList());
    }

    private TemplateImportVo checkTemplateData(TemplateImportVo templateImportVo, Map<String, ProductCategoryInfoVo> productCategoryInfoVoMap, Locale language) {
        String pathName = templateImportVo.getPathName();
        ProductCategoryInfoVo productCategoryInfoVo = productCategoryInfoVoMap.get(pathName);
        if (productCategoryInfoVo == null) {
            setCheckResultAndErrorReason(templateImportVo, I18nUtil.getI18n(GlobalConstants.PRODUCT_SUBCATEGORY_NOT_EXIST));
            return templateImportVo;
        }
        templateImportVo.setProductSubcategory(productCategoryInfoVo.getId());
        setPassCheckResult(templateImportVo);
        return templateImportVo;
    }

    private void setPassCheckResult(TemplateImportVo templateImportVo) {
        if (!templateImportVo.isFail()) {
            templateImportVo.setCheckResult(TemplateCheckResultEnums.PASS.getCode());
        }
    }

    private void setCheckResultAndErrorReason(TemplateImportVo templateImportVo, String errorReason) {
        templateImportVo.setCheckResult(TemplateCheckResultEnums.FAIL.getCode());
        String existingErrorReason = StringUtils.defaultIfBlank(templateImportVo.getFailureReason(), "");
        String separator = existingErrorReason.isEmpty() ? "" : GlobalConstants.SEMICOLON;
        String newReason = existingErrorReason + separator + errorReason;
        templateImportVo.setFailureReason(newReason);
    }

    private Set<String> getAllProductCategory(String projectId) {
        BillOfQuantityQueryDto queryDto = new BillOfQuantityQueryDto();
        queryDto.setId(projectId);
        return billOfQuantityRepository.queryByCondition(queryDto).stream()
                .map(BillOfQuantityVo::getProductSubcategory)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    private Set<String> getExcludeIdProductCategory(String projectId, Set<String> excludeIds) {
        //获取项目下排除 excludeIds 外的产品小类
        BillOfQuantityQueryDto queryDto = new BillOfQuantityQueryDto();
        queryDto.setId(projectId);
        return billOfQuantityRepository.queryByCondition(queryDto).stream()
                .filter(p -> !excludeIds.contains(p.getId()))
                .map(BillOfQuantityVo::getProductSubcategory)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    private void createToDoTasks(String projectId, List<String> productCategoryIds, String userId) {
        if (CollectionUtils.isNotEmpty(productCategoryIds)) {
            //构建请求参数
            MktApprovalAddDto mktApproval = new MktApprovalAddDto();
            mktApproval.setType(4);
            mktApproval.setProjectId(projectId);
            mktApproval.setSchemeSeId(userId);
            mktApproval.setProductCategoryIds(productCategoryIds);
            // 创建待办任务
            log.debug("createToDoTasks mktApproval:{}", mktApproval);
//            processService.createMktApproval(mktApproval);
        }
    }

    private void cancelToDoTasks(String projectId, List<String> productCategoryIds, String reason) {
        if (CollectionUtils.isNotEmpty(productCategoryIds)) {
            //构建请求参数
            MktApprovalCancelDto approvalCancelDto = new MktApprovalCancelDto();
            approvalCancelDto.setProjectId(projectId);
            approvalCancelDto.setProductCategoryId(productCategoryIds);
            approvalCancelDto.setReason(reason);
            //  取消任务
            log.debug("cancelToDoTasks approvalCancelDto:{}", approvalCancelDto);
//            processService.cancelMktApproval(approvalCancelDto);
        }
    }

    private void checkAndCreateTask(String projectId, String productCategoryId, String userId) {
        long subCategory = billOfQuantityRepository.existSubCategory(projectId, productCategoryId);
        if (subCategory > GlobalConstants.ZERO) {
            log.info("The project:{} already has pending tasks for product subclasses:{}", projectId, productCategoryId);
            return;
        }
        createToDoTasks(projectId, Collections.singletonList(productCategoryId), userId);
    }

    private void checkAndCancelTask(String projectId, String productCategoryId, String reason) {
        long subCategory = billOfQuantityRepository.existSubCategory(projectId, productCategoryId);
        //如果存在多个，则直接返回
        if (subCategory > GlobalConstants.ONE) {
            log.info("There are duplicate product subcategories:{} in the project:{}", productCategoryId, projectId);
            return;
        }
        cancelToDoTasks(projectId, Collections.singletonList(productCategoryId), reason);
    }

    private void populateAuditFields(BillOfQuantityEntity entity, String userId, String currentTime) {
        if (entity.getId() == null) { // 判断是否为新增操作
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateBy(userId);
            entity.setCreateTime(currentTime);
        }
        entity.setUpdateBy(userId);
        entity.setUpdateTime(currentTime);
    }

    private void sendSuccessLogMessage(OperationLogBean operationLogBean, String detail) {
        sendKafkaMessage(operationLogBean, OperationResultOptional.OPERATION_RESULT_SUCCESS, detail);
    }

    private void sendFailLogMessage(OperationLogBean operationLogBean, String errorMessage, String detail) {
        String[] args = new String[]{errorMessage};
        operationLogBean.setFailReason(I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_FAIL_REASON, args));
        sendKafkaMessage(operationLogBean, OperationResultOptional.OPERATION_RESULT_FAIL, detail);
    }

    private void sendKafkaMessage(OperationLogBean operationLogBean, OperationResultOptional optional, String detail) {

        //设置结束时间
        operationLogBean.setLogEndDate(DateTimeUtils.getCurrentTime());
        operationLogBean.setOperateResult(optional.getId());
        String[] args = new String[]{detail};
        String detailZh = I18nUtil.getI18nWithArgs(GlobalConstants.BOQ_BATCH_OPERATION_DETAIL, args, Locale.CHINA);
        String detailEn = I18nUtil.getI18nWithArgs(GlobalConstants.BOQ_BATCH_OPERATION_DETAIL, args, Locale.ENGLISH);
        operationLogBean.setDetail(I18nUtils.toI18nJson(detailZh, detailEn));
        try {
            log.debug("batch operation, :operationLogBean={}!", operationLogBean);
            kafkaSenderService.send(KafkaTopicOptional.KAFKA_TOPIC_OTCP_LOG_MANAGE, operationLogBean);
        } catch (Exception e) {
            log.error("log Message send failed for ", e);
        }
    }

    private OperationLogBean buildOperationLogBean(String operation, OperationTypeOptional operationType, String userName) {
        OperationLogBean operationLogBean = new OperationLogBean();
        //操作
        String cnOperation = I18nUtil.getI18n(operation, Locale.CHINA);
        String enOperation = I18nUtil.getI18n(operation, Locale.ENGLISH);
        String i18nJson = I18nUtils.toI18nJson(cnOperation, enOperation);
        operationLogBean.setOperation(i18nJson);
        operationLogBean.setDescriptionInfo(i18nJson);
        //模块
        String cnModule = I18nUtil.getI18n(GlobalConstants.PROJECT_MODULE, Locale.CHINA);
        String enModule = I18nUtil.getI18n(GlobalConstants.PROJECT_MODULE, Locale.US);
        operationLogBean.setAppModule(I18nUtils.toI18nJson(cnModule, enModule));
        //风险等级
        operationLogBean.setRank(OperationLogRankEnum.VERY_IMPORTANT.getId());
        //操作类型
        operationLogBean.setOperateType(operationType.getId());
        //操作人员及时间
        operationLogBean.setUserName(userName);
        operationLogBean.setLogStartDate(DateTimeUtils.getCurrentTime());
        operationLogBean.setHostname(HttpServletUtil.getHost());
        operationLogBean.setConnectMode(ConnectModeOptional.CONNECT_MODE_WEB.getId());
        return operationLogBean;
    }
}
/* Ended by AICoder, pid:i373bkb65aj0d2b14b540b427359db819c8827f6 */