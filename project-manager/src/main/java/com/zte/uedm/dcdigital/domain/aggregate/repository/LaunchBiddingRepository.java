/* Started by AICoder, pid:046d1z8b24904ca1411e099a202a852ad92973a7 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.LaunchBiddingEntity;

/**
 * LaunchBiddingRepository接口定义了与LaunchBiddingEntity相关的数据持久化操作。
 *
 * <AUTHOR> Name]
 * @version 1.0
 * @since [Date]
 */
public interface LaunchBiddingRepository {

    /**
     * 根据ID查询LaunchBiddingEntity实体。
     *
     * @param id 要查询的实体的唯一标识符
     * @return 如果找到，返回对应的LaunchBiddingEntity实体；否则返回null
     */
    LaunchBiddingEntity queryById(String id);

    /**
     * 保存或更新一个LaunchBiddingEntity实体。
     *
     * @param launchBiddingEntity 要保存或更新的实体
     * @param isUpdate 标志是否为更新操作。如果为true，则执行更新操作；否则执行插入操作
     */
    void saveOrUpdate(LaunchBiddingEntity launchBiddingEntity, boolean isUpdate);
}
/* Ended by AICoder, pid:046d1z8b24904ca1411e099a202a852ad92973a7 */