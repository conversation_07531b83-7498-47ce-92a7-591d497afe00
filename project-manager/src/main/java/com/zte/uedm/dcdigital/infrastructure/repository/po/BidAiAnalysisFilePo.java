/* Started by AICoder, pid:84ef9h444a7cc01146670add907f6e6c61b0eab2 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * BidAiAnalysisFilePo 数据持久化对象，用于封装标书AI分析请求中的文件信息。
 * 该类包含文件ID、分析ID、页码、文件类型、文件名称、内容、创建人和创建时间等信息。
 */
@Getter
@Setter
@ToString
public class BidAiAnalysisFilePo {

    /**
     * 主键ID，唯一标识一个记录。
     */
    private String id;

    /**
     * 分析ID，关联到具体的标书AI分析任务。
     */
    private String analysisId;

    /**
     * 文件ID，唯一标识一个文件。
     */
    private String fileId;

    /**
     * 页码，表示文件中的具体页面。
     */
    private String page;

    /**
     * 文件类型，描述文件的类型（例如：PDF、DOCX）。
     */
    private String fileType;

    /**
     * 文件名称，描述文件的名称。
     */
    private String fileName;

    /**
     * 文件内容，存储文件的具体内容。
     */
    private String content;

    /**
     * 创建人，记录创建该记录的用户ID。
     */
    private String createBy;

    /**
     * 创建时间，记录该记录的创建时间。
     */
    private String createTime;
}
/* Ended by AICoder, pid:84ef9h444a7cc01146670add907f6e6c61b0eab2 */