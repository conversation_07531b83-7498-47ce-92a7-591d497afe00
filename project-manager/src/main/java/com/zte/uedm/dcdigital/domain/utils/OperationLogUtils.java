package com.zte.uedm.dcdigital.domain.utils;

import com.zte.uedm.basis.util.base.http.ConnectModeOptional;
import com.zte.uedm.basis.util.base.http.HttpServletUtil;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

/**
 * 构建请求日志BEAN
 */
@Slf4j
public class OperationLogUtils {

    public static OperationLogBean buildOperationLogBean(String operation, OperationTypeOptional operationType, String userName, String optional, Object message,String operationLogRank) {
        OperationLogBean operationLogBean = new OperationLogBean();
        //操作
        String cnOperation = I18nUtil.getI18n(operation, Locale.CHINA);
        String enOperation = I18nUtil.getI18n(operation, Locale.ENGLISH);
        String i18nJson = I18nUtils.toI18nJson(cnOperation, enOperation);
        operationLogBean.setOperation(i18nJson);
        operationLogBean.setDescriptionInfo(i18nJson);
        //模块
        String cnModule = "产品管理模块";
        String enModule = "Module.product.manager";
        operationLogBean.setAppModule(I18nUtils.toI18nJson(cnModule, enModule));
        //风险等级
        operationLogBean.setRank(operationLogRank);
        //操作类型
        operationLogBean.setOperateType(operationType.getId());
        //操作人员及时间
        operationLogBean.setUserName(userName);
        operationLogBean.setLogStartDate(DateTimeUtils.getCurrentTime());
        operationLogBean.setHostname(HttpServletUtil.getHost());
        operationLogBean.setConnectMode(ConnectModeOptional.CONNECT_MODE_WEB.getId());
        //设置结束时间
        operationLogBean.setLogEndDate(DateTimeUtils.getCurrentTime());
        operationLogBean.setOperateResult(optional);
        String detail = buildLogDetail(message);
        String[] args = new String[]{detail};
        String detailZh = I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_LOG, args, Locale.CHINA);
        String detailEn = I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_LOG, args, Locale.ENGLISH);
        operationLogBean.setDetail(I18nUtils.toI18nJson(detailZh, detailEn));
        return operationLogBean;
    }

    public static String buildLogDetail(Object object) {
        JsonUtils jsonUtils = JsonUtils.getInstance();
        try {
            return jsonUtils.objectToJson(object);
        } catch (UedmException e) {
            log.error("convert error",e);
        }
        return StringUtils.EMPTY;
    }

}
