package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenImplementationHistoryPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeepenImplementationHistoryQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 深化实施历史表数据库访问层接口
 */
@Mapper
public interface DeepenImplementationHistoryMapper extends BaseMapper<DeepenImplementationHistoryPo> {

    /**
     * 根据项目ID查询深化实施历史记录（按更新时间倒序）
     *
     * @param queryDto 查询条件
     * @return 深化实施历史记录列表
     */
    List<DeepenImplementationHistoryPo> selectHistoryByItemId(@Param("queryDto") DeepenImplementationHistoryQueryDto queryDto);

    /**
     * 插入深化实施历史记录
     *
     * @param po 深化实施历史记录
     * @return 影响的行数
     */
    int insertHistory(DeepenImplementationHistoryPo po);
}
