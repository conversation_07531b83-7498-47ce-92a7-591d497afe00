/* Started by AICoder, pid:686dcadfe6f914114b4908a820288c65ea824330 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName("deepen_design_files_relation")
public class DeepenDesignFilesRelationPo {
    /**
     * 流程flowId(关联文件id)
     */
    @TableId(value = "flow_id")
    private String flowId;

    /**
     * 项目id
     */
    @TableField("item_id")
    private String itemId;

    /**
     * 产品小类id
     */
    @TableField("product_category_id")
    private String productCategoryId;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 提交人
     */
    @TableField("submit_user")
    private String submitUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private String updateTime;

    /**
     * 验证结果: 0-未上传附件,1-已上传附件
     */
    @TableField("approval_result")
    private String approvalResult;
}

/* Ended by AICoder, pid:686dcadfe6f914114b4908a820288c65ea824330 */