package com.zte.uedm.dcdigital.domain.common.enums;

public enum HandoverStatusEnums {
    NORMAL("1","{\"zh-CN\":\"正常\",\"en-US\":\"Normal\"}"),
    DELAY_DEMAND("2","{\"zh-CN\":\"延误（客户需求变化）\",\"en-US\":\"Delay (changes in customer demand)\"}"),
    DELAY_PRODUCT("3","{\"zh-CN\":\"延误（产品原因）\",\"en-US\":\"Delay (product cause)\"}"),
    DELAY_BUSINESS("4","{\"zh-CN\":\"延误（商务原因）\",\"en-US\":\"Delay (business reasons)\"}"),
    DELAY_OTHER("5","{\"zh-CN\":\"延误（其它）\",\"en-US\":\"Delay (other)\"}"),
    ;

    private final String code;
    private final String name;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
    HandoverStatusEnums(String code, String name)
    {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (HandoverStatusEnums statusEnums : HandoverStatusEnums.values()) {
            if (statusEnums.getCode().equals(code)) {
                return statusEnums.getName();
            }
        }
        return null;
    }
}
