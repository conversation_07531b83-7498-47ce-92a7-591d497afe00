/* Started by AICoder, pid:n3314rbe9525749147a00a3bd1e2918dccd50b2e */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.enums.InstallationSupervisionNameEnums;
import com.zte.uedm.dcdigital.domain.common.enums.SupplyTypeEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * BillOfQuantityAddDto 数据传输对象，用于添加工程量清单。
 * 该类包含多个字段，用于描述工程量清单的各种属性，并提供了验证方法。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Slf4j
public class BillOfQuantityAddDto {

    /**
     * 归属项目ID，不能为空。
     */
    @NotBlank(message = "归属项目id不能为空")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    @DcResourceField
    private String ascriptionId;

    /**
     * 工程量清单名称，必填项，最大长度为200字符。
     */
    @NotBlank(message = "工程量清单名称为必填项")
    @Size(max = 200, message = "工程量清单名称长度不能超过200字符")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String name;

    /**
     * 特征描述，最大长度为1000字符。
     */
    @Size(max = 1000, message = "特征长度不能超过1000字符")
    private String characteristic;

    /**
     * 计量单位，必填项，最大长度为10字符。
     */
    @Size(max = 10, message = "计量单位长度不能超过10字符")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String unitOfMeasurement;

    /**
     * 工程量，必填项，必须大于等于0，精确到小数点后两位。
     */
    @DecimalMin(value = "0.00", inclusive = true, message = "工程量必须大于等于0")
    @Digits(integer = 10, fraction = 2, message = "工程量精确到小数点后两位")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private BigDecimal quantity;

    /**
     * 所属工程分项，最大长度为20字符。
     */
    @Size(max = 20, message = "所属工程分项长度不能超过20字符")
    private String subProject;

    /**
     * 供货类型，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String supply;

    /**
     * 所属产品小类，当供货选择为产线时必填（待选项为系统的产品小类），当供货选择不是产线时必须为空。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String productSubcategory;

    /**
     * 一次性卸货，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String oneTimeUnloading;

    /**
     * 设备就位，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String equipmentPositioning;

    /**
     * 安装督导，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String installationSupervision;

    /**
     * 调试，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String commissioning;

    /**
     * 巡检，必填项。
     */
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String patrolInspection;

    /**
     * 验证DTO字段的有效性。
     */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER.getCode(), validResult.getErrorMessage());
        }

        // 校验供货/一次卸货/设备就位/安装督导/调试/巡检 的枚举
        List<String> fieldsToValidate = Arrays.asList(
                supply,
                oneTimeUnloading,
                equipmentPositioning,
                commissioning,
                patrolInspection
        );
        validateEnumFields(fieldsToValidate);
        validateEnuminstallationSupervision(installationSupervision);
        // 自定义验证 productSubcategory
        validateProductSubcategory();
    }

    /**
     * 校验供货、一次卸货、设备就位、安装督导、调试、巡检的枚举值是否有效。
     *
     * @param fields 需要校验的字段列表
     */
    private void validateEnumFields(List<String> fields) {
        for (String field : fields) {
            if (StringUtils.isNotBlank(field) && SupplyTypeEnums.isNotExist(field)) {
                log.error("参数: {} 不在约定范围内.", field);
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
        }
    }

    /**
     * 校验安装监督名称是否在枚举范围内。
     *
     * @param installationSupervision 安装监督名称
     */
    private void validateEnuminstallationSupervision(String installationSupervision) {
        if (StringUtils.isNotBlank(installationSupervision) && InstallationSupervisionNameEnums.isNotExist(installationSupervision)) {
            log.error("参数: {} 不在约定范围内.", installationSupervision);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
    }

    /**
     * 自定义验证productSubcategory字段。
     */
    private void validateProductSubcategory() {
        boolean isBlank = StringUtils.isBlank(productSubcategory);
        if (SupplyTypeEnums.PRODUCTION_LINE_SUPPLY.getCode().equals(supply) && isBlank) {
            log.error("Product subcategory is required when selecting production line as the supply option.");
            throw new BusinessException(ProjectStatusCode.SUBCATEGORY_MISS_ERROR);
        }
        if (!SupplyTypeEnums.PRODUCTION_LINE_SUPPLY.getCode().equals(supply) && !isBlank) {
            log.error("Product subcategory must be empty when the supply selection is not for the production line.");
            throw new BusinessException(ProjectStatusCode.SUBCATEGORY_NOT_EMPTY);
        }
    }
}
/* Ended by AICoder, pid:n3314rbe9525749147a00a3bd1e2918dccd50b2e */