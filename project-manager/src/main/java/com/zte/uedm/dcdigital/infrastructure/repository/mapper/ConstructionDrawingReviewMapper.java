package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ConstructionDrawingReviewPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 施工图会审记录表数据库访问层接口
 */
@Mapper
public interface ConstructionDrawingReviewMapper extends BaseMapper<ConstructionDrawingReviewPo> {

    /**
     * 插入施工图会审记录
     *
     * @param po 施工图会审记录
     * @return 影响的行数
     */
    int insertReviewRecord(ConstructionDrawingReviewPo po);

    /**
     * 根据ID更新施工图会审记录
     *
     * @param po 包含更新数据及ID的实体对象
     * @return 影响的行数
     */
    int updateReviewRecordById(ConstructionDrawingReviewPo po);

    /**
     * 根据ID删除施工图会审记录
     *
     * @param id 会审记录ID
     * @return 影响的行数
     */
    int deleteReviewRecordById(@Param("id") String id);

    /**
     * 根据项目ID查询所有施工图会审记录
     *
     * @param itemId 项目ID
     * @return 会审记录列表
     */
    List<ConstructionDrawingReviewPo> selectReviewRecordsByItemId(@Param("itemId") String itemId);

    /**
     * 根据记录ID查询施工图会审记录详情
     *
     * @param id 会审记录ID
     * @return 会审记录详情
     */
    ConstructionDrawingReviewPo selectReviewRecordById(@Param("id") String id);
}
