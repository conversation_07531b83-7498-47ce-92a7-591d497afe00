/* Started by AICoder, pid:pf0688930at5986146790928503f0c26fed71529 */
package com.zte.uedm.dcdigital.application.project;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.project.BillOfQuantityInnerVo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BillOfQuantityExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BillOfQuantityQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BillOfQuantityVo;

import javax.servlet.http.HttpServletResponse;

/**
 * BillOfQuantityQueryService 接口定义了工程量清单的查询操作。
 */
public interface BillOfQuantityQueryService {

    /**
     * 根据项目ID查询工程量清单。
     *
     * @param queryDto 查询条件，包含项目ID和其他可能的过滤条件。
     * @return 包含查询结果的分页对象，每个结果都是一个 BillOfQuantityVo 对象。
     */
    PageVO<BillOfQuantityVo> queryByCondition(BillOfQuantityQueryDto queryDto);

    /**
     * 根据ID查询单个工程量清单。
     *
     * @param id 工程量清单的唯一标识符。
     * @return 包含查询结果的 BillOfQuantityVo 对象，如果未找到则返回 null。
     */
    BillOfQuantityVo queryById(String id);

    /**
     * 根据工程量清单id导出关联物料信息
     * @param exportDto 导出条件
     * @param response 响应对象
     * */
    void exportBillOfQuantity(BillOfQuantityExportDto exportDto, HttpServletResponse response);

    BillOfQuantityInnerVo queryBillOfQuantitiesById(String id);

    PageVO<BillOfQuantityVo> queryByConditionFilter(BillOfQuantityQueryDto queryDto);
}
/* Ended by AICoder, pid:pf0688930at5986146790928503f0c26fed71529 */