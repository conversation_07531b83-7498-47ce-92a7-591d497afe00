/* Started by AICoder, pid:fd61fd17102a92e148f70ba7b051756df22817c5 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 启动招标返回信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class LaunchBiddingVo {
    /**
     * 项目ID。
     */
    private String id;

    /**
     * 项目名称。
     */
    private String name;

    /**
     * 项目状态。
     */
    private Integer projectStage;

    /**
     * 项目阶段名称。
     */
    private String projectStageName;

    /**
     * 发标时间。
     */
    private String bidIssuingTime;

    private String decompressCompleteTime;

    private String decompressUpdateTime;

    /**
     * 澄清提交时间。
     */
    private String clarifySubmissionTime;

    /**
     * 完成澄清提交时间。
     */
    private String clarifySubmissionCompleteTime;

    /**
     * 配置清单锁定时间。
     */
    private String configureManifestLockTime;

    /**
     * 完成配置清单锁定时间。
     */
    private String configureManifestLockTimeCompleteTime;

    /**
     * 招标文件定稿时间。
     */
    private String biddingDocumentsFinalizationTime;

    /**
     * 完成招标文件定稿时间。
     */
    private String biddingDocumentsFinalizationCompletionTime;

    /**
     * 交标时间。
     */
    private String bidSubmissionTime;

    /**
     * 投标结论确认更新时间。
     */
    private String bidConclusionUpdateTime;

    /**
     *     BIDDING("BIDDING", 1, "{\"zh-CN\":\"发标\",\"en-US\":\"Bidding\"}"),
     *     DECOMPOSITION("DECOMPOSITION", 2,  "{\"zh-CN\":\"标书分解\",\"en-US\":\"Bidding decomposition\"}"),
     *     CLARIFICATION("CLARIFICATION", 3, "{\"zh-CN\":\"澄清\",\"en-US\":\"Clarification\"}"),
     *     CONFIGURATION_LOCK("CONFIGURATION_LOCK", 4, "{\"zh-CN\":\"配置清单锁定\",\"en-US\":\"Configuration lock\"}"),
     *     BIDDING_DOC_FINALIZE("BIDDING_DOC_FINALIZE", 5, "{\"zh-CN\":\"投标文件定稿\",\"en-US\":\"Bidding document finalize\"}"),
     *     BIDDING_SUBMISSION("BIDDING_SUBMISSION", 6, "{\"zh-CN\":\"交标\",\"en-US\":\"Bidding submission\"}"),
     *     CONFIRMATION_BIDDING_CONCLUSION("CONFIRMATION_BIDDING_CONCLUSION", 7, "{\"zh-CN\":\"投标结论确认\",\"en-US\":\"Confirmation of the bidding conclusion\"}");
     */
    private Map<String, Boolean> result = new HashMap<String, Boolean>(){{
        put("BIDDING", false);
        put("DECOMPOSITION", false);
        put("CLARIFICATION", false);
        put("CONFIGURATION_LOCK", false);
        put("BIDDING_DOC_FINALIZE", false);
        put("BIDDING_SUBMISSION", false);
        put("CONFIRMATION_BIDDING_CONCLUSION", false);
    }};
}
/* Ended by AICoder, pid:fd61fd17102a92e148f70ba7b051756df22817c5 */