/* Started by AICoder, pid:b2ee2871bec2b68145a8093700ab6a16a68354bc */
package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.common.bean.project.OpportunitySupportInnerDto;
import com.zte.uedm.dcdigital.common.bean.project.OpportunitySupportInnerVo;

public interface OpportunitySupportDomainService {

    void addOpportunitySupport(OpportunitySupportInnerDto supportInnerDto);

    void updateOpportunitySupport(OpportunitySupportInnerDto supportInnerDto);

    OpportunitySupportInnerVo getOpportunitySupport(OpportunitySupportInnerDto supportInnerDto);
}

/* Ended by AICoder, pid:b2ee2871bec2b68145a8093700ab6a16a68354bc */