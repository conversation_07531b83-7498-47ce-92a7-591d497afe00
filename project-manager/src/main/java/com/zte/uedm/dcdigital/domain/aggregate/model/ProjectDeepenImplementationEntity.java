package com.zte.uedm.dcdigital.domain.aggregate.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 项目深化实施实体
 */
@Getter
@Setter
@ToString
public class ProjectDeepenImplementationEntity {

    /**
     * 主键id所属项目id，一个项目只有一个深化实施
     */
    private String id;

    /**
     * 设计负责人
     */
    private String designDirector;

    /**
     * 启动时间，非必填：时间选择，精确到天
     */
    private String startTime;

    /**
     * 要求提资完成时间，非必填：时间选择，精确到天
     */
    private String requestInformationCompletionTime;

    /**
     * 实际提资完成时间，非必填：时间选择，精确到天
     */
    private String actualInformationCompletionTime;

    /**
     * 深化设计开始时间，非必填：时间选择，精确到天
     */
    private String deepenDesignStartTime;

    /**
     * 深化设计结束时间，非必填：时间选择，精确到天
     */
    private String deepensDesignEndTime;

    /**
     * 合图开始时间，非必填：时间选择，精确到天
     */
    private String combinedImageStartTime;

    /**
     * 合图结束时间，非必填：时间选择，精确到天
     */
    private String combinedImageEndTime;

    /**
     * BIM专项设计开始时间，非必填：时间选择，精确到天
     */
    private String bIMSpecialDesignStartTime;

    /**
     * BIM专项设计结束时间，非必填：时间选择，精确到天
     */
    private String bIMSpecialDesignEndTime;

    /**
     * 首批备料清单输出时间，非必填：时间选择，精确到天
     */
    private String firstBatchMaterialPreparationOutputTime;

    /**
     * 长周期物料清单输出时间，非必填：时间选择，精确到天
     */
    private String longPeriodMaterialListOutputTime;

    /**
     * 整体备料清单锁定时间，非必填：时间选择，精确到天
     */
    private String overallMaterialPreparationLockTime;

    /**
     * 工程深化进度，必填，0-100
     */
    private Integer engineeringDeepenProgress;

    /**
     * 深化设计状态，必填，下拉框选择：A正常；B延误（客户需求变化）；B延误（产品原因）；B延误（商务原因）；B延误（其它）
     */
    private String deepenDesignStatus;

    /**
     * 延误原因，必填（仅延误时可填写），1000字符
     */
    private String delayReason;

    /**
     * 施工图修订开始时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingRevisionStartTime;

    /**
     * 修改深化设计开始时间，非必填：时间选择，精确到天
     */
    private String modifyDeepenDesignStartTime;

    /**
     * 修改深化设计结束时间，非必填：时间选择，精确到天
     */
    private String modifyDeepenDesignEndTime;

    /**
     * 施工图校正开始时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingCalibrationStartTime;

    /**
     * 施工图校正结束时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingCalibrationEndTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private String updateTime;
}
