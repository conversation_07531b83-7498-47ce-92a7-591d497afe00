package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectStageScoreEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectDashboardRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectDashboardMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.TimeRangeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProjectDashboardRepositoryImpl implements ProjectDashboardRepository {

    @Autowired
    private ProjectDashboardMapper projectDashboardMapper;

    @Override
    public List<ProjectStageScoreEntity> listProjectScore(TimeRangeDto timeRange, List<String> projectIds) {
        return projectDashboardMapper.listProjectScore(timeRange, projectIds);
    }

    @Override
    public List<IdNameBean> selectGuideProductCategories(String projectId) {
        return projectDashboardMapper.selectGuideProductCategories(projectId);
    }

    @Override
    public List<ProjectStageScoreEntity> selectLineChartData(String projectId, String productCategoryId) {
        return projectDashboardMapper.selectLineChartData(projectId, productCategoryId);
    }
}
