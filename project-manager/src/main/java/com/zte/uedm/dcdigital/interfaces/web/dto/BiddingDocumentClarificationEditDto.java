/* Started by AICoder, pid:f39a1s84ef72d4a149580bc96047d439a0953f2d */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 用于编辑招标文件澄清的DTO类。
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BiddingDocumentClarificationEditDto extends BiddingDocumentClarificationAddDto {

    /**
     * 澄清ID，必须字段。
     */
    @NotBlank(message = "ID is required")
    @LogMark(range = {OperationMethodEnum.UPDATE})
    private String id;
}
/* Ended by AICoder, pid:f39a1s84ef72d4a149580bc96047d439a0953f2d */
