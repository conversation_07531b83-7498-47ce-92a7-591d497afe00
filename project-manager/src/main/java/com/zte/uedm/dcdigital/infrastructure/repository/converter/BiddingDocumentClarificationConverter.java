/* Started by AICoder, pid:11ecag1e19yf78714bac09a0907add3b2698dfce */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.BiddingDocumentClarificationObj;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingDocumentClarificationPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface BiddingDocumentClarificationConverter {

    // 使用常量 INSTANCE 以便通过 Mappers 工厂获取单例实例
    BiddingDocumentClarificationConverter INSTANCE = Mappers.getMapper(BiddingDocumentClarificationConverter.class);

    /**
     * 将 BiddingDocumentClarificationPo 转换为 BiddingDocumentClarificationObj。
     *
     * @param po 需要转换的 BiddingDocumentClarificationPo 对象
     * @return 转换后的 BiddingDocumentClarificationObj 对象
     */
    BiddingDocumentClarificationObj po2Obj(BiddingDocumentClarificationPo po);

    /**
     * 将 BiddingDocumentClarificationPo 列表转换为 BiddingDocumentClarificationObj 列表。
     *
     * @param poList 需要转换的 BiddingDocumentClarificationPo 对象列表
     * @return 转换后的 BiddingDocumentClarificationObj 对象列表
     */
    List<BiddingDocumentClarificationObj> poList2ObjList(List<BiddingDocumentClarificationPo> poList);

    /**
     * 将 BiddingDocumentClarificationObj 转换为 BiddingDocumentClarificationPo。
     *
     * @param obj 需要转换的 BiddingDocumentClarificationObj 对象
     * @return 转换后的 BiddingDocumentClarificationPo 对象
     */
    BiddingDocumentClarificationPo obj2Po(BiddingDocumentClarificationObj obj);
}

/* Ended by AICoder, pid:11ecag1e19yf78714bac09a0907add3b2698dfce */