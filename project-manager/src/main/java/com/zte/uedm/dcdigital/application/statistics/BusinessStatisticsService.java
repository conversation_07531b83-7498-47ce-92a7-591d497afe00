package com.zte.uedm.dcdigital.application.statistics;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticsVo;

import java.util.List;
import java.util.Map;

/**
 * 商机统计服务接口
 * 
 * <AUTHOR> Assistant
 */
public interface BusinessStatisticsService {

    /**
     * 获取所有地区下级商机统计数据
     *
     * 传入天类型时间节点范围或者天单个时间节点，查询时间范围内或者截止到该时间点的下级商机列表数据。
     * 这个接口不能直接查询商机的日周月年表，需要通过现有商机业务逻辑去project模块的表中将数据查询整合出来，
     * 地区需要递归去处理，先查最底层地区的数据，然后汇聚给父节点地区，一直这样操作直到汇聚到最顶层，
     * 最后输出一个所有地区的统计数据列表，这个列表数据能要直接入库到商机资产日表中。
     *
     * @param queryDto 查询条件
     * @return 所有地区的商机统计数据列表
     */
    List<BusinessStatisticsVo> getAreaBusinessStatistics(BusinessStatisticsQueryDto queryDto);

    /**
     * 获取所有地区信息映射
     *
     * @return Map<地区ID, 地区名称>
     */
    Map<String, String> getAllAreaMap();
}
