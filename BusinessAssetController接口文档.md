# BusinessAssetController 接口文档

## 概述

商机资产统计控制器，提供商机数量统计、数据导出和埋点处理功能。

- **控制器类名**: `BusinessAssetController`
- **基础路径**: `/uportal/asset`
- **API标签**: 商机资产统计接口

---

## 接口列表

### 1. 商机数量统计和变化统计

#### 基本信息
- **接口路径**: `POST /uportal/asset/statBusiness`
- **接口描述**: 按时间维度统计商机总数和变化情况，支持天/周/月/年维度，支持下级商机变化查询
- **请求方式**: POST
- **Content-Type**: `application/json`
- **Response-Type**: `application/json`

#### 请求参数 (BusinessAssetQueryDto)

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| startTime | String | 否 | 开始时间，格式根据timeType而定：<br/>• timeType=1(天): yyyyMMdd (如: 20250105)<br/>• timeType=2(周): yyyyww (如: 202530)<br/>• timeType=3(月): yyyyMM (如: 202505)<br/>• timeType=4(年): yyyy (如: 2025) | "20250105" |
| endTime | String | 否 | 结束时间，格式同startTime | "20250131" |
| areaId | String | 否 | 区域ID | "area001" |
| timeType | Integer | 否 | 时间类型：1-天，2-周，3-月，4-年 | 1 |
| timePoint | String | 否 | 时间点：下级列表展示的具体时间点，格式根据timeType而定，该时间点必须在startTime和endTime范围内 | "20250105" |

#### 响应参数 (BaseResult<BusinessAssetStatVo>)

**BaseResult 通用响应结构**:
```json
{
  "code": "string",     // 响应状态码
  "message": "string",  // 响应消息
  "data": {}           // 业务数据
}
```

**BusinessAssetStatVo 业务数据结构**:
```json
{
  "timeStatList": [     // 时间范围内的汇总统计数据（按时间节点）
    {
      "timeNode": "string",        // 时间节点（例：20250620、202506、2025）
      "projectAddNum": 0,          // 新增商机数
      "projectStartNum": 0         // 启动投标数
    }
  ],
  "areaStatList": [     // 下级地区统计数据（指定时间点的快照）
    {
      "areaName": "string",           // 地区名称
      "allNum": 0,                    // 总数
      "projectAddNum": 0,             // 新增商机数
      "bidNum": 0,                    // 投标阶段数
      "projectStartNum": 0,           // 启动投标数
      "subBidNum": 0,                 // 交标阶段数
      "beforeBidNum": 0,              // 标前阶段数
      "projectApprovalNum": 0         // 立项阶段数
    }
  ]
}
```

#### 请求示例
```json
{
  "startTime": "20250101",
  "endTime": "20250131",
  "areaId": "area001",
  "timeType": 1,
  "timePoint": "20250115"
}
```

#### 响应示例
```json
{
  "code": "200",
  "message": "success",
  "data": {
    "timeStatList": [
      {
        "timeNode": "20250101",
        "projectAddNum": 15,
        "projectStartNum": 8
      },
      {
        "timeNode": "20250102",
        "projectAddNum": 12,
        "projectStartNum": 6
      }
    ],
    "areaStatList": [
      {
        "areaName": "北京",
        "allNum": 100,
        "projectAddNum": 15,
        "bidNum": 25,
        "projectStartNum": 8,
        "subBidNum": 20,
        "beforeBidNum": 18,
        "projectApprovalNum": 14
      }
    ]
  }
}
```

---

### 2. 商机统计数据导出

#### 基本信息
- **接口路径**: `POST /uportal/asset/statBusinessExport`
- **接口描述**: 导出商机统计数据为Excel文件
- **请求方式**: POST
- **Content-Type**: `application/json`
- **Response-Type**: `application/octet-stream`

#### 请求参数 (BusinessAssetQueryDto)
参数结构与统计接口相同，详见上方 BusinessAssetQueryDto 说明。

#### 响应参数
直接返回Excel文件流，无JSON响应体。文件包含以下字段：
- 时间
- 地区
- 商机总数
- 新增商机
- 投标阶段数
- 启动投标数
- 交标阶段数
- 标前阶段数

#### 请求示例
```json
{
  "startTime": "20250101",
  "endTime": "20250131",
  "areaId": "area001",
  "timeType": 3
}
```

#### 响应说明
- 成功时直接返回Excel文件流
- 失败时抛出RuntimeException异常

---

### 3. 前端埋点接口

#### 基本信息
- **接口路径**: `POST /uportal/asset/buryingPoints`
- **接口描述**: 处理商机启动投标埋点数据
- **请求方式**: POST
- **Content-Type**: `application/json`
- **Response-Type**: `application/json`

#### 请求参数 (AssetBuryingPointDto)

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| projectId | String | 是 | 商机ID | "project123" |
| areaId | String | 是 | 地区ID | "area001" |

#### 响应参数 (BaseResult<String>)

**BaseResult<String> 响应结构**:
```json
{
  "code": "string",     // 响应状态码
  "message": "string",  // 响应消息
  "data": "string"      // 处理结果消息
}
```

#### 请求示例
```json
{
  "projectId": "project123",
  "areaId": "area001"
}
```

#### 响应示例

**成功响应**:
```json
{
  "code": "200",
  "message": "success",
  "data": "Burying point data processed successfully"
}
```

**失败响应**:
```json
{
  "code": "400",
  "message": "failed",
  "data": "商机ID不能为空"
}
```

---

## 错误码说明

| 状态码 | 错误信息 | 说明 | 接口 |
|--------|----------|------|------|
| 200 | success | 请求成功 | 所有接口 |
| 400 | 商机ID不能为空 | projectId参数为空或空字符串 | 埋点接口 |
| 400 | 地区ID不能为空 | areaId参数为空或空字符串 | 埋点接口 |
| 500 | Failed to get business asset statistics | 统计数据获取过程中发生异常 | 统计接口 |
| 500 | Failed to export business asset statistics | 数据导出过程中发生异常 | 导出接口 |
| 500 | Failed to process burying point data | 埋点数据处理过程中发生异常 | 埋点接口 |

---

## 重要说明

### 时间格式说明
不同的 `timeType` 对应不同的时间格式：

| timeType | 含义 | 时间格式 | 示例 |
|----------|------|----------|------|
| 1 | 天 | yyyyMMdd | 20250105 |
| 2 | 周 | yyyyww | 202530 |
| 3 | 月 | yyyyMM | 202505 |
| 4 | 年 | yyyy | 2025 |

### 业务逻辑说明

1. **统计接口**：
   - 支持按不同时间维度进行统计
   - `timeStatList` 返回时间范围内每个时间节点的汇总数据
   - `areaStatList` 返回指定时间点各地区的详细统计数据

2. **导出接口**：
   - 基于统计接口的数据生成Excel文件
   - 文件格式为 `.xlsx`
   - 包含时间、地区和各项统计指标

3. **埋点接口**：
   - 用于记录用户行为数据
   - 采用异步处理方式
   - 主要记录商机启动投标的操作

### 注意事项

1. **参数校验**：埋点接口对必填参数进行严格校验
2. **异步处理**：埋点数据采用异步处理，响应成功不代表处理完成
3. **异常处理**：所有接口都有完善的异常处理机制
4. **日志记录**：所有操作都会记录详细的日志信息
5. **文件下载**：导出接口返回文件流，前端需要处理文件下载逻辑

---

## 技术实现

- **框架**: Spring Boot + JAX-RS
- **数据库**: PostgreSQL
- **日志**: SLF4J + Logback
- **文档**: Swagger注解
- **异步处理**: Spring异步方法
- **文件导出**: EasyExcel

---

*文档生成时间: 2025-01-28*
*版本: v1.0*
