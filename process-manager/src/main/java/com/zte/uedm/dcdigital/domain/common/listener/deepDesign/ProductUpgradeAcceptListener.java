/* Started by AICoder, pid:md414c3d1ekc280146a30ad800ceff5783f60cbe */
package com.zte.uedm.dcdigital.domain.common.listener.deepDesign;

import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component("ProductUpgradeAcceptListener")
public class ProductUpgradeAcceptListener implements TaskListener {
    @Autowired
    @Qualifier("approvalServiceImpl")
    private ApprovalService approvalService;

    private static ProductUpgradeAcceptListener productUpgradeAcceptListener;

    @PostConstruct
    public void init() {
        productUpgradeAcceptListener = this;
        productUpgradeAcceptListener.approvalService = this.approvalService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        // 获取当前流程实例ID
        String processInstanceId = delegateTask.getProcessInstanceId();

        // 获取产品分类路径变量
        String pathName = (String) delegateTask.getVariable(TaskConstants.PRODUCT_CATEGORY_PATH);

        // 获取设计负责人变量
        String designLeader = (String) delegateTask.getVariable(ProcessConstants.DESIGN_LEADER);

        // 设置设计负责人为任务处理人
        delegateTask.setAssignee(designLeader);

        // 构建审批标题
        String approvalTitle = pathName + TaskConstants.PRODUCT_UPGRADE;

        // 创建并配置审批实体
        ApprovalEntity approvalEntity = new ApprovalEntity();
        approvalEntity.setFlowId(processInstanceId);
        approvalEntity.setTitle(approvalTitle);

        // 更新审批信息
        productUpgradeAcceptListener.approvalService.updateApprovalInfo(approvalEntity);
    }
}

/* Ended by AICoder, pid:md414c3d1ekc280146a30ad800ceff5783f60cbe */