/* Started by AICoder, pid:u281ep9945tf5a8140860971b01f511b36896b21 */
package com.zte.uedm.dcdigital.application.approval.executor.impl;

import com.zte.uedm.dcdigital.application.approval.executor.ManualApprovalCommandService;
import com.zte.uedm.dcdigital.domain.model.approval.ManualApprovalProjectRequest;
import com.zte.uedm.dcdigital.domain.service.ManualApprovalService;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ManualApprovalCommandServiceImpl implements ManualApprovalCommandService {

    @Autowired
    private ManualApprovalService manualApprovalService;

    @Override
    public void addManualApproval(ManualApprovalProjectRequest approvalDto) {
        manualApprovalService.addManualApproval(approvalDto);
    }

    @Override
    public void acceptManualApproval(ApprovalSubmitDto submitDto) {
        manualApprovalService.acceptManualApproval(submitDto);
    }

    @Override
    public MktApprovalDetailVo getMktApprovalDetail(String flowId) {
        return manualApprovalService.getMktApprovalDetail(flowId);
    }

    @Override
    public ApprovalVo getApprovalById(String approvalId) {
        return manualApprovalService.getApprovalById(approvalId);
    }

    @Override
    public void cancelMktTask(String projectId, String productCategoryId) {
         manualApprovalService.cancelMktTask(projectId, productCategoryId);
    }
}

/* Ended by AICoder, pid:u281ep9945tf5a8140860971b01f511b36896b21 */