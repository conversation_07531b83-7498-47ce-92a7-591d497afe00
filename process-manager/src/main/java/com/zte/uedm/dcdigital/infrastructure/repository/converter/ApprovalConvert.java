package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.process.MaterialToApprovalDto;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalMaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalMaterialWithCostVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalMaterialWithDeliveryDaysVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ApprovalConvert {

    ApprovalConvert INSTANCE = Mappers.getMapper(ApprovalConvert.class);

    /* Started by AICoder, pid:jc994p49bfvd3e2148500a4830b7231fbc275730 */
    /**
     * 将 ApprovalEntity 转换为 ApprovalPo。
     *
     * @param approvalEntity 需要转换的 ApprovalEntity 对象。
     * @return 转换后的 ApprovalPo 对象。
     */
    @Mappings({})
    ApprovalPo approvalEntityToPo(ApprovalEntity approvalEntity);

    /**
     * 将 ApprovalPo 转换为 ApprovalEntity。
     *
     * @param approvalPo 需要转换的 ApprovalPo 对象。
     * @return 转换后的 ApprovalEntity 对象。
     */
    @Mappings({})
    ApprovalEntity approvalPoToEntity(ApprovalPo approvalPo);
    /* Ended by AICoder, pid:jc994p49bfvd3e2148500a4830b7231fbc275730 */

    ApprovalObj convertPo2Obj(ApprovalPo approvalPo);
    List<ApprovalObj> convertPos2Objs(List<ApprovalPo> approvalPos);
    ApprovalVo convertObj2Vo(ApprovalObj approvalObj);
    List<ApprovalMaterialVo> convertMaterialDtos2MaterialVos(List<MaterialToApprovalDto> dtos);
    List<ApprovalMaterialWithCostVo> convertMaterialDtos2MaterialWithCostVos(List<MaterialToApprovalDto> dtos);
    List<ApprovalMaterialWithDeliveryDaysVo> convertMaterialDtos2MaterialWithDeliveryDaysVos(List<MaterialToApprovalDto> dtos);

    List<ApprovalEntity> approvalPoToListEntity(List<ApprovalPo> approvalPo);

    List<ApprovalVo> convertObjToVos(List<ApprovalObj> approvalObjList);

    List<ApprovalCommonVo> convertObj2CommVo(List<ApprovalObj> approvalObjList);
    ApprovalCommonVo singleConvertObj2CommVo(ApprovalObj approvalObj);
}
