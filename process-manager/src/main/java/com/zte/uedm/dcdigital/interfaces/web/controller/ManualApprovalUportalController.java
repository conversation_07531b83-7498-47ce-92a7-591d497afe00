/* Started by AICoder, pid:p30694409cl00e8147c80bdf407e6f6a9db3ab9c */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.approval.executor.ApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.ApprovalQueryService;
import com.zte.uedm.dcdigital.application.approval.executor.ManualApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.model.approval.ManualApprovalProjectRequest;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

/**
 * 工程量清单审批流程相关Controller，处理工程量清单的手动下发评审单创建。
 */
@Path("uportal/manual/approval")
@Controller
public class ManualApprovalUportalController {

    /**
     * 用于处理审批命令的服务。
     */
    @Autowired
    private ApprovalCommandService approvalCommandService;

    /**
     * 用于处理手动审批命令的服务。
     */
    @Autowired
    private ManualApprovalCommandService manualApprovalCommandService;

    /**
     * 用于查询审批信息的服务。
     */
    @Autowired
    private ApprovalQueryService approvalQueryService;

    @Autowired
    private DcTaskService dcTaskService;

    /**
     * 创建一个新的手动下发工程量清单流程任务。
     *
     * @param approvalDto 包含评审单详细信息的数据传输对象
     * @return 包含新创建评审单ID的BaseResult对象
     */
    @POST
    @Path("/generat-task")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-approval-manager",
            targetClass = ManualApprovalProjectRequest.class, operation = "ManualApprovalAdd", mapperName = ApprovalMapper.class)
    @ApiOperation(value = "创建'工程量清单'手动下发评审单", notes = "创建'工程量清单' 手动下发评审单", httpMethod = "POST")
    public BaseResult<String> createMktApproval(@Valid ManualApprovalProjectRequest approvalDto) {
        manualApprovalCommandService.addManualApproval(approvalDto);
        return BaseResult.success();
    }

    /**
     * 处理手动下发工程量清单任务。
     *
     * @param submitDto 包含处理评审单详细信息的数据传输对象
     * @return 包含新创建评审单ID的BaseResult对象
     */
    @POST
    @Path("/manual-accept")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "处理'工程量清单'手动下发评审单", notes = "处理'工程量清单' 手动下发评审单", httpMethod = "POST")
    public BaseResult<String> acceptManualApproval(ApprovalSubmitDto submitDto) {
        manualApprovalCommandService.acceptManualApproval(submitDto);
        return BaseResult.success();
    }
    /**
     * 查询手动下发工程量清单任务详情。
     *
     * @param flowId 流程id
     * @return 包含新创建评审单ID的BaseResult对象
     */
    @GET
    @Path("/manual-task-detail")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据流程id查询手动下发'市场投标支持'系列任务详情", notes = "根据流程id查询手动下发'市场投标支持'详情", httpMethod = "GET")
    public BaseResult<MktApprovalDetailVo> getManualTaskDetail(@QueryParam("flowId") String flowId){
        MktApprovalDetailVo vo=manualApprovalCommandService.getMktApprovalDetail(flowId);
        return BaseResult.success(vo);
    }

    @GET
    @Path("/manual-flow-detail")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据ID查询评审单流程详情", notes = "根据ID查询评审单流程详情", httpMethod = "GET")
    public BaseResult<ApprovalVo> getApprovalById(@QueryParam("approvalId") String approvalId){
        ApprovalVo approval = manualApprovalCommandService.getApprovalById(approvalId);
        return BaseResult.success(approval);
    }

    @POST
    @Path("/stop-remove")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "停止历史市场评审单流程", notes = "停止历史市场评审单流程", httpMethod = "POST")
    public BaseResult<ApprovalVo> removeHistoryMktApproval(MktApprovalCancelDto approvalCancelDto){
        dcTaskService.cancelMarketBidApproval(approvalCancelDto);
        return BaseResult.success();
    }
    /* Started by AICoder, pid:223ca08ae0fd032147f90b48004b021b65913863 */
    @POST
    @Path("/cancel-mkt-task")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "取消市场投标支持任务", notes = "取消市场投标支持任务", httpMethod = "POST")
    public BaseResult<ApprovalVo> cancelMktApproval(
            @QueryParam("projectId") String projectId,
            @QueryParam("productCategoryId") String productCategoryId) {
        manualApprovalCommandService.cancelMktTask(projectId, productCategoryId);
        return BaseResult.success();
    }

    /* Ended by AICoder, pid:223ca08ae0fd032147f90b48004b021b65913863 */
}

/* Ended by AICoder, pid:p30694409cl00e8147c80bdf407e6f6a9db3ab9c */