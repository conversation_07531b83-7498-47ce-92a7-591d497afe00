/* Started by AICoder, pid:qe877e928d2c25f143070ac690c5e85a0706bb1d */
package com.zte.uedm.dcdigital.domain.common.constant;

import java.util.*;

public final class MktTaskConstants {

    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String FOUR = "4";
    public static final String ASC_ORDER = "ASC";
    /**  下面定义了一系列的flowable流程中的针对市场投标支持任务的常量key*/
    //"市场投标支持"主任务对应的子任务个数
    public static final String VERIFY_COUNT = "verificationCount";
    //文档编写任务数量
    public static final String DOCUMENT_TASK_NUM = "document_task_num";
    //物料选型任务数量
    public static final String MATERIEL_TASK_NUM = "materiel_task_num";
    //标书澄清任务数量
    public static final String TENDER_TASK_NUM = "tender_task_num";
    //文档编写处理人变量
    public static final String DOCUMENT_CANDIDATE = "document_candidate";
    //物料选型处理人变量
    public static final String MATERIEL_CANDIDATE = "materiel_candidate";
    //标书澄清处理人变量
    public static final String TENDER_CANDIDATE = "tender_candidate";

    // 产品SE角色编码
    public static final String MKT_BID_ROLE_CODE = "market-bid-product-se";

    //标书分解（市场投标支持）
    public static final String BREAKDOWN_TENDER_ID = "sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54";

    // 主任务节点ID（市场投标支持）
    public static final String MAIN_TASK_ID = "sid-B08CA7D5-E32A-48AC-8351-C28E263554C1";

    // 补充主任务节点ID（市场投标支持)
    public static final String REP_MAIN_TASK_ID = "sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF";

    public static final String SPONSOR_TASK_ID = "sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54";//市场投标支持发起人任务静态Id
    public static final String MATERIAL_SELECTION_ID ="sid-A428E5F8-2B5A-437A-9F09-201382A49772";//物料选型
    public static final String BID_CLARIFICATION_ID = "sid-65571342-7185-4FBA-B817-47526C512BFE";//标书澄清
    public static final String DOCUMENTATION_ID = "sid-E568363F-04EA-4304-A202-B60A6CAE6A36";// 文档编写
    public static final String MATERIAL_SELECTION_VERIFY_ID ="sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2";//物料选型验证
    public static final String BID_CLARIFICATION_VERIFY_ID = "sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C";//标书澄清验证
    public static final String DOCUMENTATION_VERIFY_ID = "sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C";// 文档编写验证

    public static final String MATERIAL_SELECTION_AUTH_ID ="sid-C7E61921-76B2-49C1-B99F-78F882E8B021";//物料选型通过
    public static final String BID_CLARIFICATION_AUTH_ID = "sid-7A790D28-CF41-4302-AA8A-DEAB9C111868";//标书澄清通过
    public static final String DOCUMENTATION_AUTH_ID = "sid-30DD5D5F-48E4-4AF5-A14A-7B6D5ECED710";// 文档编写通过

    public static final String MATERIAL_SELECTION_REJECT_ID ="sid-CAAAD52A-9B2B-4BD5-8240-A431B9E246AA";//物料选型不通过
    public static final String BID_CLARIFICATION_REJECT_ID = "sid-A073A467-66C0-4EA7-8FEA-B1FB60D50D70";//标书澄清不通过
    public static final String DOCUMENTATION_REJECT_ID = "sid-C8F50371-AF9A-49A0-B6FA-C4E692BF1F2E";// 文档编写不通过
    // 需要筛选的目标任务定义Key集合
    public static final Set<String> SUB_TASK_IDS = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList(
                    "sid-A428E5F8-2B5A-437A-9F09-201382A49772", // 物料选型
                    "sid-65571342-7185-4FBA-B817-47526C512BFE", // 标书澄清
                    "sid-E568363F-04EA-4304-A202-B60A6CAE6A36"  // 文档编写
            ))
    );

    // 验证任务定义Key集合
    public static final Set<String> VERIFY_TASK_IDS = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList(
                    "sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C", // 文档编写验证
                    "sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2", // 物料选型验证
                    "sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C"  // 标书澄清验证
            ))
    );

    // 目标任务Key集合
    public static final Set<String> TARGET_TASK_KEYS = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList(
                    "sid-A428E5F8-2B5A-437A-9F09-201382A49772", // 物料选型
                    "sid-65571342-7185-4FBA-B817-47526C512BFE", // 标书澄清
                    "sid-E568363F-04EA-4304-A202-B60A6CAE6A36",  // 文档编写
                    "sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF"   // 市场投标支持
            ))
    );

    // 私有构造函数防止实例化
    private MktTaskConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}

/* Ended by AICoder, pid:qe877e928d2c25f143070ac690c5e85a0706bb1d */