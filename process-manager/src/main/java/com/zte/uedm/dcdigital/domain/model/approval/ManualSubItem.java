/* Started by AICoder, pid: */
package com.zte.uedm.dcdigital.domain.model.approval;

import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
public class ManualSubItem {
    // 产品小类id
    @LogMark(range = {OperationMethodEnum.ADD})
    @NotBlank
    private String subcategoryId;

    // 任务类型合集
    @LogMark(range = {OperationMethodEnum.ADD})
    @NotEmpty
    private List<Integer> approvalTypes;
}

/* Ended by AICoder, pid: */