/* Started by AICoder, pid:65f9ea5aaei3916140800b8b90e8121001b9022b */
package com.zte.uedm.dcdigital.domain.common.event;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContextHolder implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringContextHolder.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }
}

/* Ended by AICoder, pid:65f9ea5aaei3916140800b8b90e8121001b9022b */