/* Started by AICoder, pid:w4554m0145u4152146230829c00d5a6e3892d35f */
package com.zte.uedm.dcdigital.domain.common.listener.deepDesign;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class ProductUpgradeAcceptDoNotPassListener implements ExecutionListener {
    @Autowired
    @Qualifier("approvalServiceImpl")
    private ApprovalService approvalService;

    private static ProductUpgradeAcceptDoNotPassListener productUpgradeAcceptDoNotPassListener;

    @PostConstruct
    public void init() {
        productUpgradeAcceptDoNotPassListener = this;
        productUpgradeAcceptDoNotPassListener.approvalService = this.approvalService;
    }

    /**
     * 此监听器作用是验证任务不通过时设置回退到上一级任务的处理人
     */
    @Override
    public void notify(DelegateExecution delegateExecution) {
        // 获取流程实例ID
        String processInstanceId = delegateExecution.getProcessInstanceId();

        // 获取流程变量
        String pathName = (String) delegateExecution.getVariable(TaskConstants.PRODUCT_CATEGORY_PATH);
        String actualAssignee = (String) delegateExecution.getVariable(ProcessConstants.PRODUCT_UPGRADE_USER);

        // 恢复实际任务处理人
        delegateExecution.setVariable(ProcessConstants.MANUAL_USER, actualAssignee);

        // 构建审批标题
        String approvalTitle = pathName + TaskConstants.PRODUCT_UPGRADE;

        // 更新审批信息
        ApprovalEntity approvalEntity = new ApprovalEntity();
        approvalEntity.setFlowId(processInstanceId);
        approvalEntity.setTitle(approvalTitle);
        productUpgradeAcceptDoNotPassListener.approvalService.updateApprovalInfo(approvalEntity);

        // 验证流程实例存在性
        if (processInstanceId == null) {
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }
    }
}

/* Ended by AICoder, pid:w4554m0145u4152146230829c00d5a6e3892d35f */