package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.interfaces.web.vo.CommentVo;
import org.flowable.engine.history.HistoricActivityInstance;

import java.util.List;

public interface MktTaskHistoryFunctionService {
    /**
     *  根据流程实例id查询流程处理的节点组合列表
     * */
    public List<String> getActivityIds(String staticTaskId);
    /**
     *  根据流程实例id与流程节点组合查询流程处理历史
     * */
    public List<HistoricActivityInstance> getMktFlowDataHistory(String flowId, List<String> targetActivityIds);
    /**
     *  将流程处理历史转换为自定义的列表类
     * */
    public List<CommentVo> getCommentList(List<HistoricActivityInstance> historicActivityInstances);
}
