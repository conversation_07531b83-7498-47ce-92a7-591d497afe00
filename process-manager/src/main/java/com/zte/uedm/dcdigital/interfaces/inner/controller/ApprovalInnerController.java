package com.zte.uedm.dcdigital.interfaces.inner.controller;

import com.zte.uedm.dcdigital.application.approval.executor.ApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.ApprovalQueryService;
import com.zte.uedm.dcdigital.application.approval.executor.ManualApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.MktApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.process.*;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ApprovalMapper;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Controller
@Path("/station-inner")
public class ApprovalInnerController {

    @Autowired
    private ApprovalCommandService  approvalCommandService;

    @Autowired
    private MktApprovalCommandService mktApprovalCommandService;

    @Autowired
    private ApprovalQueryService approvalQueryService;

    /**
     * 用于处理手动审批命令的服务。
     */
    @Autowired
    private ManualApprovalCommandService manualApprovalCommandService;

    @POST
    @Path("/create-approval")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-approval-manager",
            targetClass = NewApprovalDto.class, operation = "ApprovalAdd",mapperName= ApprovalMapper.class)
    @ApiOperation(value = "创建评审单", notes = "创建评审单", httpMethod = "POST")
    public BaseResult<String> createApproval( NewApprovalDto newApprovalDto) {
        String id = approvalCommandService.createApproval(newApprovalDto);
        return BaseResult.success(id);
    }

    @POST
    @Path("/cancel-mkt-approval")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "撤销'市场投标支持' 评审单", notes = "撤销'市场投标支持' 评审单", httpMethod = "POST")
    public BaseResult<String> cancelMktApproval(MktApprovalCancelDto cancelDto) {
        mktApprovalCommandService.cancelMarketBidApproval(cancelDto);
        return BaseResult.success();
    }
    @POST
    @Path("/create-mkt-approval")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-approval-manager",
            targetClass = MktApprovalAddDto.class, operation = "MktApprovalAdd",mapperName= ApprovalMapper.class)
    @ApiOperation(value = "创建'市场投标支持' 评审单", notes = "创建'市场投标支持' 评审单", httpMethod = "POST")
    public BaseResult<String> createMktApproval(MktApprovalAddDto approvalDto) {
        String id = mktApprovalCommandService.createMarketBidApproval(approvalDto);
        return BaseResult.success(id);
    }

    @POST
    @Path("/select-market-bid-info")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询'市场投标支持'审批单中产品小类与市场产品se信息", notes = "查询'市场投标支持'审批单中产品小类与市场产品se信息", httpMethod = "POST")
    public BaseResult<List<MktApprovalInfoVo>> selectMarketBidInfo( MktApprovalInfoQueryDto mtkDto) {
        List<MktApprovalInfoVo> voList = approvalQueryService.selectMarketBidInfo(mtkDto);
        return BaseResult.success(voList);
    }

    @GET
    @Path("/project/query-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<ApprovalCommonVo>> queryProjectIdByAreaId1(@QueryParam("projectId") String projectId) {
        List<ApprovalCommonVo> approvalCommonVos = approvalQueryService.queryApprovalByProject(projectId);
        return BaseResult.success(approvalCommonVos);
    }

    @GET
    @Path("/project/query-by-approvalId")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<ApprovalCommonVo> getApprovalByApprovalId(@QueryParam("approvalId") String approvalId) {

        return BaseResult.success( approvalQueryService.getApprovalByApprovalId(approvalId));
    }

    @POST
    @Path("/project/query-by-lectotype-ids")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<ApprovalCommonVo>> queryApprovalByLectotypeIdList(@QueryParam("lectotypeIdList") List<String> lectotypeIdList) {
        List<ApprovalCommonVo> approvalCommonVos = approvalQueryService.queryApprovalByLectotypeIdList(lectotypeIdList);
        return BaseResult.success(approvalCommonVos);
    }

    @GET
    @Path("/project/query-by-lectotype-id")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<ApprovalCommonVo> queryApprovalByLectotypeId(@QueryParam("lectotypeId") String lectotypeId) {
        return BaseResult.success(approvalQueryService.queryApprovalByLectotypeId(lectotypeId));
    }

    /* Started by AICoder, pid:e8203uf3f4w478314601085bf091321edd908d9a */
    @POST
    @Path("/cancel-mkt-task")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "取消市场投标支持任务", notes = "取消市场投标支持任务", httpMethod = "POST")
    public BaseResult<ApprovalVo> cancelMktApproval(
            @QueryParam("projectId") String projectId,
            @QueryParam("productCategoryId") String productCategoryId) {
        manualApprovalCommandService.cancelMktTask(projectId, productCategoryId);
        return BaseResult.success();
    }

    /* Ended by AICoder, pid:e8203uf3f4w478314601085bf091321edd908d9a */


}
