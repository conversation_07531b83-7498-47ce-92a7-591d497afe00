/* Started by AICoder, pid:96339c3a0ac4407147d70bcd70dd6b28fc721d1a */
package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoQueryDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoVo;
import com.zte.uedm.dcdigital.domain.model.approval.MktApprovalAssocInfoPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.MktApprovalAssocInfoDto;

import java.util.List;

public interface MktApprovalAssocInfoRepository {
    /**
     * 根据传入的DTO查询市场投标信息。
     *
     * @param mtkDto 用于查询的DTO对象
     * @return 包含查询结果的VO列表
     */
    List<MktApprovalInfoVo> selectMarketBidInfo(MktApprovalInfoQueryDto mtkDto);

    /**
     * 添加市场投标信息。
     *
     * @param mtkDto 包含要添加信息的DTO对象
     */
    void addMarketBidInfo(MktApprovalAssocInfoDto mtkDto);

    /**
     * 更新市场投标信息。
     *
     * @param mtkDto 包含要更新的DTO对象
     */
    void updateMarketBidInfo(MktApprovalAssocInfoDto mtkDto);

    /**
     * "查询'市场投标支持'审批单中产品小类与市场产品se信息"
     * @param flowId
     * */
    MktApprovalAssocInfoPo getMktApprovalDetail(String flowId);
}

/* Ended by AICoder, pid:96339c3a0ac4407147d70bcd70dd6b28fc721d1a */