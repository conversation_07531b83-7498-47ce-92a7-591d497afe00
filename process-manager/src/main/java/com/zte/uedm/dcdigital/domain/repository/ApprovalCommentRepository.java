package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalCommentPo;

import java.util.List;

public interface ApprovalCommentRepository {

    /* Started by AICoder, pid:je30125bd2a6cd214c3f09f9a069d902a297c703 */
    /**
     * 添加审批评论信息。
     *
     * @param commentEntity 包含审批评论详细信息的实体对象。
     * @return 如果添加成功，返回 `true`；否则返回 `false`。
     */
    public boolean addCommentInfo(ApprovalCommentEntity commentEntity);
    /* Ended by AICoder, pid:je30125bd2a6cd214c3f09f9a069d902a297c703 */

    /**
     * 根据taskId查询审批结果。
     *
     * @param taskIds 包含审批评论详细信息的实体对象。
     * @return 对应审批列表。
     */
    public List<ApprovalCommentPo> getCommentInfoByTaskIds(List<String> taskIds);

    /* Started by AICoder, pid:ja672e70a70977f146810be29033291f0ec2f905 */
    /**
     * 查询审批评论实体列表。
     *
     * <p>
     * 该方法接收一个包含审批ID的列表，并返回与这些审批相关的所有评论实体列表。
     * 如果传入的审批ID列表为空或null，方法将返回一个空列表。
     * </p>
     *
     * @param approvalIds 一个包含审批ID的列表。如果为空或null，将返回一个空列表。
     * @return 一个包含审批评论实体的列表。如果没有找到任何匹配的评论，将返回一个空列表。
     */
    List<ApprovalCommentEntity> queryApprovalCommentEntities(List<String> approvalIds);
    /* Ended by AICoder, pid:ja672e70a70977f146810be29033291f0ec2f905 */
}
