/* Started by AICoder, pid:af1edbdaccw45921437e0987002969219e743ce6 */
package com.zte.uedm.dcdigital.application.approval.executor;

import com.zte.uedm.dcdigital.common.bean.process.MktApprovalAddDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MktApprovalSubmitDto;

public interface MktApprovalCommandService {

    /**
     * 创建"处理市场投标支持"评审单。
     *
     * @param approvalDto 包含审批单详细信息的数据传输对象，不能为空。
     * @return 新创建的审批单的唯一标识符。
     */
    String createMarketBidApproval(MktApprovalAddDto approvalDto);

    /**
     * 处理"市场投标支持"评审单。
     *
     * @param mktApprovalSubmitDto 包含提交审批单详细信息的数据传输对象，不能为空。
     */
    void acceptMktApproval(MktApprovalSubmitDto mktApprovalSubmitDto);
    /**
     * 撤销"市场投标支持"评审单。
     *
     * @param cancelDto 包含提交审批单流程实例id不能为空。
     */
    void cancelMarketBidApproval(MktApprovalCancelDto cancelDto);
}

/* Ended by AICoder, pid:af1edbdaccw45921437e0987002969219e743ce6 */