/* Started by AICoder, pid:w2aa5835916a9d014a6a0a55c0ac1c81e9938190 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ProductUpgradeApprovalVo {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 审批名称
     */
    private String title;

    /**
     * 审批类型,9 产品提资任务
     */
    private Integer approvalType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 发起人
     */
    private String submitUser;

    /**
     * 提交时间
     */
    private String submitTime;

    /**
     * 流程实例ID
     */
    private String flowId;

    /**
     * 流程类型
     */
    private String flowType;

    /**
     * 审批状态,0-待审批，1-审批中，2-审批通过，3-审批不通过，4-已撤回,5-待处理，6-处理中，7-待验收，8-已验收，9-已终止
     */
    private int status;

    /**
     * 评论列表
     */
    private List<ProductUpgradeCommentVo> productUpgradeComments;

    /**
     * 附件列表
     * */
    private List<FileInfoVo> fileInfos;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}

/* Ended by AICoder, pid:w2aa5835916a9d014a6a0a55c0ac1c81e9938190 */