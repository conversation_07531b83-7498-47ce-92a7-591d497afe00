<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="ManualApproval" name="手动下发市场投标支持" isExecutable="true">
    <startEvent id="startEvent1" name="开始" flowable:formFieldValidation="true"/>
    <userTask id="sid-65BDE01B-F332-488B-9998-ABA8F0906755" name="方案经理" flowable:assignee="${initiator}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427" name="产品se" flowable:assignee="${manualUser}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.manual.ProcessTaskListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-94F1575B-C408-4D3C-96FC-38CEE240658A" name="方案经理" flowable:assignee="${schemeSe}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.manual.AcceptTaskListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-46D1F554-2242-4F22-AFD6-65304FE3B6C7" name="产品se" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.manual.SetUpCandidatesListener"/>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-7DF752A1-0D8C-4024-9F31-81EF66240B08" name="验证"/>
    <sequenceFlow id="sid-019ECDD0-F742-406F-8C50-82DFF06D936A" sourceRef="sid-94F1575B-C408-4D3C-96FC-38CEE240658A" targetRef="sid-7DF752A1-0D8C-4024-9F31-81EF66240B08"/>
    <exclusiveGateway id="sid-021BE331-9398-41B7-BDAE-C235231EC2D9"/>
    <sequenceFlow id="sid-3B9ED623-4191-4F7A-9A28-3D6754325CD8" sourceRef="startEvent1" targetRef="sid-65BDE01B-F332-488B-9998-ABA8F0906755"/>
    <sequenceFlow id="sid-64F5748B-D967-49EC-AF05-E2F1DB1ADDCD" name="下发" sourceRef="sid-65BDE01B-F332-488B-9998-ABA8F0906755" targetRef="sid-46D1F554-2242-4F22-AFD6-65304FE3B6C7"/>
    <endEvent id="sid-3F05CA30-889E-43A7-B56C-41E6ECCA7EAE" name="结束"/>
    <sequenceFlow id="sid-2A90F252-B230-4656-8D96-F6228AEBCDA9" name="通过" sourceRef="sid-7DF752A1-0D8C-4024-9F31-81EF66240B08" targetRef="sid-3F05CA30-889E-43A7-B56C-41E6ECCA7EAE">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.listener.manual.AcceptDoPassListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==1} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-635E1D2E-BC3F-4DF7-B63D-75F26CE925C3" name="不通过" sourceRef="sid-7DF752A1-0D8C-4024-9F31-81EF66240B08" targetRef="sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.listener.manual.AcceptDoNotPassListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==0} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C1FD542B-C12A-46EE-95FC-BD0CD934F466" name="完成" sourceRef="sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427" targetRef="sid-94F1575B-C408-4D3C-96FC-38CEE240658A"/>
    <sequenceFlow id="sid-41A2D029-B1B1-4305-84C0-75148ADD2775" name="拒绝" sourceRef="sid-021BE331-9398-41B7-BDAE-C235231EC2D9" targetRef="sid-3F05CA30-889E-43A7-B56C-41E6ECCA7EAE">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${accept==0} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C68131F1-088F-4199-B858-0433B9A9DC06" name="同意" sourceRef="sid-021BE331-9398-41B7-BDAE-C235231EC2D9" targetRef="sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427">
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${accept==1} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-CA2181F1-7CBA-4ACA-B257-CB487C1631B5" name="接收" sourceRef="sid-46D1F554-2242-4F22-AFD6-65304FE3B6C7" targetRef="sid-021BE331-9398-41B7-BDAE-C235231EC2D9"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_ManualApproval">
    <bpmndi:BPMNPlane bpmnElement="ManualApproval" id="BPMNPlane_ManualApproval">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="15.0" y="165.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-65BDE01B-F332-488B-9998-ABA8F0906755" id="BPMNShape_sid-65BDE01B-F332-488B-9998-ABA8F0906755">
        <omgdc:Bounds height="80.0" width="100.0" x="105.0" y="140.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427" id="BPMNShape_sid-E63CA1A8-E64E-4D2C-A91B-C0A3631E1427">
        <omgdc:Bounds height="80.0" width="100.0" x="510.0" y="140.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-94F1575B-C408-4D3C-96FC-38CEE240658A" id="BPMNShape_sid-94F1575B-C408-4D3C-96FC-38CEE240658A">
        <omgdc:Bounds height="80.0" width="100.0" x="690.0" y="140.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-46D1F554-2242-4F22-AFD6-65304FE3B6C7" id="BPMNShape_sid-46D1F554-2242-4F22-AFD6-65304FE3B6C7">
        <omgdc:Bounds height="80.0" width="100.0" x="255.0" y="140.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-7DF752A1-0D8C-4024-9F31-81EF66240B08" id="BPMNShape_sid-7DF752A1-0D8C-4024-9F31-81EF66240B08">
        <omgdc:Bounds height="40.0" width="40.0" x="900.0" y="160.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-021BE331-9398-41B7-BDAE-C235231EC2D9" id="BPMNShape_sid-021BE331-9398-41B7-BDAE-C235231EC2D9">
        <omgdc:Bounds height="40.0" width="40.0" x="408.5" y="160.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3F05CA30-889E-43A7-B56C-41E6ECCA7EAE" id="BPMNShape_sid-3F05CA30-889E-43A7-B56C-41E6ECCA7EAE">
        <omgdc:Bounds height="28.0" width="28.0" x="1050.0" y="166.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-3B9ED623-4191-4F7A-9A28-3D6754325CD8" id="BPMNEdge_sid-3B9ED623-4191-4F7A-9A28-3D6754325CD8" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="44.94999883049306" y="180.0"/>
        <omgdi:waypoint x="105.0" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C1FD542B-C12A-46EE-95FC-BD0CD934F466" id="BPMNEdge_sid-C1FD542B-C12A-46EE-95FC-BD0CD934F466" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="609.9499999999431" y="180.0"/>
        <omgdi:waypoint x="689.9999999999723" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-019ECDD0-F742-406F-8C50-82DFF06D936A" id="BPMNEdge_sid-019ECDD0-F742-406F-8C50-82DFF06D936A" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="789.9499999999431" y="180.0"/>
        <omgdi:waypoint x="900.0" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-635E1D2E-BC3F-4DF7-B63D-75F26CE925C3" id="BPMNEdge_sid-635E1D2E-BC3F-4DF7-B63D-75F26CE925C3" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="920.2870503597122" y="199.65598490294752"/>
        <omgdi:waypoint x="922.0" y="317.0"/>
        <omgdi:waypoint x="560.0" y="317.0"/>
        <omgdi:waypoint x="560.0" y="219.95000000000002"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-41A2D029-B1B1-4305-84C0-75148ADD2775" id="BPMNEdge_sid-41A2D029-B1B1-4305-84C0-75148ADD2775" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="428.5871179039301" y="160.0873362445415"/>
        <omgdi:waypoint x="429.0" y="66.0"/>
        <omgdi:waypoint x="1064.0" y="66.0"/>
        <omgdi:waypoint x="1064.0" y="166.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C68131F1-088F-4199-B858-0433B9A9DC06" id="BPMNEdge_sid-C68131F1-088F-4199-B858-0433B9A9DC06" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="448.4424202127067" y="180.0"/>
        <omgdi:waypoint x="510.0" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-64F5748B-D967-49EC-AF05-E2F1DB1ADDCD" id="BPMNEdge_sid-64F5748B-D967-49EC-AF05-E2F1DB1ADDCD" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="204.9499999999581" y="180.0"/>
        <omgdi:waypoint x="255.0" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CA2181F1-7CBA-4ACA-B257-CB487C1631B5" id="BPMNEdge_sid-CA2181F1-7CBA-4ACA-B257-CB487C1631B5" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="354.95000000000005" y="180.0"/>
        <omgdi:waypoint x="408.5" y="180.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2A90F252-B230-4656-8D96-F6228AEBCDA9" id="BPMNEdge_sid-2A90F252-B230-4656-8D96-F6228AEBCDA9" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="939.9430777238028" y="180.0"/>
        <omgdi:waypoint x="1050.0" y="180.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>