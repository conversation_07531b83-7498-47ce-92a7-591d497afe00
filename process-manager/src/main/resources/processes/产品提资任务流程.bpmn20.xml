<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:y2dd5wf412i7a8914e8808500178fd569126b9ee -->
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.example.org/DesignTask"
             exporter="Flowable Open Source Modeler"
             exporterVersion="6.7.2">

    <process id="ProductUpgradeTaskProcess" name="产品提资任务" isExecutable="true">
        <!-- 开始事件 -->
        <startEvent id="startEvent" name="开始" flowable:formFieldValidation="true"/>

        <!-- 分配设计任务 -->
        <userTask id="assignDesignTask" name="分配设计任务" flowable:formFieldValidation="true">
            <documentation>设计负责人根据产品小类为产品SE分配提资任务</documentation>
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.deepDesign.ProductUpgradeAllocationDesignListener"/>
            </extensionElements>
        </userTask>

        <!-- 提交文档附件 -->
        <userTask id="submitDocuments" name="产品支持SE提交文档附件" flowable:assignee="${productUpgradeUser}" flowable:formFieldValidation="true">
            <documentation>产品SE提交相关文档附件</documentation>
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.deepDesign.ProductUpgradeSubmitDocProcessTaskListener"/>
                <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
                    <![CDATA[ false ]]>
                </modeler:initiator-can-complete>
            </extensionElements>
        </userTask>

        <!-- 排他网关 -->
        <exclusiveGateway id="checkDocuments"/>

        <!-- 验收任务 -->
        <userTask id="acceptTask" name="设计负责人验收" flowable:assignee="${designLeader}" flowable:formFieldValidation="true">
            <documentation>设计负责人确认任务完成</documentation>
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.deepDesign.ProductUpgradeAcceptListener"/>
                <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
                    <![CDATA[ false ]]>
                </modeler:initiator-can-complete>
            </extensionElements>
        </userTask>

        <!-- 结束事件 -->
        <endEvent id="sid-534A8D01-0828-43FB-B7E9-1A196C9B34A2"/>

        <!-- 发起人任务 -->
        <userTask id="sid-A14B2E19-1D0F-43E0-A6C8-9AB1311D9FAD" name="发起人" flowable:assignee="${initiator}" flowable:formFieldValidation="true">
            <extensionElements>
                <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
                    <![CDATA[ false ]]>
                </modeler:initiator-can-complete>
            </extensionElements>
        </userTask>

        <!-- 流程连接线 -->
        <sequenceFlow id="sequenceFlow-d2f2a089-7598-44db-aaf6-0011cdaf8c9d" sourceRef="submitDocuments" targetRef="acceptTask"/>
        <sequenceFlow id="sid-FA5FAF9A-8EC0-4D26-87E2-6DE9E7BF6F6A" sourceRef="sid-A14B2E19-1D0F-43E0-A6C8-9AB1311D9FAD" targetRef="assignDesignTask"/>
        <sequenceFlow id="sequenceFlow-54e2bb62-a5c3-4259-8d99-2ff293ddd281" sourceRef="assignDesignTask" targetRef="submitDocuments"/>
        <sequenceFlow id="sid-16011BC2-311F-4533-8AED-5F6A173BD576" name="验收文档" sourceRef="acceptTask" targetRef="checkDocuments"/>
        <sequenceFlow id="sid-26D4F80F-9565-44F5-B483-5AC4F7C64048" name="验收不通过" sourceRef="checkDocuments" targetRef="submitDocuments">
            <extensionElements>
                <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.listener.deepDesign.ProductUpgradeAcceptDoNotPassListener"/>
            </extensionElements>
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[ ${approvalResult==0} ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="sid-A5E87096-245D-4E77-B1FC-6D1C3D681A3E" name="验收通过" sourceRef="checkDocuments" targetRef="sid-534A8D01-0828-43FB-B7E9-1A196C9B34A2">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[ ${approvalResult==1} ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="sid-4F3BA1FF-5EE2-4617-AE50-8B9A97DABA5E" sourceRef="startEvent" targetRef="sid-A14B2E19-1D0F-43E0-A6C8-9AB1311D9FAD"/>
    </process>

    <!-- BPMN Diagram 定义 -->
    <bpmndi:BPMNDiagram id="BPMNDiagram_productDesignTaskProcess">
        <bpmndi:BPMNPlane bpmnElement="productDesignTaskProcess" id="BPMNPlane_productDesignTaskProcess">
            <!-- 元素坐标定义 -->
            <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
                <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="95.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="assignDesignTask" id="BPMNShape_assignDesignTask">
                <omgdc:Bounds height="60.0" width="100.0" x="210.0" y="80.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="submitDocuments" id="BPMNShape_submitDocuments">
                <omgdc:Bounds height="60.0" width="100.0" x="390.0" y="80.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="checkDocuments" id="BPMNShape_checkDocuments">
                <omgdc:Bounds height="40.0" width="40.0" x="825.0" y="90.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="acceptTask" id="BPMNShape_acceptTask">
                <omgdc:Bounds height="60.0" width="100.0" x="615.0" y="80.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sid-534A8D01-0828-43FB-B7E9-1A196C9B34A2" id="BPMNShape_sid-534A8D01-0828-43FB-B7E9-1A196C9B34A2">
                <omgdc:Bounds height="28.0" width="28.0" x="1065.0" y="96.0"/>
            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sid-A14B2E19-1D0F-43E0-A6C8-9AB1311D9FAD" id="BPMNShape_sid-A14B2E19-1D0F-43E0-A6C8-9AB1311D9FAD">
                <omgdc:Bounds height="80.0" width="100.0" x="75.0" y="70.0"/>
            </bpmndi:BPMNShape>

            <!-- 连接线路径定义 -->
            <bpmndi:BPMNEdge bpmnElement="sequenceFlow-54e2bb62-a5c3-4259-8d99-2ff293ddd281" id="BPMNEdge_sequenceFlow-54e2bb62-a5c3-4259-8d99-2ff293ddd281">
                <omgdi:waypoint x="309.94999999990614" y="110.0"/>
                <omgdi:waypoint x="390.0" y="110.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sequenceFlow-d2f2a089-7598-44db-aaf6-0011cdaf8c9d" id="BPMNEdge_sequenceFlow-d2f2a089-7598-44db-aaf6-0011cdaf8c9d">
                <omgdi:waypoint x="489.95000000000005" y="110.0"/>
                <omgdi:waypoint x="615.0" y="110.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sid-FA5FAF9A-8EC0-4D26-87E2-6DE9E7BF6F6A" id="BPMNEdge_sid-FA5FAF9A-8EC0-4D26-87E2-6DE9E7BF6F6A">
                <omgdi:waypoint x="174.95" y="110.0"/>
                <omgdi:waypoint x="209.9999999999962" y="110.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sid-A5E87096-245D-4E77-B1FC-6D1C3D681A3E" id="BPMNEdge_sid-A5E87096-245D-4E77-B1FC-6D1C3D681A3E">
                <omgdi:waypoint x="864.486583011583" y="110.45922746781116"/>
                <omgdi:waypoint x="1065.0000273818912" y="110.0298716034776"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sid-4F3BA1FF-5EE2-4617-AE50-8B9A97DABA5E" id="BPMNEdge_sid-4F3BA1FF-5EE2-4617-AE50-8B9A97DABA5E">
                <omgdi:waypoint x="29.949998489957597" y="110.0"/>
                <omgdi:waypoint x="75.0" y="110.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sid-16011BC2-311F-4533-8AED-5F6A173BD576" id="BPMNEdge_sid-16011BC2-311F-4533-8AED-5F6A173BD576">
                <omgdi:waypoint x="714.9499999999999" y="110.0"/>
                <omgdi:waypoint x="825.0" y="110.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sid-26D4F80F-9565-44F5-B483-5AC4F7C64048" id="BPMNEdge_sid-26D4F80F-9565-44F5-B483-5AC4F7C64048">
                <omgdi:waypoint x="845.5" y="129.44435278498162"/>
                <omgdi:waypoint x="845.5" y="278.18181757099376"/>
                <omgdi:waypoint x="424.5" y="278.18181757099376"/>
                <omgdi:waypoint x="424.5" y="139.95"/>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>

        <!-- Ended by AICoder, pid:y2dd5wf412i7a8914e8808500178fd569126b9ee -->