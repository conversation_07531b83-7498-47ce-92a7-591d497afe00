<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
    <process id="lectotypeProcess" name="创建选型单流程" isExecutable="true">
        <userTask id="sid-DB75F12A-F89F-4713-8E08-600EFFF18803" name="资料准备" flowable:candidateUsers="${costDirectors}" flowable:formFieldValidation="true">
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.demand.DemandProcOneListener"/>
            </extensionElements>
        </userTask>
        <userTask id="sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3" name="申请" flowable:candidateUsers="${materialAssistants}" flowable:formFieldValidation="true">
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.demand.DemandProcListener"/>
            </extensionElements>
        </userTask>
        <userTask id="sid-BFEBC41A-A6F8-4411-ABEA-09966E5C82D3" name="处理申请" flowable:candidateUsers="${materialAssistants}" flowable:formFieldValidation="true">
            <extensionElements>
                <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.listener.demand.DemandProcListener"/>
            </extensionElements>
        </userTask>
        <endEvent id="sid-F6685C55-7044-4E0A-A7DB-D861EB7485B3" name="结束"/>
        <startEvent id="startEvent1" name="开始" flowable:formFieldValidation="true"/>
        <sequenceFlow id="sid-D1E1DE9A-DB87-41D3-94D4-479A64E185CE" sourceRef="startEvent1" targetRef="sid-DB75F12A-F89F-4713-8E08-600EFFF18803"/>
        <sequenceFlow id="sid-CA56349D-4FB3-4A4B-9881-8671409149C0" name="退回" sourceRef="sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3" targetRef="sid-DB75F12A-F89F-4713-8E08-600EFFF18803">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[ ${approvalResult==0} ]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="sid-3A417340-7692-47B8-94DB-D3A80E8320DA" name="已完成准备" sourceRef="sid-DB75F12A-F89F-4713-8E08-600EFFF18803" targetRef="sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3"/>
        <sequenceFlow id="sid-1C7A0BA1-8D32-45D1-93BE-1AD9DFFB4A2E" name="申请已通过" sourceRef="sid-BFEBC41A-A6F8-4411-ABEA-09966E5C82D3" targetRef="sid-F6685C55-7044-4E0A-A7DB-D861EB7485B3"/>
        <sequenceFlow id="sid-A3EE7D87-0D07-4CED-BCE7-04A2A55E3BB4" name="已提交申请" sourceRef="sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3" targetRef="sid-BFEBC41A-A6F8-4411-ABEA-09966E5C82D3">
            <conditionExpression xsi:type="tFormalExpression">
                <![CDATA[ ${approvalResult==1} ]]>
            </conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_lectotypeProcess">
        <bpmndi:BPMNPlane bpmnElement="lectotypeProcess" id="BPMNPlane_lectotypeProcess">
            <bpmndi:BPMNShape bpmnElement="sid-DB75F12A-F89F-4713-8E08-600EFFF18803" id="BPMNShape_sid-DB75F12A-F89F-4713-8E08-600EFFF18803">
                <omgdc:Bounds height="80.0" width="100.0" x="180.0" y="138.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3" id="BPMNShape_sid-CCF2DE2E-8392-4C03-A3C7-E9D494878DB3">
                <omgdc:Bounds height="79.0" width="139.0" x="360.0" y="138.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="sid-BFEBC41A-A6F8-4411-ABEA-09966E5C82D3" id="BPMNShape_sid-BFEBC41A-A6F8-4411-ABEA-09966E5C82D3">
                <omgdc:Bounds height="80.0" width="100.0" x="660.0" y="138.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="sid-F6685C55-7044-4E0A-A7DB-D861EB7485B3" id="BPMNShape_sid-F6685C55-7044-4E0A-A7DB-D861EB7485B3">
                <omgdc:Bounds height="28.0" width="28.0" x="870.0" y="164.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
                <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="sid-D1E1DE9A-DB87-41D3-94D4-479A64E185CE" id="BPMNEdge_sid-D1E1DE9A-DB87-41D3-94D4-479A64E185CE" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
                <omgdi:waypoint x="129.9499986183554" y="178.0"/>
                <omgdi:waypoint x="180.0" y="178.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-CA56349D-4FB3-4A4B-9881-8671409149C0" id="BPMNEdge_sid-CA56349D-4FB3-4A4B-9881-8671409149C0" flowable:sourceDockerX="69.5" flowable:sourceDockerY="1.0" flowable:targetDockerX="75.0" flowable:targetDockerY="1.0">
                <omgdi:waypoint x="429.5" y="138.0"/>
                <omgdi:waypoint x="429.5" y="86.0"/>
                <omgdi:waypoint x="255.0" y="86.0"/>
                <omgdi:waypoint x="255.0" y="138.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-1C7A0BA1-8D32-45D1-93BE-1AD9DFFB4A2E" id="BPMNEdge_sid-1C7A0BA1-8D32-45D1-93BE-1AD9DFFB4A2E" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
                <omgdi:waypoint x="759.9499999998277" y="178.0"/>
                <omgdi:waypoint x="870.0" y="178.0"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-A3EE7D87-0D07-4CED-BCE7-04A2A55E3BB4" id="BPMNEdge_sid-A3EE7D87-0D07-4CED-BCE7-04A2A55E3BB4" flowable:sourceDockerX="138.0" flowable:sourceDockerY="39.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
                <omgdi:waypoint x="498.9499999999964" y="177.50224056603773"/>
                <omgdi:waypoint x="659.999999999995" y="177.8820754716981"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-3A417340-7692-47B8-94DB-D3A80E8320DA" id="BPMNEdge_sid-3A417340-7692-47B8-94DB-D3A80E8320DA" flowable:sourceDockerX="99.0" flowable:sourceDockerY="60.0" flowable:targetDockerX="1.39" flowable:targetDockerY="59.24999999999999">
                <omgdi:waypoint x="279.95000000000005" y="197.99089695351375"/>
                <omgdi:waypoint x="360.0" y="197.26219808229155"/>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>