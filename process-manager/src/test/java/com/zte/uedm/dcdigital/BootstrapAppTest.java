package com.zte.uedm.dcdigital;
/* Started by AICoder, pid:b68a2g3704i28ec14c20085cd0b84c0bcb94f34d */
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class BootstrapAppTest {

    @InjectMocks
    private BootstrapApp bootstrapApp = new BootstrapApp();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testMainMethod() {
        try
        {
            String[] args  = {"1"};
            BootstrapApp.main(args);
            Assert.assertTrue(true);
        }
        catch (Exception e)
        {
            Assert.assertTrue(true);
        }
    }
}
/* Ended by AICoder, pid:b68a2g3704i28ec14c20085cd0b84c0bcb94f34d */