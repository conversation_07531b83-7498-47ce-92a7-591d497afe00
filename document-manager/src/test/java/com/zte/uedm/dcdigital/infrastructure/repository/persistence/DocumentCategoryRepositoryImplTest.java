package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:70b28o3ba1e921a14175084561df6443710798e2 */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentCategoryObj;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentCategoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentCategoryVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DocumentCategoryRepositoryImplTest {

    @InjectMocks
    private DocumentCategoryRepositoryImpl documentCategoryRepository;

    @Mock
    private DocumentCategoryMapper documentCategoryMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // 测试查询列表方法
    @Test
    public void given_QueryParameters_when_QueryList_then_ReturnCorrectList() {
        // Given
        String name = "testName";
        Integer type = 1;
        List<DocumentCategoryPo> mockList = Arrays.asList(new DocumentCategoryPo(), new DocumentCategoryPo());
        when(documentCategoryMapper.selectList(any())).thenReturn(mockList);

        // When
        List<DocumentCategoryVo> result = documentCategoryRepository.queryList(name, type);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(documentCategoryMapper, times(1)).selectList(any());
    }

    @Test
    public void given_EmptyQueryParameters_when_QueryList_then_ReturnEmptyList() {
        // Given
        when(documentCategoryMapper.selectList(any())).thenReturn(Collections.emptyList());

        // When
        List<DocumentCategoryVo> result = documentCategoryRepository.queryList(null, null);

        // Then
        assertTrue(result.isEmpty());
        verify(documentCategoryMapper, times(1)).selectList(any());
    }

    // 测试添加文档分类方法
    @Test
    public void given_Entity_when_AddDocumentCategory_then_InsertIsCalled() {
        // Given
        DocumentCategoryObj entity = new DocumentCategoryObj();
        // When
        int result = documentCategoryRepository.addDocumentCategory(entity);

        // Then
        assertEquals(0, result); // 假设插入成功返回1
        verify(documentCategoryMapper, times(1)).insert(any(DocumentCategoryPo.class));
    }

    // 测试更新文档分类方法
    @Test
    public void given_Entity_when_UpdateDocumentCategory_then_UpdateIsCalled() {
        // Given
        DocumentCategoryObj entity = new DocumentCategoryObj();

        // When
        int result = documentCategoryRepository.updateDocumentCategory(entity);

        // Then
        assertEquals(0, result);
        verify(documentCategoryMapper, times(1)).updateById(any(DocumentCategoryPo.class));
    }

    // 测试删除文档分类方法
    @Test
    public void given_Id_when_DeleteDocumentCategory_then_DeleteIsCalled() {
        // Given
        String id = "testId";
        // When
        int result = documentCategoryRepository.deleteDocumentCategory(id);
        // Then
        assertEquals(0, result);
        verify(documentCategoryMapper, times(1)).deleteById(id);
    }

    // 测试查询单个文档分类方法
    @Test
    public void given_Id_when_QueryDocumentCategory_then_ReturnEntity() {
        // Given
        String id = "testId";
        DocumentCategoryPo po = new DocumentCategoryPo();

        when(documentCategoryMapper.selectById(id)).thenReturn(po);
        // When
        DocumentCategoryObj result = documentCategoryRepository.queryDocumentCategory(id);

        // Then
        assertNotNull(result);
        verify(documentCategoryMapper, times(1)).selectById(id);
    }

    @Test
    public void given_NonExistentId_when_QueryDocumentCategory_then_ReturnNull() {
        // Given
        String id = "nonexistentId";

        when(documentCategoryMapper.selectById(id)).thenReturn(null);

        // When
        DocumentCategoryObj result = documentCategoryRepository.queryDocumentCategory(id);

        // Then
        assertNull(result);
        verify(documentCategoryMapper, times(1)).selectById(id);
    }

    @Test
    public void selectUniqueByIdAndNameTest()
    {
        assertEquals(0, documentCategoryRepository.selectUniqueByIdAndName("id","NAME"));
    }

    @Test
    public void selectCitedCountByIdTest()
    {
        assertEquals(0, documentCategoryRepository.selectCitedCountById("id"));
    }
    /* Started by AICoder, pid:75df5n35a8x2c0f144b1083af00e741f8172c16e */
    @Test
    public void testSelectByName_Found() {
        DocumentCategoryPo po = new DocumentCategoryPo();
        po.setId("123456");
        po.setName("Test Category");
        // 模拟数据库查询返回一个结果
        when(documentCategoryMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(po);

        List<DocumentCategoryVo> result = documentCategoryRepository.selectByName("Test Category");

        assertNotNull(result);
    }
    /* Ended by AICoder, pid:75df5n35a8x2c0f144b1083af00e741f8172c16e */
}

/* Ended by AICoder, pid:70b28o3ba1e921a14175084561df6443710798e2 */
