package com.zte.uedm.dcdigital;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class BootstrapAppTest {

    @InjectMocks
    private BootstrapApp bootstrapApp = new BootstrapApp();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }
    /* Started by AICoder, pid:e8e06p06b783ff1147850bcb704bb50b27f26e95 */
    @Test
    public void testMainMethod() {
        try
        {
            String[] args  = {"1"};
            BootstrapApp.main(args);
            Assert.assertTrue(true);
        }
        catch (Exception e)
        {
            Assert.assertTrue(true);
        }
    }
    /* Ended by AICoder, pid:e8e06p06b783ff1147850bcb704bb50b27f26e95 */
}