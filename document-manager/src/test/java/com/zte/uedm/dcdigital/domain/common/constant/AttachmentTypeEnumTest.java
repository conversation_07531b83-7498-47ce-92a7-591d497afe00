package com.zte.uedm.dcdigital.domain.common.constant;

/* Started by AICoder, pid:v88fbl5b6cg539e14e9c0a69d0458d7e21f132e4 */
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class AttachmentTypeEnumTest {

    @Before
    public void setUp() {
        // No external dependencies to mock for this enum.
    }

    // 测试枚举值是否存在
    @Test
    public void given_EnumValues_when_CheckExistence_then_Success() {
        // Given: Enum values
        AttachmentTypeEnum file = AttachmentTypeEnum.FILE;
        AttachmentTypeEnum url = AttachmentTypeEnum.URL;

        // When: Check if they exist
        assertNotNull(file);
        assertNotNull(url);

        // Then: Verify their codes
        assertEquals(0, file.getCode());
        assertEquals(1, url.getCode());
    }

    // 测试枚举值的代码是否正确
    @Test
    public void given_EnumCodes_when_CheckCodes_then_Success() {
        // Given: Enum values
        AttachmentTypeEnum file = AttachmentTypeEnum.FILE;
        AttachmentTypeEnum url = AttachmentTypeEnum.URL;

        // When: Get their codes
        int fileCode = file.getCode();
        int urlCode = url.getCode();

        // Then: Verify the codes
        assertEquals(0, fileCode);
        assertEquals(1, urlCode);
    }

    // 测试枚举值的唯一性
    @Test
    public void given_DifferentEnumValues_when_CheckUniqueness_then_Success() {
        // Given: Enum values
        AttachmentTypeEnum file = AttachmentTypeEnum.FILE;
        AttachmentTypeEnum url = AttachmentTypeEnum.URL;

        // When: Compare them
        assertNotEquals(file, url);
    }

    // 测试枚举值的toString方法（默认行为）
    @Test
    public void given_EnumValues_when_CheckToString_then_Success() {
        // Given: Enum values
        AttachmentTypeEnum file = AttachmentTypeEnum.FILE;
        AttachmentTypeEnum url = AttachmentTypeEnum.URL;

        // When: Call toString
        String fileString = file.toString();
        String urlString = url.toString();

        // Then: Verify the results
        assertEquals("FILE", fileString);
        assertEquals("URL", urlString);
    }
}

/* Ended by AICoder, pid:v88fbl5b6cg539e14e9c0a69d0458d7e21f132e4 */
