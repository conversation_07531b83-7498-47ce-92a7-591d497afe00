package com.zte.uedm.dcdigital.application.brand.executor.Impl;

/* Started by AICoder, pid:v146ar7f8dt0d3614f7e083cf14b090591d382dc */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoRepository;
import com.zte.uedm.dcdigital.domain.common.constant.DocumentTypeEnum;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentRelationObj;
import com.zte.uedm.dcdigital.domain.service.DocumentInfoDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentAdvancedSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCommonSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentInfoDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentSearchVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DocumentInfoQueryServiceImplTest {

    @InjectMocks
    private DocumentInfoQueryServiceImpl documentInfoQueryService;

    @Mock
    private DocumentInfoRepository documentInfoRepository;

    @Mock
    private ProductService productService;

    @Mock
    private DocumentInfoDomainService documentInfoDomainService;

    @Mock
    private SystemService systemService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // 测试查询文档信息列表方法（正常情况）
    @Test
    public void given_SearchCriteria_when_QueryList_then_ReturnPageVO() {
        // Given
        DocumentInfoSearchDto dto = new DocumentInfoSearchDto();
        PageVO<DocumentInfoVo> pageInfo = new PageVO<>(2,Arrays.asList(new DocumentInfoVo(), new DocumentInfoVo()));

        when(documentInfoRepository.queryList(dto)).thenReturn(pageInfo);

        // When
        PageVO<DocumentInfoVo> result = documentInfoQueryService.queryList(dto);

        // Then
        assertEquals(2, result.getTotal());
        assertEquals(2, result.getList().size());
        verify(documentInfoRepository, times(1)).queryList(dto);
    }

    // 测试查询文档信息列表方法（空结果）
    @Test
    public void given_EmptySearchCriteria_when_QueryList_then_ReturnEmptyPageVO() {
        // Given
        DocumentInfoSearchDto dto = new DocumentInfoSearchDto();
        PageVO<DocumentInfoVo> pageInfo = new PageVO<>(0,Collections.emptyList());

        when(documentInfoRepository.queryList(dto)).thenReturn(pageInfo);

        // When
        PageVO<DocumentInfoVo> result = documentInfoQueryService.queryList(dto);

        // Then
        assertEquals(0, result.getTotal());
        assertTrue(result.getList().isEmpty());
        verify(documentInfoRepository, times(1)).queryList(dto);
    }

    /* Started by AICoder, pid:13b8ft8fccf8c2114014099a1120910e82123424 */
    @Test
    public void given_validId_when_queryDetail_then_returnDetailVo() {
        // Given
        String id = "123";
        DocumentInfoDetailVo detailVo = new DocumentInfoDetailVo();
        detailVo.setId(id);
        detailVo.setType(DocumentTypeEnum.PRODUCT.getCode());
        detailVo.setAscriptionId("456");

        List<DocumentRelationObj> relations = new ArrayList<>();
        DocumentRelationObj obj = new DocumentRelationObj();
        obj.setDocumentInfoId("123");
        obj.setResourceId("res1");
        obj.setResourceType(DocumentTypeEnum.PRODUCT.getCode());
        relations.add(obj);
        DocumentRelationObj obj1 = new DocumentRelationObj();
        obj1.setDocumentInfoId("123");
        obj1.setResourceId("res2");
        obj1.setResourceType(DocumentTypeEnum.PROJECT.getCode());
        relations.add(obj);
        relations.add(obj1);
        when(documentInfoRepository.queryDetail(id)).thenReturn(detailVo);
        when(documentInfoRepository.querRelationById(id)).thenReturn(relations);
        List<DocumentCitedVo> resourceVos = new ArrayList<>();
        DocumentCitedVo resourceVo = new DocumentCitedVo();
        resourceVo.setId("res1");
        resourceVo.setName("name");
        resourceVos.add(resourceVo);
        when(productService.selectCitedList(Mockito.anyList(),Mockito.anyInt())).thenReturn(resourceVos);


        ProductCategoryInfoVo productCategoryInfoVo = new ProductCategoryInfoVo();
        productCategoryInfoVo.setPathName("Product Path");

        when(productService.selectProductCategoryList(Collections.singletonList("456")))
                .thenReturn(Collections.singletonList(productCategoryInfoVo));

        // When
        DocumentInfoDetailVo result = documentInfoQueryService.queryDetail(id);

        // Then
        assertEquals(id, result.getId());
        assertEquals("Product Path", result.getAscriptionName());
        verify(documentInfoRepository, times(1)).queryDetail(id);
        verify(productService, times(1)).selectProductCategoryList(Collections.singletonList("456"));
    }

    @Test
    public void given_nullDetail_when_queryDetail_then_returnNull() {
        // Given
        String id = "123";
        when(documentInfoRepository.queryDetail(id)).thenReturn(null);

        // When
        DocumentInfoDetailVo result = documentInfoQueryService.queryDetail(id);

        // Then
        assertNull(result);

        verify(documentInfoRepository, times(1)).queryDetail(id);
        verify(productService, never()).selectProductCategoryList(any());
    }

    @Test
    public void given_nonProductType_when_queryDetail_then_setDefaultAscriptionName() {
        // Given
        String id = "123";
        DocumentInfoDetailVo detailVo = new DocumentInfoDetailVo();
        detailVo.setId(id);
        detailVo.setType(DocumentTypeEnum.PRODUCT.getCode());
        detailVo.setAscriptionId("456");

        when(documentInfoRepository.queryDetail(id)).thenReturn(detailVo);
        // When
        DocumentInfoDetailVo result = documentInfoQueryService.queryDetail(id);

        // Then
        assertEquals(id, result.getId());
        assertEquals("--", result.getAscriptionName());
        verify(documentInfoRepository, times(1)).queryDetail(id);
    }

    @Test
    public void given_emptyProductCategoryList_when_queryDetail_then_setDefaultAscriptionName() {
        // Given
        String id = "123";
        DocumentInfoDetailVo detailVo = new DocumentInfoDetailVo();
        detailVo.setId(id);
        detailVo.setType(DocumentTypeEnum.PRODUCT.getCode());
        detailVo.setAscriptionId("456");

        when(documentInfoRepository.queryDetail(id)).thenReturn(detailVo);

        when(productService.selectProductCategoryList(Collections.singletonList("456")))
                .thenReturn(Collections.emptyList());
        // When
        DocumentInfoDetailVo result = documentInfoQueryService.queryDetail(id);

        // Then
        assertEquals(id, result.getId());
        assertEquals("--", result.getAscriptionName());
        verify(documentInfoRepository, times(1)).queryDetail(id);
        verify(productService, times(1)).selectProductCategoryList(Collections.singletonList("456"));
    }
    /* Ended by AICoder, pid:13b8ft8fccf8c2114014099a1120910e82123424 */

    /* Started by AICoder, pid:9d97f83f91s82eb148f2085b6021a79cfbd4b7a1 */
    @Test
    public void given_validAdvancedSearchDto_when_advancedSearch_then_returnPageVO() {
        // Given
        DocumentAdvancedSearchDto dto = new DocumentAdvancedSearchDto();
        DocumentSearchVo searchVo = new DocumentSearchVo();
        searchVo.setType(0);
        searchVo.setAscriptionId("Ascription1");
        searchVo.setAttachmentType("1");
        DocumentSearchVo searchVo1 = new DocumentSearchVo();
        searchVo1.setType(1);
        searchVo1.setAscriptionId("Ascription2");
        searchVo1.setAttachmentType("1");
        PageVO<DocumentSearchVo> pageInfo = new PageVO<>(2,Arrays.asList(searchVo,searchVo1));
        when(documentInfoRepository.advancedSearch(dto)).thenReturn(pageInfo);
        ProductCategoryInfoVo productCategoryInfoVo = new ProductCategoryInfoVo();
        productCategoryInfoVo.setId("1");
        productCategoryInfoVo.setPathName("name");
        List<ProductCategoryInfoVo> productCategoryInfoVos = Collections.singletonList(productCategoryInfoVo);
        when(productService.selectProductCategoryList(any())).thenReturn(productCategoryInfoVos);

        // When
        PageVO<DocumentSearchVo> result = documentInfoQueryService.advancedSearch(dto);

        // Then
        assertEquals(2, result.getList().size());
        assertEquals("--", result.getList().get(0).getAscriptionName());
        verify(documentInfoRepository, times(1)).advancedSearch(dto);
        verify(productService, times(1)).selectProductCategoryList(any());
    }

    @Test
    public void given_emptyAdvancedSearchResult_when_advancedSearch_then_returnEmptyPageVO() {
        // Given
        DocumentAdvancedSearchDto dto = new DocumentAdvancedSearchDto();
        PageVO<DocumentSearchVo> pageInfo = new PageVO<>(0,Collections.emptyList());
        when(documentInfoRepository.advancedSearch(dto)).thenReturn(pageInfo);

        // When
        PageVO<DocumentSearchVo> result = documentInfoQueryService.advancedSearch(dto);

        // Then
        assertEquals(0, result.getList().size());
        verify(documentInfoRepository, times(1)).advancedSearch(dto);
        verify(productService, never()).selectProductCategoryList(any());
    }

    // 测试 commonSearch 方法
    @Test
    public void given_validCommonSearchDto_when_commonSearch_then_returnPageVO() {
        // Given
        DocumentCommonSearchDto dto = new DocumentCommonSearchDto();
        DocumentSearchVo searchVo = new DocumentSearchVo();
        searchVo.setType(1);
        searchVo.setAscriptionId("Ascription1");
        searchVo.setAttachmentType("1");
        DocumentSearchVo searchVo1 = new DocumentSearchVo();
        searchVo1.setType(1);
        searchVo1.setAscriptionId("Ascription2");
        searchVo1.setAttachmentType("1");
        PageVO<DocumentSearchVo> pageInfo = new PageVO<>(2,Arrays.asList(searchVo,searchVo1));
        when(documentInfoRepository.commonSearch(dto)).thenReturn(pageInfo);
        ProductCategoryInfoVo productCategoryInfoVo = new ProductCategoryInfoVo();
        productCategoryInfoVo.setId("1");
        productCategoryInfoVo.setPathName("name");
        List<ProductCategoryInfoVo> productCategoryInfoVos = Collections.singletonList(productCategoryInfoVo);
        when(productService.selectProductCategoryList(any())).thenReturn(productCategoryInfoVos);

        // When
        PageVO<DocumentSearchVo> result = documentInfoQueryService.commonSearch(dto);

        // Then
        assertEquals(2, result.getList().size());
        assertEquals("--", result.getList().get(1).getAscriptionName());
        verify(documentInfoRepository, times(1)).commonSearch(dto);
        verify(productService, times(1)).selectProductCategoryList(any());
    }

    @Test
    public void given_emptyCommonSearchResult_when_commonSearch_then_returnEmptyPageVO() {
        // Given
        DocumentCommonSearchDto dto = new DocumentCommonSearchDto();
        PageVO<DocumentSearchVo> pageInfo = new PageVO<>(0,Collections.emptyList());
        when(documentInfoRepository.commonSearch(dto)).thenReturn(pageInfo);

        // When
        PageVO<DocumentSearchVo> result = documentInfoQueryService.commonSearch(dto);

        // Then
        assertEquals(0, result.getList().size());
        verify(documentInfoRepository, times(1)).commonSearch(dto);
        verify(productService, never()).selectProductCategoryList(any());
    }
    /* Ended by AICoder, pid:9d97f83f91s82eb148f2085b6021a79cfbd4b7a1 */

    /* Started by AICoder, pid:u009cb331by76f81403408ebe07c263e48c88b86 */
    @Test
    public void given_emptyResourceId_when_queryByResourceId_then_returnEmptyList() {
        // Given
        String resourceId = "";

        // When
        List<DocumentInfoVo> result = documentInfoQueryService.queryByResourceId(resourceId);

        // Then
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void given_nullResourceId_when_queryByResourceId_then_returnEmptyList() {
        // When
        List<DocumentInfoVo> result = documentInfoQueryService.queryByResourceId(null);

        // Then
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void given_validResourceId_when_queryByResourceId_then_returnDocumentInfoList() {
        // Given
        String resourceId = "123";
        List<DocumentInfoVo> expectedList = Collections.singletonList(new DocumentInfoVo());
        when(documentInfoRepository.queryByResourceId(resourceId)).thenReturn(expectedList);

        // When
        List<DocumentInfoVo> result = documentInfoQueryService.queryByResourceId(resourceId);

        // Then
        assertEquals(expectedList, result);
        verify(documentInfoRepository, times(1)).queryByResourceId(resourceId);
    }
    /* Ended by AICoder, pid:u009cb331by76f81403408ebe07c263e48c88b86 */

    /* Started by AICoder, pid:0f39el4452rcc14142540b9e8058ac3f969192f7 */
    @Test
    public void testExistRelatedDocument_WithValidAscriptionId() {
        String ascriptionId = "123";
        when(documentInfoRepository.existRelatedDocumentByAscriptionId(ascriptionId)).thenReturn(true);

        Boolean result = documentInfoQueryService.existRelatedDocument(ascriptionId);

        assertNotNull(result);
        assertTrue(result);
        verify(documentInfoRepository, times(1)).existRelatedDocumentByAscriptionId(ascriptionId);
    }

    @Test
    public void testExistRelatedDocument_WithEmptyAscriptionId() {
        String ascriptionId = "";
        Boolean result = documentInfoQueryService.existRelatedDocument(ascriptionId);

        assertNotNull(result);
        assertFalse(result);
        verify(documentInfoRepository, never()).existRelatedDocumentByAscriptionId(anyString());
    }

    @Test
    public void testExistRelatedDocument_WithNullAscriptionId() {
        String ascriptionId = null;
        Boolean result = documentInfoQueryService.existRelatedDocument(ascriptionId);

        assertNotNull(result);
        assertFalse(result);
        verify(documentInfoRepository, never()).existRelatedDocumentByAscriptionId(anyString());
    }
    /* Started by AICoder, pid:19d57k4ea9k4196149190838301a6288ad9223b8 */
    @Test
    public void queryByAscriptionId() {
        List<DocumentInfoVo> result = documentInfoQueryService.queryByAscriptionId("ascriptionId");
        assertTrue(result.isEmpty());
    }
    @Test
    public void testQueryByAscriptionId_EmptyAscriptionId() {
        List<DocumentInfoVo> result = documentInfoQueryService.queryByAscriptionId("");
        assertTrue(result.isEmpty());
    }
    @Test
    public void testQueryByAscriptionIdAndCategory_NoSubDocument() {
        List<DocumentInfoVo> result = documentInfoQueryService.queryByAscriptionIdAndCategory("5", "");
        assertTrue(result.isEmpty());
    }
}

/* Ended by AICoder, pid:v146ar7f8dt0d3614f7e083cf14b090591d382dc */
