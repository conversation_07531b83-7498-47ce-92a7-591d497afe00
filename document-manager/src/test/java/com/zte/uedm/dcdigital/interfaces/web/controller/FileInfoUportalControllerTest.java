package com.zte.uedm.dcdigital.interfaces.web.controller;

/* Started by AICoder, pid:hfa60s6209l05e314eaa0a37b04515941da19a2c */
import com.zte.uedm.dcdigital.application.brand.executor.FileInfoCommandService;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;


import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FileInfoUportalControllerTest {

    @InjectMocks
    private FileInfoUportalController fileInfoUportalController;

    @Mock
    private FileInfoCommandService fileInfoCommandService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:d5608i2851j974114d20094ef03e1135c3004916 */
    @Test
    public void given_validFormDataMultiPart_when_uploadFile_then_returnsSuccess() {
        // Given
        FormDataMultiPart formDataMultiPart = mock(FormDataMultiPart.class);
        List<FileInfoVo> fileInfoVoList = Collections.singletonList(new FileInfoVo());
        when(fileInfoCommandService.uploadFile(any(FormDataMultiPart.class))).thenReturn(fileInfoVoList);

        // When
        BaseResult result = fileInfoUportalController.uploadFile(formDataMultiPart);

        // Then
        assertEquals(BaseResult.success().getCode(), result.getCode());
        assertEquals(fileInfoVoList, result.getData());
        verify(fileInfoCommandService, times(1)).uploadFile(formDataMultiPart);
    }

    /**
     * 测试文件上传时输入为空的情况。
     */
    @Test
    public void given_nullFormDataMultiPart_when_uploadFile_then_returnsParamError() {
        // Given
        FormDataMultiPart formDataMultiPart = null;

        // When
        BaseResult result = fileInfoUportalController.uploadFile(formDataMultiPart);

        // Then
        assertEquals(StatusCode.PARAM_ERROR.getCode(), result.getCode());
    }
    /* Ended by AICoder, pid:d5608i2851j974114d20094ef03e1135c3004916 */

    // 测试文件下载方法（正常情况）
    @Test
    public void given_FileId_when_Download_then_FileIsDownloaded() throws IOException {
        // Given
        String fileId = "123";
        HttpServletResponse response = new MockHttpServletResponse();

        // When
        fileInfoUportalController.download(fileId, response);

        // Then
        verify(fileInfoCommandService, times(1)).download(eq(fileId), any(HttpServletResponse.class));
    }

    @Test
    public void imagePreview() throws IOException {
        // Given
        String fileId = "123";
        HttpServletResponse response = new MockHttpServletResponse();

        // When
        fileInfoUportalController.imagePreview(fileId, response);

        // Then
        verify(fileInfoCommandService, times(1)).imagePreview(eq(fileId), any(HttpServletResponse.class));
    }
}

/* Ended by AICoder, pid:hfa60s6209l05e314eaa0a37b04515941da19a2c */
