/* Started by AICoder, pid:j21a8y313dwb98314b120b6b406b45359d97a9f7 */
package com.zte.uedm.dcdigital.application.brand.executor.Impl;

import com.zte.uedm.dcdigital.application.brand.executor.DocumentCategoryQueryService;
import com.zte.uedm.dcdigital.common.bean.enums.DocumentTypeEnums;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentCategoryRepository;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCategorySearchDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DocumentCategoryQueryServiceImpl 类实现了 DocumentCategoryQueryService 接口，负责处理文档分类的查询操作。
 */
@Slf4j
@Service
public class DocumentCategoryQueryServiceImpl implements DocumentCategoryQueryService {

    /**
     * 注入 DocumentCategoryRepository 以执行数据库查询操作。
     */
    @Autowired
    private DocumentCategoryRepository documentCategoryRepository;

    /**
     * 根据提供的搜索条件查询文档分类列表。
     *
     * @param documentCategorySearchDTO 包含搜索条件的数据传输对象
     * @return 文档分类的值对象列表
     */
    @Override
    public List<DocumentCategoryVo> queryList(DocumentCategorySearchDto documentCategorySearchDTO) {
        // 调用 repository 的 queryList 方法，传入名称和类型进行查询
        return documentCategoryRepository.queryList(documentCategorySearchDTO.getName(), documentCategorySearchDTO.getType());
    }

    @Override
    public List<DocumentCategoryVo> queryByCode(String type) {
        DocumentTypeEnums subDocumentEnums = DocumentTypeEnums.getSubDocumentEnumsByCode(type);
        if (subDocumentEnums == null) {
            //文档小类名称全局唯一
            log.error("The document subcategory:{} is not within the agreed scope", type);
            return null;
        }
        //文档小类名称全局唯一
        return documentCategoryRepository.selectByName(subDocumentEnums.getName());

    }
}

/* Ended by AICoder, pid:j21a8y313dwb98314b120b6b406b45359d97a9f7 */
