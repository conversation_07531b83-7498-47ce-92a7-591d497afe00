/* Started by AICoder, pid:f154al7bbcf716e14b150a576009b3431da58a4f */
package com.zte.uedm.dcdigital.application.brand.executor.Impl;

import com.alibaba.excel.util.FileUtils;
import com.zte.uedm.dcdigital.application.brand.executor.FileInfoQueryService;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.domain.aggregate.repository.FileInfoRepository;
import com.zte.uedm.dcdigital.domain.common.valueobj.FileInfoObj;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileInfoQueryServiceImpl implements FileInfoQueryService {

    @Autowired
    private FileInfoRepository fileInfoRepository;

    @Override
    public List<FileInfoVo> queryByFileIds(List<String> fileIds) {
        if (CollectionUtils.isEmpty(fileIds)) {
            return Collections.emptyList();
        }

        List<FileInfoObj> fileInfoObjList = fileInfoRepository.selectList(fileIds);

        return fileInfoObjList.stream()
                .map(this::convertToFileInfoVo)
                .collect(Collectors.toList());
    }

    private FileInfoVo convertToFileInfoVo(FileInfoObj fileInfoObj) {
        FileInfoVo vo = new FileInfoVo();
        vo.setFileId(fileInfoObj.getId());
        vo.setFileName(fileInfoObj.getFileName());
        vo.setFilePath(fileInfoObj.getFilePath());
        File file = new File(fileInfoObj.getFilePath() + File.separator + fileInfoObj.getFileName());
        if (file.exists()) {
            try {
                vo.setFileData(FileUtils.readFileToByteArray(file));
            } catch (IOException e) {
                log.error("Error reading file: {}", e.getMessage());
            }
        }
        return vo;
    }
}

/* Ended by AICoder, pid:f154al7bbcf716e14b150a576009b3431da58a4f */