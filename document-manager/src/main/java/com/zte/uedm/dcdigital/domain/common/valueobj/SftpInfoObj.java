/* Started by AICoder, pid:x9e9eb17ca7e899144cf09d13074e3359a060ced */
package com.zte.uedm.dcdigital.domain.common.valueobj;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * SftpInfoObj 类表示用于SFTP连接的配置信息。
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SftpInfoObj {

    /**
     * SFTP服务器的IP地址。
     */
    private String ip;

    /**
     * SFTP服务器的端口号。
     */
    private String port;

    /**
     * 用于SFTP连接的用户名。
     */
    private String userName;

    /**
     * 用于SFTP连接的密码。
     */
    private String password;
}
/* Ended by AICoder, pid:x9e9eb17ca7e899144cf09d13074e3359a060ced */
