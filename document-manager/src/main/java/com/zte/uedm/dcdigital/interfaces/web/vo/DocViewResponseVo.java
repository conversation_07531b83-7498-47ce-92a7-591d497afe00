package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/* Started by AICoder, pid:y16b3f9369yf97214c670a4ce0e5fa6c2721f1d6 */
@Getter
@Setter
@ToString
public class DocViewResponseVo {

    private Code code;

    @JsonDeserialize(using = BoDeserializer.class)
    private Bo bo;

    private Object other;

    @Setter
    @Getter
    @ToString
    public static class Code {
        private String code;
        private String msgId;
        private String msg;
    }

    @Getter
    @Setter
    @ToString
    public static class Bo {
        private String webviewUrl;
        private String webviewToken;
    }
    public static class BoDeserializer extends JsonDeserializer<Bo> {
        @Override
        public Bo deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            ObjectMapper objectMapper = (ObjectMapper) p.getCodec();
            DocViewResponseVo.Bo bo = objectMapper.convertValue(node, DocViewResponseVo.Bo.class);
            return bo;
        }
    }
}
/* Ended by AICoder, pid:y16b3f9369yf97214c670a4ce0e5fa6c2721f1d6 */
