package com.zte.uedm.dcdigital.application.brand.executor.Impl;

import com.zte.uedm.dcdigital.application.brand.executor.DocumentInfoTempCommandService;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoFileTempRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoTempRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.FileInfoRepository;
import com.zte.uedm.dcdigital.domain.common.DocumentStatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.AttachmentTypeEnum;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentInfoObj;
import com.zte.uedm.dcdigital.domain.service.DocumentInfoDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoEditDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentInfoDetailVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class DocumentInfoTempCommandServiceImpl implements DocumentInfoTempCommandService {

    /**
     * 注入文档信息仓库，用于执行数据库操作。
     */
    @Autowired
    private DocumentInfoRepository documentInfoRepository;

    @Autowired
    private DocumentInfoTempRepository documentInfoTempRepository;

    /**
     * 注入文件信息仓库，用于处理文件信息。
     */
    @Autowired
    private FileInfoRepository fileInfoRepository;

    @Autowired
    private AuthService authService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private DocumentInfoDomainService documentInfoDomainService;
    @Autowired
    private DocumentInfoFileTempRepository documentInfoFileTempRepository;

    /**
     * 添加新的文档信息。
     *
     * @param dto 文档信息添加数据传输对象
     * @throws BusinessException 业务异常，当文档名称已存在时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addDocument(DocumentInfoAddDto dto) {

        // 创建文档信息对象并复制属性
        DocumentInfoObj obj = new DocumentInfoObj();
        BeanUtils.copyProperties(dto, obj);

        // 设置当前时间
        String currentTime = DateTimeUtils.getCurrentTime();
        obj.setId(UUID.randomUUID().toString());
        obj.setCreateTime(currentTime);
        obj.setUpdateTime(currentTime);
        String userId = authService.getUserId();
        obj.setCreateBy(userId);
        obj.setUpdateBy(userId);

        // 添加文档信息到数据库
        documentInfoTempRepository.addDocumentInfoTemp(obj);

        // 如果附件类型为文件，则插入文件信息
        if (dto.getAttachmentType() == 0) {
            fileInfoRepository.insertFileInfo(obj.getFileIds());
            //documentInfoRepository.addFileIds(obj.getId(), obj.getFileIds());
            documentInfoFileTempRepository.batchAddFileIds(obj.getId(), obj.getFileIds(),userId);
        }
        return obj.getId();
    }

    /**
     * 编辑现有的文档信息。
     *
     * @param dto 文档信息编辑数据传输对象
     * @throws BusinessException 业务异常，当文档不存在或名称冲突时抛出
     */
    /* Started by AICoder, pid:333b3m4eb7u16fd149590969b0b30160d281b0ea */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDocument(DocumentInfoEditDto dto) {
        // 根据ID查询文档信息
        DocumentInfoObj obj = validateDocument(dto);

        // 处理文件信息更新
        updateFileInformation(obj, dto);

        // 更新文档信息对象属性
        updateDocumentProperties(obj, dto);

        // 更新文档信息到数据库
        documentInfoTempRepository.editDocumentInfoTemp(obj);
    }

    private DocumentInfoObj validateDocument(DocumentInfoEditDto dto) {
        DocumentInfoObj obj = documentInfoTempRepository.selectById(dto.getId());
        if (obj == null) {
            //编辑的不是临时文档，是正式文档
            DocumentInfoObj documentInfoObj = documentInfoRepository.selectById(dto.getId());
            if (documentInfoObj == null) {
                throw new BusinessException(DocumentStatusCode.DOCUMENT_NOT_EXIST);
            }
            return documentInfoObj;
        }
        return obj;
    }

    private void updateFileInformation(DocumentInfoObj obj, DocumentInfoEditDto dto) {
        documentInfoDomainService.updateTempFileInformation(obj,dto);
    }

    private void updateDocumentProperties(DocumentInfoObj obj, DocumentInfoEditDto dto) {
        BeanUtils.copyProperties(dto, obj);
        obj.setUpdateTime(DateTimeUtils.getCurrentTime());
        String userId = authService.getUserId();
        obj.setUpdateBy(userId);
        obj.setCreateBy(userId);
    }
    /* Ended by AICoder, pid:333b3m4eb7u16fd149590969b0b30160d281b0ea */

    /**
     * 删除文档信息。
     *
     * @param id 文档ID
     */
    @Override
    public void deleteDocument(String id) {
        documentInfoTempRepository.deleteById(id);
    }

    @Override
    public void deleteTempBidingDocumentByAscriptionId(String ascriptionId) {
        documentInfoDomainService.deleteTempBidingDocumentByAscriptionId(ascriptionId);
    }
}
