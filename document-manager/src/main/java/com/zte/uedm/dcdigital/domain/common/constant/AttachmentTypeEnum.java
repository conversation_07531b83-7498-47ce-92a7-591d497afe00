/* Started by AICoder, pid:i4d95t45dbe2b91141570aa75091bd476b59d55d */
package com.zte.uedm.dcdigital.domain.common.constant;

import lombok.Getter;

/**
 * 枚举类，用于定义附件类型。
 */
@Getter
public enum AttachmentTypeEnum {

    /**
     * 文件类型的枚举值，代码为0。
     */
    FILE(0),

    /**
     * URL类型的枚举值，代码为1。
     */
    URL(1);

    private final int code;

    /**
     * 构造函数，初始化枚举值的代码。
     * @param code 枚举值对应的代码
     */
    AttachmentTypeEnum(int code) {
        this.code = code;
    }
}

/* Ended by AICoder, pid:i4d95t45dbe2b91141570aa75091bd476b59d55d */