/* Started by AICoder, pid:k5842u9971ve05e147ea0ae88160fe0081e5fd9e */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentRelationObj;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoDetailPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoTempPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentAdvancedSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCommonSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentInfoSearchDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.ws.rs.QueryParam;
import java.util.List;

/**
 * DocumentInfoMapper接口，用于定义与文档信息相关的数据库操作。
 */
@Mapper
public interface DocumentInfoMapper extends BaseMapper<DocumentInfoPo> {

    /**
     * 添加文档和文件的关联关系。
     *
     * @param id       文档ID
     * @param fileIds  文件ID列表
     */
    void addFileIds(@Param("id") String id, @Param("fileIds") List<String> fileIds);

    /**
     * 根据文档ID删除关联文件记录。
     *
     * @param id 文档ID
     */
    void deleteByDocumentId(String id);

    /**
     * 根据ID和名称查询名称相同ID不同的文档数量。
     *
     * @param id   文档ID
     * @param name 文档名称
     * @return 符合条件的文档数量
     */
    int selectByIdAndName(@Param("id") String id, @Param("name") String name);

    /**
     * 根据文档ID查询详细信息。
     *
     * @param id 文档ID
     * @return 文档详细信息
     */
    DocumentInfoDetailPo queryDetail(String id);

    /**
     * 根据搜索条件查询文档列表。
     *
     * @param dto 搜索条件
     * @return 文档列表
     */
    List<DocumentInfoVo> queryByCondition(DocumentInfoSearchDto dto);

    /**
     * 查询所有文件ID。
     *
     * @return 文件ID列表
     */
    List<String> queryAllFileIds();

    /**
     * 根据文档ID查询引用计数。
     *
     * @param id 文档ID
     * @return 引用计数
     */
    int selectCitedCountById(@Param("id") String id);

    /**
     * 根据文档ID查询相关文档。
     *
     * @param id 文档ID
     * @return 相关文档列表
     */
    List<DocumentRelationObj> queryRelationById(@Param("id") String id);

    /**
     * 高级搜索文档。
     *
     * @param idList 搜索条件
     * @return 文档列表
     */
    List<DocumentSearchVo> advancedSearch(@Param("idList") List<String> idList);

    /**
     * 常规搜索文档。
     *
     * @param dto 搜索条件
     * @return 文档列表
     */
    List<DocumentSearchVo> commonSearch(@Param("searchDto") DocumentCommonSearchDto dto);
    /* Started by AICoder, pid:b26a68d325l93ee14601091f10873f3a2d907d39 */
    /**
     * 批量关联资源到文档。
     *
     * 它将这些关系批量插入到数据库中，以确保文档和资源之间的关联被正确记录。
     * @param list 一个包含文档和资源关系的DocumentRelationObj对象列表。
     */
    void batchRelateResources(List<DocumentRelationObj> list);

    /**
     * 根据资源ID删除所有相关联的文档关系。
     *
     * 此方法接收一个资源ID，并删除所有与该资源ID相关的文档关系记录。
     * @param resourceId 要删除关系的资源ID。
     */
    void deleteRelationsByResourceId(String resourceId);

    /**
     * 根据文档id删除文档关系。
     * @param documentId
     */
    void deleteRelationsById(String documentId);

    /**
     * 根据资源ID查询所有相关联的文档信息。
     *
     * 此方法接收一个资源ID，并返回所有与该资源ID相关的文档信息列表。
     * @param resourceId 要查询的资源ID。
     * @return 一个包含所有相关文档信息的列表。
     */
    List<DocumentInfoVo> queryByResourceId(String resourceId);

    List<FileInfoVo> selectFileInfosById(String id);

    List<String> advancedSearchIds(DocumentAdvancedSearchDto dto);

    List<DocumentInfoVo> queryByIds(List<String> ids);

    /* Ended by AICoder, pid:b26a68d325l93ee14601091f10873f3a2d907d39 */

    /* Started by AICoder, pid:j7189r0884m101814b0e098960f2b10081170db7 */
    /**
     * 根据资源ID查询文档信息列表。
     *
     * @param id 资源ID，用于查询相关的文档信息。
     * @return 包含查询结果的文档信息列表。如果未找到任何文档，返回空列表。
     */
    List<DocumentInfoVo> queryByAscriptionId(@Param("id") String id,@Param("documentCategoryId") String documentCategoryId);
    /* Ended by AICoder, pid:j7189r0884m101814b0e098960f2b10081170db7 */

    void batchInsert(List<DocumentInfoTempPo> list);

    /* Started by AICoder, pid:669a1l488cg824f149fe08c0503ec1063597df4a */
    /**
     * 根据ID更新品牌ID列表。
     *
     * @param id       要更新的品牌ID对应的记录ID，不能为空。
     * @param brandIds 新的品牌ID列表，不能为空。
     */
    void updateBrandIdsById(@Param("id") String id, @Param("brandIds") String brandIds);
    /* Ended by AICoder, pid:669a1l488cg824f149fe08c0503ec1063597df4a */
    /* Started by AICoder, pid:j124bs8d415509a14ac709deb0dfb2027098e8a7 */
    /**
     * 获取标书分析内容。
     *
     * @param ascriptionId 归属ID
     * @param brandId      品牌ID
     * @return 包含标书分析内容的 DocumentInfoVo 对象列表
     */
    List<DocumentInfoVo> getBidAnalysisContent(@Param("ascriptionId") String ascriptionId, @Param("brandId") String brandId);
    /* Ended by AICoder, pid:j124bs8d415509a14ac709deb0dfb2027098e8a7 */
    Integer getNumByCreateTimeAndAscriptionId(@QueryParam("createTime") String createTime, @QueryParam("ascriptionId") String ascriptionId);
    Integer getNumByTimeAndNotInIds(@QueryParam("createTime") String createTime, @QueryParam("list") List<String> list);
}
/* Ended by AICoder, pid:k5842u9971ve05e147ea0ae88160fe0081e5fd9e */