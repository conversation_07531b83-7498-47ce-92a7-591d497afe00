package com.zte.uedm.dcdigital.infrastructure.repository.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@Getter
@Setter
@ToString
public class DocumentInfoDetailPo {

    /* Started by AICoder, pid:tf9f9b18f49ea54142200abd206fe449ed352748 */

    /**
     * 文档的唯一标识符。
     */
    private String id;

    /**
     * 文档的名称。
     */
    private String name;

    /**
     * 文档的类型。
     */
    private Integer type;

    /**
     * 文档类别的唯一标识符。
     */
    private String documentCategoryId;

    /**
     * 文档类别的名称。
     */
    private String documentCategoryName;

    /**
     * 附件的类型。
     */
    private Integer attachmentType;

    /**
     * 归属的唯一标识符。
     */
    private String ascriptionId;

    /**
     * 文档的描述。
     */
    private String description;

    /**
     * 文档的URL列表。
     */
    private String urls;

    /**
     * 品牌类型 0:通用品牌，1：专属品牌
     */
    private Integer brandType;

    /**
     * 专属品牌id列表
     */
    private String brandIds;
    /* Ended by AICoder, pid:tf9f9b18f49ea54142200abd206fe449ed352748 */
}
