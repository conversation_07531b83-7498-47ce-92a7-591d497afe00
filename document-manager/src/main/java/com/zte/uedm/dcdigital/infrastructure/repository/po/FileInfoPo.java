/* Started by AICoder, pid:x5589hbbe7h32f9146e80a35906dec461e06c8f8 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 文件信息临时持久化对象，用于存储临时文件的元数据。
 */
@Getter
@Setter
@ToString
public class FileInfoPo implements Serializable {

    /**
     * 文件的唯一标识符。
     */
    private String id;

    /**
     * 文件名。
     */
    private String fileName;

    /**
     * 文件路径。
     */
    private String filePath;

    /**
     * 文件后缀（例如：.txt, .pdf）。
     */
    private String fileSuffix;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小，以字节为单位。
     */
    private Integer fileSize;
}

/* Ended by AICoder, pid:x5589hbbe7h32f9146e80a35906dec461e06c8f8 */
