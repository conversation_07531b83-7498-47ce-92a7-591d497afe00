/* Started by AICoder, pid:s602dr907a468de1431e094a40369140df514402 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 角色持久化对象，用于数据库交互。
 */
@Getter
@Setter
@ToString
@TableName("auth_role")
public class RolePo {

    /**
     * 角色唯一标识符
     */
    private String id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色的创建时间
     */
    private String createTime;

    /**
     * 角色的最后更新时间
     */
    private String updateTime;

    /**
     * 创建该角色的用户标识符
     */
    private String createBy;

    /**
     * 最后更新该角色的用户标识符
     */
    private String updateBy;
}

/* Ended by AICoder, pid:s602dr907a468de1431e094a40369140df514402 */