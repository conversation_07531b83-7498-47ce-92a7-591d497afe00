package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.zte.uedm.dcdigital.application.stat.executor.PageVisitCommandService;
import com.zte.uedm.dcdigital.domain.service.PageVisitDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitPontAddDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PageVisitCommandServiceImpl implements PageVisitCommandService {
    @Autowired
    private PageVisitDomainService pageVisitDomainService;

    @Override
    public void addPageVisitData(PageVisitPontAddDto addDto) {
        pageVisitDomainService.addPageVisitData(addDto);
    }
}
