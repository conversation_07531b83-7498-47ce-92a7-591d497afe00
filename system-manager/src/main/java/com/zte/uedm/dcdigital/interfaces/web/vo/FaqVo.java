/* Started by AICoder, pid:m69f544e98t6099146d3098bd0b892631ee327f1 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 常见问题解答视图对象
 */
@Getter
@Setter
@ToString
public class FaqVo {

    /**
     * 唯一标识符
     */
    private String id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 产品小类名称(路径)
     */
    private String categoryPathName;
}

/* Ended by AICoder, pid:m69f544e98t6099146d3098bd0b892631ee327f1 */