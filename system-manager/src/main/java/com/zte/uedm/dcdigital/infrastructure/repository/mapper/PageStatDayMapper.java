/* Started by AICoder, pid:h1ce5118a4k9b58144df086ea0b1ca604bb23325 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 页面访问日统计表 Mapper 接口
 */
@Mapper
public interface PageStatDayMapper {

    /**
     * 添加日统计记录
     *
     * @param po 日统计实体
     * @return 影响行数
     */
    int insertPageStatDay(PageStatDayPo po);

    /**
     * 批量添加日统计记录
     *
     * @param list 日统计实体列表
     * @return 影响行数
     */
    int batchInsertPageStatDay(@Param("list") List<PageStatDayPo> list);

    /**
     * 批量删除日统计记录
     *
     * @param ids ID数组
     * @return 影响行数
     */
    int deletePageStatDayByIds(String[] ids);

    /**
     * 查询日统计列表
     *
     * @param po 查询条件
     * @return 日统计列表
     */
    List<PageStatDayPo> selectPageStatDayList(PageStatDayPo po);

    /**
     * 根据ID查询日统计
     *
     * @param id 主键ID
     * @return 日统计实体
     */
    PageStatDayPo selectPageStatDayById(String id);

    /**
     * 批量更新日统计记录
     *
     * @param poList 日统计实体列表
     * @return 影响行数
     */
    void batchUpdatePageStatDay(List<PageStatDayPo> poList);

    /**
     * 根据条件查询日统计单条记录
     * */
    PageStatDayPo selectPageStatDayDataByCondition(PageStatDayPo po);

    /**
     * 更新用户页面访问日统计记录
     *
     * @param po 用户页面访问日统计实体对象
     */
    int updatePageStatDay(PageStatDayPo po);

    List<PageVisitDto> getPageVisitStatWithDataList(Integer previousDayNumber, Integer previousWeekNumber, Integer previousMonthNumber, Integer previousYearNumber);

}

/* Ended by AICoder, pid:h1ce5118a4k9b58144df086ea0b1ca604bb23325 */