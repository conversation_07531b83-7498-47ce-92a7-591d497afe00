/* Started by AICoder, pid:rb21e1c529a90fd14f290b1f201c534c2392cf1b */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.*;

@Getter
@Setter
public class BusinessStatisticVo {
    /**
     * 统计日期（格式：YYYY-MM-DD）。
     */
    private String day;

    /**
     * 总任务数。
     */
    private Long allTaskNum;

    /**
     * 正常任务数。
     */
    private Long normalTaskNum;

    /**
     * 扩展任务数。
     */
    private Long extensionTaskNum;

    /**
     * 总标书数量。
     */
    private Long allLectotypeNum;

    /**
     * 普通标书数量。
     */
    private Long bidLectotypeNum;

    /**
     * 指定标书数量。
     */
    private Long appointLectotypeNum;
}
/* Ended by AICoder, pid:rb21e1c529a90fd14f290b1f201c534c2392cf1b */