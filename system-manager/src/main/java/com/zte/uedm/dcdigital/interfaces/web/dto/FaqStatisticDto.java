package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class FaqStatisticDto {

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    /**
     * 产品小类ID
     */
    @NotBlank(message = "产品小类ID不能为空")
    private String productCategoryId;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;

    /**
     * 是否下级: 1 只查询下级
     */
    private Integer junior;
}
