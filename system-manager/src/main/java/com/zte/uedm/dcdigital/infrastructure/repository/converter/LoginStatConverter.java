/* Started by AICoder, pid:r4ffbv9206v821514f6a0bd530395463a1147d49 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatYearEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatDayPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatMonthPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatYearPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        imports = {
                LoginStatDayPo.class,
                LoginStatDayEntity.class,
                LoginStatMonthPo.class,
                LoginStatMonthEntity.class,
                LoginStatWeekPo.class,
                LoginStatWeekEntity.class,
                LoginStatYearPo.class,
                LoginStatYearEntity.class
        }
)
public interface LoginStatConverter {
    // 使用MapStruct的实例获取方式
    LoginStatConverter INSTANCE = Mappers.getMapper(LoginStatConverter.class);

    // DayPo转DayEntity
    LoginStatDayEntity dayPoToLoginStatDayEntity(LoginStatDayPo loginStatDayPo);

    // DayPoList转DayEntityList
    List<LoginStatDayEntity> dayPoListToLoginStatDayEntityList(List<LoginStatDayPo> loginStatDayPoList);

    // DayEntity转DayPo
    LoginStatDayPo dayEntityToLoginStatDayPo(LoginStatDayEntity loginStatDayEntity);

    // DayEntityList转DayPoList
    List<LoginStatDayPo> dayEntityListToLoginStatDayPoList(List<LoginStatDayEntity> loginStatDayEntityList);

    // MonthEntity转LoginStatMonthPo
    LoginStatMonthPo monthEntityToPo(LoginStatMonthEntity entity);

    // MonthEntityList转poList
    List<LoginStatMonthPo> monthEntityListToPoList(List<LoginStatMonthEntity> entityList);

    // MonthPoList转entityList
    List<LoginStatMonthEntity> monthPoListToEntityList(List<LoginStatMonthPo> poList);

    // WeekEntityList转WeekPoList
    List<LoginStatWeekPo> weekEntityListToPoList(List<LoginStatWeekEntity> entityList);

    // WeekEntity转WeekPo
    LoginStatWeekPo weekEntityToPo(LoginStatWeekEntity entity);

    // WeekPoList转WeekEntityList
    List<LoginStatWeekEntity> weekPoListToEntityList(List<LoginStatWeekPo> poList);


    // YearEntityList转YearPoList
    List<LoginStatYearPo> yearEntityListToPoList(List<LoginStatYearEntity> entityList);

    // YearEntity转YearPo
    LoginStatYearPo yearEntityToPo(LoginStatYearEntity entity);

    // YearPoList转YearEntityList
    List<LoginStatYearEntity> yearPoListToEntityList(List<LoginStatYearPo> poList);
}

/* Ended by AICoder, pid:r4ffbv9206v821514f6a0bd530395463a1147d49 */