/* Started by AICoder, pid:t235a67e67d128b149a20b3ea013047db270122c */
package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 页面访问年统计数据持久化对象
 */
@Getter
@Setter
@ToString
@TableName("page_stat_year")
public class PageStatYearEntity {
    /**
     * 主键ID（UUID格式）
     */
    private String id;

    /**
     * 统计年份（格式：YYYY，如2025表示2025年）
     */
    @TableField("day")
    private Integer day;

    /**
     * 部门/组织ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 产品类别ID
     */
    @TableField("product_category_id")
    private String productCategoryId;

    /**
     * 类型（1:页面访问, 2:资源下载等）
     */
    @TableField("type")
    private String type;

    /**
     * 资源ID（当type=2时为文件资源ID）
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 访问次数
     */
    @TableField("num")
    private Integer num;

    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;
}

/* Ended by AICoder, pid:t235a67e67d128b149a20b3ea013047db270122c */