/* Started by AICoder, pid:02b30v95fbc60c5142970adb300de8543858fb0a */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 文档下载统计业务实体
 */
@Getter
@Setter
@ToString
public class DocDownQueryDto {
    private Integer operationType; // 5、文档下载-预览，6、文档下载-下载
    /**
     * 资源id
     */
    private String resourceId;
}

/* Ended by AICoder, pid:02b30v95fbc60c5142970adb300de8543858fb0a */