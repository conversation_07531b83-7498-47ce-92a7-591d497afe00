/* Started by AICoder, pid:a49f931952zad3c1425d0a3c514fd303fa953825 */
package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.common.bean.system.*;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.AuthDepartmentRoleResourceEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserRoleResourceEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.AuthDepartmentRoleResourceRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.UserRoleResourceRepository;
import com.zte.uedm.dcdigital.domain.service.ResourceEntityService;
import com.zte.uedm.dcdigital.domain.service.RoleService;
import com.zte.uedm.dcdigital.domain.service.UserRoleResourceService;
import com.zte.uedm.dcdigital.domain.service.UserService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.UserPoConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.UserRoleResourcePoConverter;
import com.zte.uedm.dcdigital.interfaces.web.dto.UserDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UserRoleResourceDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ResourceEntityVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.UserRoleResourceVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserRoleResourceServiceImpl implements UserRoleResourceService {

    @Autowired
    private UserRoleResourceRepository userRoleResourceRepository;
    @Autowired
    private AuthDepartmentRoleResourceRepository authDepartmentRoleResourceRepository;
    @Autowired
    private AuthService authService;

    @Autowired
    private ResourceEntityService resourceEntityService;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;


    @Override
    public List<UserRoleResourceVo> selectAllByUserId(String userId) {
        return userRoleResourceRepository.selectByUserId(userId)
                .stream()
                .map(UserRoleResourcePoConverter.INSTANCE::convertEntityToVo)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserRoleResourceVo> selectAllByRoleId(String roleId) {
        return userRoleResourceRepository.selectByRoleId(roleId)
                .stream()
                .map(UserRoleResourcePoConverter.INSTANCE::convertEntityToVo)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserRoleResourceVo> selectAllByResourceId(String resourceId) {
        return userRoleResourceRepository.selectByResourceId(resourceId)
                .stream()
                .map(UserRoleResourcePoConverter.INSTANCE::convertEntityToVo)
                .collect(Collectors.toList());
    }

    @Override
    public UserRoleResourceVo selectByUserIdAndRoleIdAndResourceId(String userId, String roleId, String resourceId) {
        UserRoleResourceEntity entity = userRoleResourceRepository.selectByUserIdAndRoleIdAndResourceId(userId, roleId, resourceId);
        return UserRoleResourcePoConverter.INSTANCE.convertEntityToVo(entity);
    }

    @Transactional
    @Override
    public void insert(UserRoleResourceDto dto) {
        ResourceEntityVo resourceEntityVo = resourceEntityService.insert(dto.getResourceEntityDto());
        UserVo user = userService.createUser(dto.getUserDto());
        UserRoleResourceEntity entity = UserRoleResourcePoConverter.INSTANCE.convertDtoToEntity(dto);
        entity.setId(UUID.randomUUID().toString());
        entity.setUserId(user.getId());
        RoleVo roleVo = roleService.selectByCode(dto.getRoleCode());
        entity.setRoleId(roleVo.getId());
        entity.setResourceId(resourceEntityVo.getId());
        userRoleResourceRepository.insert(entity);
    }

    @Transactional
    @Override
    public void insertBatch(List<UserRoleResourceDto> dtos,List<DepartmentRoleDto> departmentRoleDtoList,ResourceEntityDto entityDto) {
        log.info("insertBatch dtos:{}", dtos);
        String userId = authService.getUserId();
        List<String> roleCods = dtos.stream().map(UserRoleResourceDto::getRoleCode).distinct().collect(Collectors.toList());
        log.info("roleCods:{}", roleCods);
        List<RoleVo> roleVos = roleService.selectByCodes(roleCods);
        log.info("roleVos:{}", roleVos);
        Map<String, String> collect = roleVos.stream().collect(Collectors.toMap(RoleVo::getCode, RoleVo::getId));
        ResourceEntityVo entityVo = resourceEntityService.selectByEntityId(entityDto.getEntityId());
        if (entityVo == null) {
            entityVo = resourceEntityService.insert(entityDto);
        }
        ResourceEntityVo finalEntityVo = entityVo;
        List<UserRoleResourceEntity> entities = dtos.stream().map(dto -> {
            UserRoleResourceEntity entity = UserRoleResourcePoConverter.INSTANCE.convertDtoToEntity(dto);
            UserDto userDto = dto.getUserDto();
            if(userDto.getId() != null){
                UserVo user = userService.createUser(dto.getUserDto());
                entity.setId(UUID.randomUUID().toString());
                entity.setUserId(user.getId());
                entity.setRoleId(collect.get(dto.getRoleCode()));
                entity.setResourceId(finalEntityVo.getId());
                entity.setCreateTime(DateTimeUtils.getCurrentTime());
                entity.setUpdateTime(DateTimeUtils.getCurrentTime());
                entity.setCreateBy(userId);
                entity.setUpdateBy(userId);
            }
            return entity;
        }).collect(Collectors.toList()).stream().filter(item->item.getUserId()!=null).collect(Collectors.toList());
        if (!entities.isEmpty()) {
            userRoleResourceRepository.insertBatch(entities);
        }
        //TODO 分组绑定的资源，记录
        //记录资源和分组的记录
        updateGroupRelationResource(departmentRoleDtoList,collect,userId,finalEntityVo.getId());
    }

    private void updateGroupRelationResource(List<DepartmentRoleDto> departmentRoleDtoList, Map<String, String> roleMap, String userId, String resourceId) {
        authDepartmentRoleResourceRepository.deleteByResourceId(resourceId);
        if (CollectionUtils.isEmpty(departmentRoleDtoList)) {
            return;
        }
        List<AuthDepartmentRoleResourceEntity> entities = new ArrayList<>();
        String currentTime = DateTimeUtils.getCurrentTime();
        departmentRoleDtoList.forEach(dto -> {
            String roleCode = dto.getRoleCode();
            String roleId = roleMap.get(roleCode);
            dto.getDepartmentIds().forEach(departmentId -> {
                entities.add(createGroupEntity(departmentId, roleId,currentTime, userId, resourceId));
            });
        });
        authDepartmentRoleResourceRepository.insertBatch(entities);
    }

    private AuthDepartmentRoleResourceEntity createGroupEntity(String departmentId, String roleId, String currentTime,
                                                               String updater, String resourceId) {
        AuthDepartmentRoleResourceEntity entity = new AuthDepartmentRoleResourceEntity();
        entity.setId(UUID.randomUUID().toString());
        entity.setDepartmentId(departmentId);
        entity.setRoleId(roleId);
        entity.setResourceId(resourceId);
        entity.setCreateTime(currentTime);
        entity.setUpdateTime(currentTime);
        entity.setCreateBy(updater);
        entity.setUpdateBy(updater);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateResource(ResourceDto resourceDto) {
        log.info("updateResource resourceDto:{}", resourceDto);
        String entityId = resourceDto.getResourceEntityDto().getEntityId();
        ResourceEntityVo resourceEntityVo = resourceEntityService.selectByEntityId(entityId);
        if (resourceEntityVo == null) {
            log.error("resourceEntityVo is null");
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
        //auth_user_role_resource resource_id
        String resourceId = resourceEntityVo.getId();

        List<UserRoleDto> userRoleDtoList = resourceDto.getUserRoleDtoList();
        List<String> roleCods = userRoleDtoList.stream().map(UserRoleDto::getRoleCode).collect(Collectors.toList());
        log.info("roleCods:{}", roleCods);
        List<RoleVo> roleVos = roleService.selectByCodes(roleCods);
        List<String> roleIds = Optional.ofNullable(roleVos).orElse(Collections.emptyList()).stream().map(RoleVo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        userRoleResourceRepository.deleteByResourceIdAndRoleId(resourceId,roleIds);
        Map<String, String> roleMap = roleVos.stream().collect(Collectors.toMap(RoleVo::getCode, RoleVo::getId));
        List<UserRoleResourceEntity> entities = new ArrayList<>();
        List<UserRoleDto> userRoleDtos = userRoleDtoList.stream().filter(item -> item.getId() != null).collect(Collectors.toList());
        for (UserRoleDto userRoleDto : userRoleDtos) {
            UserDto userDto = new UserDto();
            userDto.setId(userRoleDto.getId());
            userDto.setName(userRoleDto.getName());
            userDto.setEmail(userRoleDto.getEmail());
            userDto.setEmployeeId(userRoleDto.getEmployeeId());
            UserVo user = userService.createUser(userDto);
            String userId = user.getId();
            UserRoleResourceEntity userRoleResource = new UserRoleResourceEntity();
            userRoleResource.setId(UUID.randomUUID().toString());
            userRoleResource.setUserId(userId);
            userRoleResource.setRoleId(roleMap.get(userRoleDto.getRoleCode()));
            userRoleResource.setResourceId(resourceEntityVo.getId());
            userRoleResource.setCreateTime(DateTimeUtils.getCurrentTime());
            userRoleResource.setUpdateTime(DateTimeUtils.getCurrentTime());
            userRoleResource.setCreateBy(userId);
            userRoleResource.setUpdateBy(userId);
            entities.add(userRoleResource);
        }
        if (!entities.isEmpty()) {
            userRoleResourceRepository.insertBatch(entities);
        }
        String userId = authService.getUserId();
        List<DepartmentRoleDto> groupRoleDtoList = resourceDto.getDepartmentRoleDtoList();
        updateGroupRelationResource(groupRoleDtoList,roleMap,userId,resourceEntityVo.getId());
    }

    @Transactional
    @Override
    public void deleteByEntityId(String entityId) {
        ResourceEntityVo resourceEntityVo = resourceEntityService.selectByEntityId(entityId);
        resourceEntityService.deleteByEntityId(entityId);
        if (resourceEntityVo != null) {
            userRoleResourceRepository.deleteByResourceId(resourceEntityVo.getId());
            authDepartmentRoleResourceRepository.deleteByResourceId(resourceEntityVo.getId());
        }
    }

    @Override
    public List<UserVo> selectUserByRoleCodeAndResourceId(String roleCode, String resourceId) {
        List<UserEntity> userEntities = userRoleResourceRepository.selectUserByRoleCodeAndResourceId(roleCode, resourceId);
        return UserPoConverter.INSTANCE.convertEntitiesToVos(userEntities);
    }

    @Override
    public List<RoleVo> selectRoleByUserIdAndResourceId(String userId, String resourceId) {
        return userRoleResourceRepository.selectRoleByUserIdAndResourceId(userId, resourceId);
    }

    @Override
    public void disbandDepartment(String departmentId) {
        authDepartmentRoleResourceRepository.deleteByDepartmentId(departmentId);
    }
}

/* Ended by AICoder, pid:a49f931952zad3c1425d0a3c514fd303fa953825 */