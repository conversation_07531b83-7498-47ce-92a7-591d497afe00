/* Started by AICoder, pid:2d27bt2c20c9c3d144770bb2d0e9704454756f2c */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatYearEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LoginStatYearRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.LoginStatConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatYearMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatYearPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class LoginStatYearRepositoryImpl implements LoginStatYearRepository {

    @Autowired
    private LoginStatYearMapper loginStatYearMapper;

    @Override
    public void addLoginStatYearList(List<LoginStatYearEntity> yearEntityList) {
        if (CollectionUtils.isEmpty(yearEntityList)) {
            return;
        }
        List<LoginStatYearPo> poList = LoginStatConverter.INSTANCE.yearEntityListToPoList(yearEntityList);
        loginStatYearMapper.batchInsertLoginStatYear(poList);
    }

    @Override
    public List<LoginStatYearEntity> getLoginStatYearList(LoginStatYearEntity yearEntity) {
        LoginStatYearPo loginStatYearPo = LoginStatConverter.INSTANCE.yearEntityToPo(yearEntity);
        List<LoginStatYearPo> loginStatWeekPos = loginStatYearMapper.selectLoginStatYearList(loginStatYearPo);
        return LoginStatConverter.INSTANCE.yearPoListToEntityList(loginStatWeekPos);
    }

    @Override
    public void updateLoginStatYearList(List<LoginStatYearEntity> yearEntityList) {
        if (CollectionUtils.isEmpty(yearEntityList)) {
            return;
        }
        List<LoginStatYearPo> poList = LoginStatConverter.INSTANCE.yearEntityListToPoList(yearEntityList);
        loginStatYearMapper.batchUpdateLoginStatYear(poList);
    }

    @Override
    public void deleteByYearNumber(Integer yearNumber) {
        loginStatYearMapper.deleteByYearNumber(yearNumber);
    }
}

/* Ended by AICoder, pid:2d27bt2c20c9c3d144770bb2d0e9704454756f2c */