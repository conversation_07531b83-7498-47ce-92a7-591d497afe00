package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

/* Started by AICoder, pid:x3765848d7t6b1e14c930871203b5b4799d4b4fb */
import lombok.Getter;
import lombok.Setter;

/**
 * 资源实体类，用于表示资源与实体的关联信息。
 */
@Getter
@Setter
public class ResourceEntity {

    /**
     * 资源-实体关联ID。
     */
    private String id;

    /**
     * 实体ID。
     */
    private String entityId;

    private String parentId;

    /**
     * 分类。
     */
    private Integer type;

    /**
     * 用户的创建时间
     */
    private String createTime;

    /**
     * 用户的最后更新时间
     */
    private String updateTime;

    /**
     * 创建该记录的用户标识符。
     */
    private String createBy;

    /**
     * 最后更新该记录的用户标识符。
     */
    private String updateBy;
}

/* Ended by AICoder, pid:x3765848d7t6b1e14c930871203b5b4799d4b4fb */
