/* Started by AICoder, pid:q909fpd90ei136114baf0bc3b0fc2a44b075c945 */
package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AuthDeptEntity {
    // 部门ID
    private String deptId;

    // 父部门ID
    private String parentId;

    // 部门名称
    private String deptName;

    // 显示顺序
    private Integer orderNum;

    // 部门所在层级
    private Integer deptLevel;

    // 组织路径
    private String deptPath;

    // 组织路径id
    private String deptPathId;

    // 负责人
    private String leader;

    // 备注
    private String remark;

    // 创建者
    private String createBy;

    // 创建时间
    private String createTime;

    // 更新者
    private String updateBy;

    // 更新时间
    private String updateTime;
}

/* Ended by AICoder, pid:q909fpd90ei136114baf0bc3b0fc2a44b075c945 */