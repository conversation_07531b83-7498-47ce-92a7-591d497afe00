/* Started by AICoder, pid:v4f1dj9f16v54dd149270923805de42c6aa89c8a */
package com.zte.uedm.dcdigital.application.stat.scheduler;

import com.zte.uedm.dcdigital.domain.service.DocAssetDomainService;
import com.zte.uedm.dcdigital.domain.service.DocDownStatsDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 这里是文档下载统计相关的定时任务 主要用于将日表数据统计到周表、月表、年表
 */
@Slf4j
@Component
public class DocAssetScheduledTasks {
    @Autowired
    private DocAssetDomainService docAssetDomainService;

     //开发测试时使用 5分钟执行一次
//     @Scheduled(cron = "0 0/5 * * * ?")

    // 线上暂定每天凌晨1点执行
    @Scheduled(cron = "0 1 1 * * ?")
    public void synchronizeDocAssetData() {
        log.info("The doc asset scheduled task begins to execute");
        docAssetDomainService.synchronizeDocAssetData();
        log.info("synchronizeDocAssetData executed successfully");
    }
    //日表1分钟执行一次
    @Scheduled(cron = "0 0/1 * * * ?")
    public void synchronizeDocAssetDayData() {
        log.info("The doc asset scheduled task begins to execute");
        docAssetDomainService.synchronizeDocAssetDayData();
        log.info("synchronizeDocAssetData executed successfully");
    }
}

/* Ended by AICoder, pid:v4f1dj9f16v54dd149270923805de42c6aa89c8a */