/* Started by AICoder, pid:nccfbzf68402f161477b0a9a10634d691bd0ffa7 */
package com.zte.uedm.dcdigital.interfaces.web.controller.stat;

import com.zte.uedm.dcdigital.application.stat.executor.BusinessStatService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Slf4j
@Path("/uportal/business")
@Api(value = "业务过程统计", tags = {"业务过程统计"})
@Controller
public class BusinessStatController {

    @Autowired
    private BusinessStatService businessStatService;

    /**
     * 页面访问统计（智能体）。
     *
     * @param queryDto 查询条件传输对象
     * @return 包含统计结果的 BaseResult 对象
     */
    @POST
    @Path("/stat")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "统计", notes = "统计", httpMethod = "POST")
    public BaseResult<Object> statisticIntelligence(BusinessStatisticQueryDto queryDto) {
        log.info("statisticIntelligence start, queryDto: {}", queryDto);
        List<BusinessStatisticVo> result = businessStatService.statBusiness(queryDto);
        return BaseResult.success(result);
    }

    /**
     * 统计任务。
     *
     * @return 包含操作结果的 BaseResult 对象
     */
    @POST
    @Path("/statTask")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "统计任务", notes = "统计任务", httpMethod = "POST")
    public BaseResult<Object> statTask(@QueryParam("time")String time) {
        businessStatService.synchronizeBusinessStatData(time);
        return BaseResult.success();
    }
}
/* Ended by AICoder, pid:nccfbzf68402f161477b0a9a10634d691bd0ffa7 */