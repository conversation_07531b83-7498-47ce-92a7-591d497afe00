/* Started by AICoder, pid:0b4ffe139b1a1d4146e40be870d6836007c61ac9 */
package com.zte.uedm.dcdigital.interfaces.inner.controller;

import com.zte.uedm.dcdigital.application.system.executor.ResourceCommandService;
import com.zte.uedm.dcdigital.application.system.executor.ResourceQueryService;
import com.zte.uedm.dcdigital.common.bean.system.ResourceDto;
import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.UserRoleResourceService;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ResourceQueryVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.Collections;
import java.util.List;

@Controller
@Path("/resource-inner")
public class ResourceInnerController {

    @Autowired
    private ResourceCommandService resourceCommandService;
    @Autowired
    private ResourceQueryService resourceQueryService;
    @Autowired
    private UserRoleResourceService userRoleResourceService;

    @POST
    @Path("/create")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "创建资源", notes = "创建资源", httpMethod = "POST")
    public BaseResult<Object> createResource(@RequestBody ResourceDto resourceDto) {
        resourceCommandService.createResource(resourceDto);
        return BaseResult.success();
    }

    @POST
    @Path("/update")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "更新资源", notes = "更新资源", httpMethod = "POST")
    public BaseResult<Object> updateResource(@RequestBody ResourceDto resourceDto) {
        resourceCommandService.updateResource(resourceDto);
        return BaseResult.success();
    }

    @POST
    @Path("/delete")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "删除资源", notes = "删除资源", httpMethod = "POST")
    public BaseResult<Object> deleteResource(@QueryParam("resourceEntityId") String resourceEntityId) {
        resourceCommandService.deleteResource(resourceEntityId);
        return BaseResult.success();
    }

    @GET
    @Path("/query-by-userId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据用户ID查询资源", notes = "根据用户ID查询资源", httpMethod = "GET")
    public BaseResult<List<String>> queryResourceByUserId(@QueryParam("userId") String userId, @QueryParam("type") Integer type) {
        List<String> ids = resourceQueryService.queryResourceByUserId(userId, type);
        return BaseResult.success(ids);
    }

    @GET
    @Path("/query-all")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询所有资源", notes = "查询所有资源", httpMethod = "GET")
    public BaseResult<List<ResourceQueryVo>> queryAllResources(@QueryParam("type") Integer type) {
        List<ResourceQueryVo> resources = resourceQueryService.queryAllResource(type);
        return BaseResult.success(resources);
    }

    @GET
    @Path("/query-user-by-roleCode-resourceId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据资源ID和角色code查询用户信息", notes = "根据资源ID和角色code查询用户信息", httpMethod = "GET")
    public BaseResult<List<UserVo>> queryUserByRoleCodeAndResourceId(@QueryParam("roleCode") String roleCode, @QueryParam("resourceId") String resourceId) {
        List<UserVo> userVos = userRoleResourceService.selectUserByRoleCodeAndResourceId(roleCode, resourceId);
        return BaseResult.success(userVos);
    }

    @GET
    @Path("/query-role-by-userId-resourceId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据资源ID和用户ID查询角色信息", notes = "根据资源ID和用户ID查询角色信息", httpMethod = "GET")
    public BaseResult<List<RoleVo>> queryRoleByUserIdAndResourceId(@QueryParam("userId") String userId, @QueryParam("resourceId") String resourceId) {
        List<RoleVo> roleVos = userRoleResourceService.selectRoleByUserIdAndResourceId(userId, resourceId);
        return BaseResult.success(roleVos);
    }

    @GET
    @Path("/get-entityIds-by-userId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据用户ID查询资源ID", notes = "根据用户ID查询资源ID", httpMethod = "GET")
    public BaseResult<List<String>> getEntityIdsByUserId(@QueryParam("userId") String userId) {
        return BaseResult.success();
    }

    @POST
    @Path("/get-by-ids-roleCode")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据资源id列表和角色code查询资源信息", notes = "根据资源id列表和角色code查询资源信息", httpMethod = "POST")
    public BaseResult<List<ResourceVo>> getResourceByIdsAndRoleCode(@QueryParam("ids") List<String> ids, @QueryParam("roleCode") String roleCode){
        List<ResourceVo> res = resourceQueryService.getResourceByIdAndRoleCode(ids, roleCode);
        return BaseResult.success(res);
    }

    @POST
    @Path("/get-by-id")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据资源id查询资源信息", notes = "根据资源id查询资源信息", httpMethod = "POST")
    public BaseResult<List<ResourceVo>> getResourceById(@QueryParam("id") String id) {
        List<ResourceVo> res = resourceQueryService.getResourceByIdAndRoleCode(Collections.singletonList(id),  "");
        return BaseResult.success(res);
    }

    /* Started by AICoder, pid:7543fi35f75d6b9145610864f0f1340a2288ff17 */
    @POST
    @Path("/get-areaIds-by-userId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据用户ID查询区域ID", notes = "根据用户ID查询区域ID", httpMethod = "POST")
    public BaseResult<List<String>> getAreaIdsByUserId(@QueryParam("userId") String userId,@QueryParam("roleCode") String roleCode) {
        List<String> areaIds = resourceQueryService.getAreaIdsByUserId(userId,roleCode);
        return BaseResult.success(areaIds);
    }

    @POST
    @Path("/query-by-resource-and-role")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据资源id及角色查询资源信息", notes = "根据资源id及角色查询资源信息", httpMethod = "POST")
    public BaseResult<ResourceVo> queryByResourceAndRoleCode(@QueryParam("resourceId") String resourceId,@QueryParam("roleCodes") List<String> roleCodes) {
        //会查询分组
        ResourceVo res = resourceQueryService.queryByResourceAndRoleCode(resourceId, roleCodes);
        return BaseResult.success(res);
    }
}

/* Ended by AICoder, pid:0b4ffe139b1a1d4146e40be870d6836007c61ac9 */