/* Started by AICoder, pid:43c30c6717m51d914a190b17d0ca12213697034f */
package com.zte.uedm.dcdigital.application.stat.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo;

import java.util.List;

/**
 * 业务统计服务接口。
 * 提供业务统计数据的查询和同步功能。
 */
public interface BusinessStatService {

    /**
     * 根据查询条件统计业务数据。
     *
     * @param queryDto 查询条件传输对象
     * @return 包含业务统计数据的 BusinessStatisticVo 对象列表
     */
    List<BusinessStatisticVo> statBusiness(BusinessStatisticQueryDto queryDto);

    /**
     * 同步业务统计数据。
     * 该方法用于定期同步业务统计数据，确保数据的一致性和准确性。
     */
    void synchronizeBusinessStatData(String time);

    /* Started by AICoder, pid:b1fc1j498f8d283145b30b08d0d1250c8e18bfff */
    /**
     * 统计业务数据概览。
     *
     * @param endTime   结束时间（格式：YYYY-MM-DD）
     * @param timeType  时间类型：1-天，2-周，3-月，4-年
     * @return 包含统计结果的 BusinessStatisticVo 对象
     */
    BusinessStatisticVo overviewStatBusiness(String startTime, String endTime, Integer timeType);
    /* Ended by AICoder, pid:b1fc1j498f8d283145b30b08d0d1250c8e18bfff */
}
/* Ended by AICoder, pid:43c30c6717m51d914a190b17d0ca12213697034f */