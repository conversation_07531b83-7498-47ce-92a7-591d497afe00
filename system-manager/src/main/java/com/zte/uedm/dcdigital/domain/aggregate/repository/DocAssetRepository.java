package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetUpdEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetAddDto;

import java.util.List;

public interface DocAssetRepository {
    DocAssetUpdEntity getDataByDayAndId(DocAssetAddDto addDto);

    void addRecord(DocAssetUpdEntity entity);

    Integer getDataByDayAndProductCategoryId(Integer beginDay, Integer endDay, String productCategoryId);


}
