/* Started by AICoder, pid:nf908r409ec6745142320b712060d54688e55e67 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatWeekPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档下载周统计表 Mapper 接口
 */
@Mapper
public interface DocDownStatWeekMapper {




//    /**
//     * 查询周统计列表
//     *
//     * @param po 查询条件
//     * @return 周统计列表
//     */
//    List<LoginStatWeekPo> selectLoginStatWeekList(LoginStatWeekPo po);


    /**
     * 批量添加周统计记录
     *
     * @param poList 周统计实体
     * @return 影响行数
     */
    void batchInsertDocDownStatWeek(@Param("list") List<DocDownStatWeekPo> poList);

    /**
     * 批量更新周统计记录
     *
     * @param poList 周统计实体
     * @return 影响行数
     */
    void batchUpdateDocDownStatWeek(@Param("list") List<DocDownStatWeekPo> poList);

    List<DocDownStatWeekPo> getDownAndViewNum(@Param("item")DocDownStatWeekPo weekPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatWeekPo> getPreviewTop50Data(@Param("item")DocDownStatWeekPo weekPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatWeekPo> getDownloadTop50Data(@Param("item")DocDownStatWeekPo weekPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatWeekPo> getPreviewDetailsData(@Param("item")DocDownStatWeekPo weekPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatWeekPo> getDownloadDetailsData(@Param("item")DocDownStatWeekPo weekPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatEntity> getUserStatisticFileDate(@Param("item")DocDownStatEntity entity, @Param("list")List<String> deptUserIds);

    List<DocDownStatWeekPo> selectByTimeRange(@Param("startTime")Integer startTime, @Param("endTime")Integer endTime);
}

/* Ended by AICoder, pid:nf908r409ec6745142320b712060d54688e55e67 */