package com.zte.uedm.dcdigital.application.faq.executor.impl;

import com.zte.uedm.dcdigital.application.faq.executor.FaqQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.domain.service.FaqDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.FaqVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class FaqQueryServiceImpl implements FaqQueryService {

    @Autowired
    private FaqDomainService faqDomainService;

    @Override
    public FaqVo getById(String faqId) {
        FaqVo faqVo = faqDomainService.getById(faqId);
        return faqVo;
    }

    @Override
    public PageVO<FaqVo> queryByCondition(FaqQueryDto faqQueryDto) {
        return faqDomainService.queryByCondition(faqQueryDto);
    }

    @Override
    public List<DocumentCitedVo> selectCitedList(List<String> ids, Integer type) {
        if (DocumentRelateResourceTypeEnum.FAQ.getCode() != type) {
            return Collections.emptyList();
        }
        return faqDomainService.selectCitedList(ids);
    }

    /* Started by AICoder, pid:mf59a09357m0da81459f091d008c1a1df730750a */
    @Override
    public Boolean existRelatedFaq(String resourceId) {
        return faqDomainService.existRelatedFaqByResourceId(resourceId);
    }
    /* Ended by AICoder, pid:mf59a09357m0da81459f091d008c1a1df730750a */
}
