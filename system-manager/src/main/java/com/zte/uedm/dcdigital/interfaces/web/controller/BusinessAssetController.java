package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssetBuryingPointDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetStatVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 商机资产统计控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Path("/uportal/asset")
@Api(value = "商机资产统计", tags = {"商机资产统计接口"})
@Controller
public class BusinessAssetController {

    @Autowired
    private BusinessAssetDomainService businessAssetDomainService;

    /**
     * 商机数量统计和变化统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @POST
    @Path("/statBusiness")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "商机数量统计和变化统计",
            notes = "按时间维度统计商机总数和变化情况，支持天/周/月/年维度，支持下级商机变化查询",
            httpMethod = "POST"
    )
    public BaseResult<BusinessAssetStatVo> statBusiness(BusinessAssetQueryDto queryDto) {
        log.info("Business asset statistics request received, queryDto: {}", queryDto);
        
        try {

            // 调用业务服务
            BusinessAssetStatVo result = businessAssetDomainService.getStatBusiness(queryDto);
            
            log.info("Business asset statistics completed successfully, timeStatList size: {}, areaStatList size: {}",
                    result.getTimeStatList() != null ? result.getTimeStatList().size() : 0,
                    result.getAreaStatList() != null ? result.getAreaStatList().size() : 0);
            
            return BaseResult.success(result);
            
        } catch (Exception e) {
            log.error("Error processing business asset statistics request", e);
            return BaseResult.failed("Failed to get business asset statistics: " + e.getMessage());
        }
    }

    /**
     * 商机统计数据导出
     *
     */
    @POST
    @Path("/statBusinessExport")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "商机统计数据导出",
            notes = "导出商机统计数据为Excel文件",
            httpMethod = "POST"
    )
    public void statBusinessExport(BusinessAssetQueryDto queryDto) {
        log.info("Business asset statistics export request received, queryDto: {}", queryDto);

        try {

            // 调用导出服务
            businessAssetDomainService.exportBusinessAssetStat(queryDto);

            log.info("Business asset statistics export completed successfully");

        } catch (Exception e) {
            log.error("Error processing business asset statistics export request", e);
            throw new RuntimeException("Failed to export business asset statistics: " + e.getMessage());
        }
    }

    @POST
    @Path("/buryingPoints")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "前端埋点接口",
            notes = "处理商机启动投标埋点数据",
            httpMethod = "POST"
    )
    public BaseResult<String> buryingPoint(AssetBuryingPointDto buryingPointDto) {

        try {
            // 参数校验
            if (buryingPointDto.getProjectId() == null || buryingPointDto.getProjectId().trim().isEmpty()) {
                return BaseResult.failed("商机ID不能为空");
            }
            if (buryingPointDto.getAreaId() == null || buryingPointDto.getAreaId().trim().isEmpty()) {
                return BaseResult.failed("地区ID不能为空");
            }

            // 处理埋点数据
            businessAssetDomainService.processBusinessStartBuryingPointAsync(
                    buryingPointDto.getProjectId(),
                    buryingPointDto.getAreaId()
            );

            log.info("Asset burying point processed successfully for projectId: {}, areaId: {}",
                    buryingPointDto.getProjectId(), buryingPointDto.getAreaId());
            return BaseResult.success("Burying point data processed successfully");

        } catch (Exception e) {
            log.error("Error processing asset burying point request", e);
            return BaseResult.failed("Failed to process burying point data: " + e.getMessage());
        }
    }
}
