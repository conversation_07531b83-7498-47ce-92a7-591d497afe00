/* Started by AICoder, pid:n870ez7e06vbbc1149a90bbb5062c85947266629 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 用户角色权限菜单信息传输对象。
 * 
 * 该类用于封装用户的角色、菜单和权限信息，以便在Web层进行数据传输。
 * 
 * <AUTHOR> UEDM DC Digital Team
 */
@Getter
@Setter
@ToString
public class UserRolePermissionMenuVo {

    /**
     * 工号。
     * 
     * 用于标识用户的唯一工号。
     */
    private String id;

    /**
     * 姓名。
     * 
     * 用户的姓名信息。
     */
    private String name;

    /**
     * 角色列表。
     * 
     * 用户所拥有的角色信息列表。
     */
    private List<RoleVo> roles;

    /**
     * 菜单列表。
     * 
     * 用户所拥有的菜单信息列表。
     */
    private List<MenuListVo> menuListVos;

    /**
     * 权限列表。
     * [资源类型--->资源id->操作权限集合]
     * 用户所拥有的权限信息列表。
     */
    private Map<String, Map<String, List<PermissionResourceVo>>> permissions;
}


/* Ended by AICoder, pid:n870ez7e06vbbc1149a90bbb5062c85947266629 */