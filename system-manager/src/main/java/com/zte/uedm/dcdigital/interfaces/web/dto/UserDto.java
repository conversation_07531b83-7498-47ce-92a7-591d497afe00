/* Started by AICoder, pid:bde02z69a493e43145d0094c00886b510915f029 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 用户数据传输对象，用于在不同层之间传递用户信息。
 */
@Getter
@Setter
public class UserDto {

    /**
     * 用户的唯一标识符。
     */
    private String id;

    /**
     * 用户的姓名。
     */
    private String name;

    /**
     * 用户的电子邮件地址。
     */
    private String email;

    /**
     * 用户的员工编号。
     */
    private String employeeId;

    /**
     * 用户的电话号码。
     */
    private String phoneNumber;

    /**
     * 用户的创建时间
     */
    private String createTime;

    /**
     * 用户的最后更新时间
     */
    private String updateTime;

    /**
     * 创建该用户的用户标识符。
     */
    private String createBy;

    /**
     * 最后更新该用户的用户标识符。
     */
    private String updateBy;
}

/* Ended by AICoder, pid:bde02z69a493e43145d0094c00886b510915f029 */