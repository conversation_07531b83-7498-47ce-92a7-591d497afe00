/* Started by AICoder, pid:7ec2f62a9dbdeaa148f4084fb02fab47ba059bec */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LoginStatWeekRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.LoginStatConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatWeekMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class LoginStatWeekRepositoryImpl implements LoginStatWeekRepository {

    @Autowired
    private LoginStatWeekMapper loginStatWeekMapper;

    @Override
    public void addLoginStatWeekList(List<LoginStatWeekEntity> weekEntityList) {
        if (CollectionUtils.isEmpty(weekEntityList)) {
            return;
        }
        List<LoginStatWeekPo> poList = LoginStatConverter.INSTANCE.weekEntityListToPoList(weekEntityList);
        loginStatWeekMapper.batchInsertLoginStatWeek(poList);
    }

    @Override
    public List<LoginStatWeekEntity> getLoginStatWeekList(LoginStatWeekEntity weekEntity) {
        LoginStatWeekPo loginStatWeekPo = LoginStatConverter.INSTANCE.weekEntityToPo(weekEntity);
        List<LoginStatWeekPo> loginStatWeekPos = loginStatWeekMapper.selectLoginStatWeekList(loginStatWeekPo);
        return LoginStatConverter.INSTANCE.weekPoListToEntityList(loginStatWeekPos);
    }

    @Override
    public void updateLoginStatWeekList(List<LoginStatWeekEntity> weekEntityList) {
        if (CollectionUtils.isEmpty(weekEntityList)) {
            return;
        }
        List<LoginStatWeekPo> poList = LoginStatConverter.INSTANCE.weekEntityListToPoList(weekEntityList);
        loginStatWeekMapper.batchUpdateLoginStatWeek(poList);
    }

    @Override
    public void deleteByWeekNumber(Integer weekNumber) {
        loginStatWeekMapper.deleteByWeekNumber(weekNumber);
    }
}

/* Ended by AICoder, pid:7ec2f62a9dbdeaa148f4084fb02fab47ba059bec */