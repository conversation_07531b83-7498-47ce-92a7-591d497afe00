/* Started by AICoder, pid:p94afs34ef01ccc148d70a376043325e583184a9 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.EvalChecklistPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EvalChecklistMapper extends BaseMapper<EvalChecklistPo> {

    /**
     * 新增评价功能点
     *
     * @param evalChecklist 评价功能点对象
     * @return 影响行数
     */
    int insertEvalChecklist(EvalChecklistPo evalChecklist);

    /**
     * 查询评价功能点列表（支持条件查询）
     *
     * @param evalChecklist 查询条件封装对象
     * @return 符合条件的评价功能点列表
     */
    List<EvalChecklistPo> selectEvalChecklistList(EvalChecklistPo evalChecklist);

    /**
     * 批量删除评价功能点
     *
     * @param ids 需要删除的ID数组
     * @return 影响行数
     */
    int deleteEvalChecklistByIds(String[] ids);

    /**
     * 根据主键ID查询评价功能点
     *
     * @param id 主键ID
     * @return 对应的评价功能点对象
     */
    EvalChecklistPo selectEvalChecklistById(String id);

    /**
     * 更新评价功能点信息
     *
     * @param evalChecklist 包含更新信息的评价功能点对象
     * @return 影响行数
     */
    int updateEvalChecklist(EvalChecklistPo evalChecklist);
}

/* Ended by AICoder, pid:p94afs34ef01ccc148d70a376043325e583184a9 */