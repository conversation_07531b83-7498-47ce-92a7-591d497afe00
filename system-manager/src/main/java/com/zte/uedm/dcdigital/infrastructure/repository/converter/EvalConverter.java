/* Started by AICoder, pid:u2756477c7777c714d5d093e104b7d3a2065c5e2 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.EvalDataEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.EvalDataPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.EvalDataDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface EvalConverter {
    // 使用MapStruct的实例获取方式
    EvalConverter INSTANCE = Mappers.getMapper(EvalConverter.class);

    /**
     * PO列表转实体对象列表
     */
    List<EvalDataEntity> evalDataPoListToEvalDataEntityList(List<EvalDataPo> evalPoList);

    /**
     * DTO转PO
     */
    EvalDataPo evalDataDtoToEvalDataPo(EvalDataDto evalDataDto);

    /**
     * PO转实体对象
     */
    EvalDataEntity evalDataPoToEvalDataEntity(EvalDataPo evalDataPo);

    /**
     * 实体对象转PO
     */
    EvalDataPo evalDataEntityToEvalDataPo(EvalDataEntity evalDataEntity);
}

/* Ended by AICoder, pid:u2756477c7777c714d5d093e104b7d3a2065c5e2 */