package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.zte.uedm.dcdigital.application.stat.executor.LoginStatsQueryService;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.service.DeptDomainService;
import com.zte.uedm.dcdigital.domain.service.DocDownStatsDomainService;
import com.zte.uedm.dcdigital.domain.service.UserService;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeptUserRelationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AuthDeptUserRelationPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeptQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DocDownStatsDomainServiceImpl implements DocDownStatsDomainService {
    @Autowired
    private DocDownStatDayRepository dayRepository;
    @Autowired
    private DocDownStatWeekRepository weekRepository;
    @Autowired
    private DocDownStatMonthRepository monthRepository;
    @Autowired
    private DocDownStatYearRepository yearRepository;
    @Autowired
    private DeptRepository deptRepository;
    @Autowired
    private DeptDomainService deptDomainService;
    @Autowired
    private DeptUserRelationMapper deptUserRelationMapper;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private UserService userService;
    @Autowired
    private LoginStatsQueryService loginStatsQueryService;
    public static final String EMPTY_STRING = "";
    private final Map<Integer, Function<DocDownStatQueryDto, DocDownStatisticsVo>> handlerMap = new HashMap<>();

    private final Map<Integer, Function<DocDownStatQueryDto, List<DocDownStatisticsTopVo>>> handlerTopMap = new HashMap<>();

    public DocDownStatsDomainServiceImpl() {
        handlerMap.put(SystemConstants.ONE, this::handleDay);
        handlerMap.put(SystemConstants.TWO, this::handleWeek);
        handlerMap.put(SystemConstants.THREE, this::handleMonth);
        handlerMap.put(SystemConstants.FOUR, this::handleYear);
        handlerTopMap.put(SystemConstants.ONE, this::handleTopDay);
        handlerTopMap.put(SystemConstants.TWO, this::handleTopWeek);
        handlerTopMap.put(SystemConstants.THREE, this::handleTopMonth);
        handlerTopMap.put(SystemConstants.FOUR, this::handleTopYear);
    }

    @Override
    /* Started by AICoder, pid:552f505a5ct2b21142380916e0c79969c6c381ee */
    public void addRecordDocDown(String userId, String resourceId, int operationType) {
        // 获取当前日期并转换为整数格式
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        //部门id
        AuthDeptEntity deptInfo= deptRepository.findDeptInfoByUserId(userId);

        DocDownStatDayEntity dayEntity = dayRepository.selectByUserIdAndResourceIdAndDay(userId, resourceId, currentDayNumber);
        DocDownStatDayEntity newDayEntity = new DocDownStatDayEntity();
        newDayEntity.setId(UUID.randomUUID().toString());
        newDayEntity.setDay(currentDayNumber);
        newDayEntity.setUserId(userId);
        if (deptInfo != null) {
            newDayEntity.setDeptId(deptInfo.getDeptId());
        } else {
            newDayEntity.setDeptId(EMPTY_STRING);
        }
        newDayEntity.setResourceId(resourceId);
        newDayEntity.setCreateTime(DateTimeUtils.getCurrentTime());
        //预览操作
        if(operationType == 5){
            //表中无数据
            if(dayEntity == null){
                newDayEntity.setDownloadNum(0);
                newDayEntity.setPreviewNum(1);
                dayRepository.addRecordStatDay(newDayEntity);
            }else {
                dayEntity.setPreviewNum(dayEntity.getPreviewNum() + 1);
                dayRepository.updateRecordStatDay(dayEntity);
            }
        }
        //下载操作
        if(operationType == 6){
            //表中无数据
            if(dayEntity == null){
                newDayEntity.setDownloadNum(1);
                newDayEntity.setPreviewNum(0);
                dayRepository.addRecordStatDay(newDayEntity);
            }else {
                dayEntity.setDownloadNum(dayEntity.getDownloadNum() + 1);
                dayRepository.updateRecordStatDay(dayEntity);
            }
        }
    }
    /* Ended by AICoder, pid:552f505a5ct2b21142380916e0c79969c6c381ee */

    @Override
    public DocDownStatisticsVo calculateSystemAccessDocDownStatistic(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo loginStatisticsVo = executeByTimeType(queryDto);
        return loginStatisticsVo;
    }

    @Override
    public DocDownStatisticsVo getPreviewTop(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        if(queryDto.getTimeType() == 1){
            docDownStatisticsVo  = getPreviewTopDay(queryDto);
        }else if(queryDto.getTimeType() == 2){
            docDownStatisticsVo = getPreviewTopWeek(queryDto);
        }else if(queryDto.getTimeType() == 3){
            docDownStatisticsVo = getPreviewTopMonth(queryDto);
        }else if(queryDto.getTimeType() == 4){
            docDownStatisticsVo = getPreviewTopYear(queryDto);
        }
        return docDownStatisticsVo;
    }
    /* Started by AICoder, pid:r5e28m62d1kd3ff1469409fdf0554c753e79a15a */
    public DocDownStatisticsVo getPreviewTopDay(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        //预览TOP50数据
        List<DocDownStatisticsVo.TopData> previewTopList = new ArrayList<>();
        DocDownStatDayEntity dayEntity = new DocDownStatDayEntity();
        dayEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        dayEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        dayEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatDayEntity> previewTop50Data = dayRepository.getPreviewTop50Data(dayEntity, deptUserIds);
        //通过resource_id获得文件名
        for (DocDownStatDayEntity entity : previewTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getPreviewNum());
            previewTopList.add(topData);
        }
        docDownStatisticsVo.setPreviewTopList(previewTopList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:r5e28m62d1kd3ff1469409fdf0554c753e79a15a */
    /* Started by AICoder, pid:2723ajda5bf92a514e070b8810627c68c5552256 */
    public DocDownStatisticsVo getPreviewTopWeek(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        //预览TOP50数据
        List<DocDownStatisticsVo.TopData> previewTopList = new ArrayList<>();
        DocDownStatWeekEntity weekEntity = new DocDownStatWeekEntity();
        weekEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        weekEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        weekEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatWeekEntity> previewTop50Data = weekRepository.getPreviewTop50Data(weekEntity, deptUserIds);
        //通过resource_id获得文件名
        for (DocDownStatWeekEntity entity : previewTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getPreviewNum());
            previewTopList.add(topData);
        }
        docDownStatisticsVo.setPreviewTopList(previewTopList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:2723ajda5bf92a514e070b8810627c68c5552256 */
    /* Started by AICoder, pid:uf08by1143f0e201421e089760c63e600750bd6e */
    public DocDownStatisticsVo getPreviewTopMonth(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        //预览TOP50数据
        List<DocDownStatisticsVo.TopData> previewTopList = new ArrayList<>();
        DocDownStatMonthEntity monthEntity = new DocDownStatMonthEntity();
        monthEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        monthEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        monthEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatMonthEntity> previewTop50Data = monthRepository.getPreviewTop50Data(monthEntity, deptUserIds);
        //通过resource_id获得文件名
        for (DocDownStatMonthEntity entity : previewTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getPreviewNum());
            previewTopList.add(topData);
        }
        docDownStatisticsVo.setPreviewTopList(previewTopList);
        return docDownStatisticsVo;
    }
    public DocDownStatisticsVo getPreviewTopYear(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        //预览TOP50数据
        List<DocDownStatisticsVo.TopData> previewTopList = new ArrayList<>();
        DocDownStatYearEntity yearEntity = new DocDownStatYearEntity();
        yearEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        yearEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        yearEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatYearEntity> previewTop50Data = yearRepository.getPreviewTop50Data(yearEntity, deptUserIds);
        //通过resource_id获得文件名
        for (DocDownStatYearEntity entity : previewTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getPreviewNum());
            previewTopList.add(topData);
        }
        docDownStatisticsVo.setPreviewTopList(previewTopList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:uf08by1143f0e201421e089760c63e600750bd6e */
    @Override
    public DocDownStatisticsVo getDownloadTop(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        if(queryDto.getTimeType() == 1){
            docDownStatisticsVo  = getDownloadTopDay(queryDto);
        }else if(queryDto.getTimeType() == 2){
            docDownStatisticsVo = getDownloadTopWeek(queryDto);
        }else if(queryDto.getTimeType() == 3){
            docDownStatisticsVo = getDownloadTopMonth(queryDto);
        }else if(queryDto.getTimeType() == 4){
            docDownStatisticsVo = getDownloadTopYear(queryDto);
        }
        return docDownStatisticsVo;
    }
    /* Started by AICoder, pid:ee90bt7ae0168c614d160897b0297a9df808579d */
    public DocDownStatisticsVo getDownloadTopDay(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        DocDownStatDayEntity dayEntity = new DocDownStatDayEntity();
        dayEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        dayEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        dayEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //下载TOP50数据
        List<DocDownStatisticsVo.TopData> downloadTopList = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatDayEntity> downloadTop50Data = dayRepository.getDownloadTop50Data(dayEntity, deptUserIds);
        for (DocDownStatDayEntity entity : downloadTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getDownloadNum());
            downloadTopList.add(topData);
        }
        docDownStatisticsVo.setDownloadTopList(downloadTopList);
        return docDownStatisticsVo;
    }
    public DocDownStatisticsVo getDownloadTopWeek(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        DocDownStatWeekEntity weekEntity = new DocDownStatWeekEntity();
        weekEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        weekEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        weekEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //下载TOP50数据
        List<DocDownStatisticsVo.TopData> downloadTopList = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatWeekEntity> downloadTop50Data = weekRepository.getDownloadTop50Data(weekEntity, deptUserIds);
        for (DocDownStatWeekEntity entity : downloadTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getDownloadNum());
            downloadTopList.add(topData);
        }
        docDownStatisticsVo.setDownloadTopList(downloadTopList);
        return docDownStatisticsVo;
    }
    public DocDownStatisticsVo getDownloadTopMonth(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        DocDownStatMonthEntity monthEntity = new DocDownStatMonthEntity();
        monthEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        monthEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        monthEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //下载TOP50数据
        List<DocDownStatisticsVo.TopData> downloadTopList = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatMonthEntity> downloadTop50Data = monthRepository.getDownloadTop50Data(monthEntity, deptUserIds);
        for (DocDownStatMonthEntity entity : downloadTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getDownloadNum());
            downloadTopList.add(topData);
        }
        docDownStatisticsVo.setDownloadTopList(downloadTopList);
        return docDownStatisticsVo;
    }
    public DocDownStatisticsVo getDownloadTopYear(DocDownStatQueryDto queryDto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        DocDownStatYearEntity yearEntity = new DocDownStatYearEntity();
        yearEntity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        yearEntity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        yearEntity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        //下载TOP50数据
        List<DocDownStatisticsVo.TopData> downloadTopList = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        List<DocDownStatYearEntity> downloadTop50Data = yearRepository.getDownloadTop50Data(yearEntity, deptUserIds);
        for (DocDownStatYearEntity entity : downloadTop50Data) {
            DocDownStatisticsVo.TopData topData = new DocDownStatisticsVo.TopData();
            FileInfoVo fileInfo = documentService.getFileInfo(entity.getResourceId());
            if(fileInfo != null && fileInfo.getFileName() != null){
                topData.setFileName(fileInfo.getFileName());
            }else {
                topData.setFileName("--");
            }
            topData.setNum(entity.getDownloadNum());
            downloadTopList.add(topData);
        }
        docDownStatisticsVo.setDownloadTopList(downloadTopList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:ee90bt7ae0168c614d160897b0297a9df808579d */
    @Override
    public List<DocDownStatisticsTopVo> getStatisticFileTopData(DocDownStatQueryDto queryDto) {
        Function<DocDownStatQueryDto, List<DocDownStatisticsTopVo>> handler = handlerTopMap.get(queryDto.getTimeType());
        if (handler != null) {
            // 使用apply()获取返回值
            return handler.apply(queryDto);
        } else {
            // 默认处理日统计
            return handleTopDay(queryDto);
        }
    }

    @Override
    /* Started by AICoder, pid:88792dba77e0a421425c095e40db939e0cf51ffb */
    public List<DocDownUserStatisticsVo> getUserStatisticFileDate(DocDownStatQueryDto queryDto) {
        List<DocDownUserStatisticsVo> resultList = new ArrayList<>();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(queryDto.getStartTime()), ParseUtils.parseStringToInt(queryDto.getEndTime()), queryDto);
        //根据日、周、月、年不同获得数据
        List<DocDownStatEntity> entityList = getDocDownStatEntity(queryDto);
        Map<String, List<DocDownStatEntity>> groupedByUserId = entityList.stream()
                .collect(Collectors.groupingBy(DocDownStatEntity::getUserId));
        for (Map.Entry<String, List<DocDownStatEntity>> entry : groupedByUserId.entrySet()) {
            DocDownUserStatisticsVo docDownUserStatisticsVo = new DocDownUserStatisticsVo();
            String userId = entry.getKey();
            UserVo userVo = userService.queryUserById(userId);
            String userName = (userVo != null && userVo.getName() != null) ? userVo.getName() : userId;
            List<DocDownStatEntity> entities = entry.getValue();
            //Collections.sort(entities, Comparator.comparingInt(DocDownStatEntity::getDay));
            long totalViewNum = 0l;
            long totalDownNum = 0l;
            List<Map<String, String>> periodDocDownTimes = new ArrayList<>();
            Map<String, String> periodTimes = new HashMap<>();
            List<Integer> integers = entities.stream().map(DocDownStatEntity::getDay).collect(Collectors.toList());
            // 将allIntegerDay转换为HashSet
            Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
            for (Integer num : integers) {
                if (allIntegerDaySet.contains(num)) {
                    allIntegerDaySet.remove(num);
                }
            }
            // 遍历integers，筛选不在HashSet中的数据
            List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
            setPeriodDocDownTimes(missingIntegers,periodDocDownTimes,queryDto);
            for (DocDownStatEntity item : entities) {

                Map<String, String> stringMap = new HashMap<>();
                totalViewNum += item.getPreviewNum();
                totalDownNum += item.getDownloadNum();
                String num = item.getPreviewNum() + "|" + item.getDownloadNum();
                setStringMap(queryDto, stringMap, num, item);
                periodDocDownTimes.add(stringMap);
            }
            periodDocDownTimes = periodDocDownTimes.stream()
                    .sorted(Comparator.comparing(map -> map.keySet().iterator().next()))
                    .collect(Collectors.toList());
            for (Map<String, String> periodDocDownTime : periodDocDownTimes) {
                periodTimes.putAll(periodDocDownTime);
            }
            docDownUserStatisticsVo.setPeriodTimes(periodTimes);
            docDownUserStatisticsVo.setUserName(userName + "预览|下载文档");
            docDownUserStatisticsVo.setTotalNum(totalViewNum + "|" + totalDownNum);
            docDownUserStatisticsVo.setPeriodDocDownTimes(periodDocDownTimes);
            resultList.add(docDownUserStatisticsVo);
        }
        return resultList;
    }
    /* Ended by AICoder, pid:88792dba77e0a421425c095e40db939e0cf51ffb */
    public void setPeriodDocDownTimes(List<Integer> missingIntegers, List<Map<String, String>> periodDocDownTimes, DocDownStatQueryDto queryDto){
        for (Integer missingInteger : missingIntegers) {
            Map<String, String> stringMap = new HashMap<>();
            if(queryDto.getTimeType() == 1){
                stringMap.put(getDayInfo(missingInteger),"0|0");
            }else if(queryDto.getTimeType() == 2){
                stringMap.put(getWeekInfo(missingInteger),"0|0");
            }else if(queryDto.getTimeType() == 3){
                stringMap.put(getMonthInfo(missingInteger),"0|0");
            }else if(queryDto.getTimeType() == 4){
                stringMap.put(missingInteger + "年","0|0");
            }
            periodDocDownTimes.add(stringMap);
        }
    }
    public List<Integer> getAllIntegerDay(int beginTime, int endTime, DocDownStatQueryDto queryDto){
        List<Integer> result = new ArrayList<>();
        if(queryDto.getTimeType() == 1){
            getDayInfo(beginTime, endTime, result);
        }else if(queryDto.getTimeType() == 3){
            getMonthInfo(beginTime, endTime, result);
        }else if(queryDto.getTimeType() == 2){
            getWeekInfo(beginTime, endTime, result);
        }else {
            for(int i = beginTime;i <= endTime;i++){
                result.add(i);
            }
        }
        return result;
    }
    /* Started by AICoder, pid:qd12eec3cbwca23140510a9b20fa612322c31ab1 */
    public void getDayInfo(int beginTime, int endTime, List<Integer> result){
        int year = beginTime / 10000;
        int month = (beginTime % 10000) / 100;
        int day = beginTime % 100;
        LocalDate start = LocalDate.of(year, month, day);
        int year1 = endTime / 10000;
        int month1 = (endTime % 10000) / 100;
        int day1 = endTime % 100;
        LocalDate end = LocalDate.of(year1, month1, day1);
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            result.add(date.getYear() * 10000 + date.getMonthValue() * 100 + date.getDayOfMonth());
        }
    }
    /* Ended by AICoder, pid:qd12eec3cbwca23140510a9b20fa612322c31ab1 */
    /* Started by AICoder, pid:9a203d0c679b1d0146f70b6b2097da54d464cfac */
    public void getWeekInfo(int beginTime, int endTime, List<Integer> result){
        int year = beginTime / 100;
        int weekNumber = beginTime % 100;
        LocalDate start = LocalDate.of(year, 1, 1)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, weekNumber)
                .with(ChronoField.DAY_OF_WEEK, 1);
        int year1 = endTime / 100;
        int weekNumber1 = endTime % 100;
        LocalDate end = LocalDate.of(year1, 1, 1)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, weekNumber1)
                .with(ChronoField.DAY_OF_WEEK, 1);
        for (LocalDate weekStart = start; !weekStart.isAfter(end); weekStart = weekStart.plusWeeks(1)) {
            result.add(weekStart.get(IsoFields.WEEK_BASED_YEAR ) * 100 + weekStart.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
        }
    }
    /* Ended by AICoder, pid:9a203d0c679b1d0146f70b6b2097da54d464cfac */
    /* Started by AICoder, pid:q3a01o6033dda0e141cb0a9390d8cf3290f4c3bd */
    public void getMonthInfo(int beginTime, int endTime, List<Integer> result){
        int year1 = beginTime / 100;
        int month1 = beginTime % 100;
        YearMonth start = YearMonth.of(year1, month1);
        int year2 = endTime / 100;
        int month2 = endTime % 100;
        YearMonth end = YearMonth.of(year2, month2);
        for (YearMonth month = start; !month.isAfter(end); month = month.plusMonths(1)) {
            result.add(month.getYear() * 100 + month.getMonthValue());
        }
    }
    /* Ended by AICoder, pid:q3a01o6033dda0e141cb0a9390d8cf3290f4c3bd */
    /* Started by AICoder, pid:o24aee20052fd1a1488808324010b269d7670fbb */
    public void setStringMap(DocDownStatQueryDto queryDto,Map<String, String> stringMap,String num,DocDownStatEntity item){
        if(queryDto.getTimeType() == 1){
            stringMap.put(getDayInfo(item.getDay()),num);
        }else if(queryDto.getTimeType() == 2){
            stringMap.put(getWeekInfo(item.getDay()),num);
        }else if(queryDto.getTimeType() == 3){
            stringMap.put(getMonthInfo(item.getDay()),num);
        }else if(queryDto.getTimeType() == 4){
            stringMap.put(item.getDay() + "年",num);
        }
    }
    public List<DocDownStatEntity> getDocDownStatEntity(DocDownStatQueryDto queryDto){
        DocDownStatEntity entity = new DocDownStatEntity();
        entity.setDeptId(queryDto.getDeptId());
        //字符串转数字
        entity.setBeginTime(ParseUtils.parseStringToInt(queryDto.getStartTime()));
        entity.setEndTime(ParseUtils.parseStringToInt(queryDto.getEndTime()));
        List<DocDownStatEntity> entityList = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(queryDto.getDeptId());
        if(queryDto.getTimeType() == 1){
            entityList = dayRepository.getUserStatisticFileDate(entity, deptUserIds);
        }else if(queryDto.getTimeType() == 2){
            entityList = weekRepository.getUserStatisticFileDate(entity, deptUserIds);
        }else if(queryDto.getTimeType() == 3){
            entityList = monthRepository.getUserStatisticFileDate(entity, deptUserIds);
        }else if(queryDto.getTimeType() == 4){
            entityList = yearRepository.getUserStatisticFileDate(entity, deptUserIds);
        }
        return entityList;
    }
    /* Ended by AICoder, pid:o24aee20052fd1a1488808324010b269d7670fbb */
    public DocDownStatisticsVo executeByTimeType(DocDownStatQueryDto dto) {
        Function<DocDownStatQueryDto, DocDownStatisticsVo> handler = handlerMap.get(dto.getTimeType());
        if (handler != null) {
            // 使用apply()获取返回值
            return handler.apply(dto);
        } else {
            // 默认处理日统计
            return handleDay(dto);
        }
    }
    /* Started by AICoder, pid:x23dcw48cfz6d7514f450a4c60650c839e056352 */
    private DocDownStatisticsVo handleDay(DocDownStatQueryDto dto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        //整合基础数据
        DocDownStatDayEntity dayEntity = new DocDownStatDayEntity();
        dayEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        dayEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        dayEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        //获得文档总数
        Long fileNum = documentService.getFileNum();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //获取当前时间段，当前部门活跃人数
        List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                ParseUtils.parseStringToInt(dto.getStartTime()),
                ParseUtils.parseStringToInt(dto.getEndTime()),
                dto.getTimeType(),
                dto.getDeptId());

        List<DocDownStatDayEntity> dayEntities = dayRepository.getDownAndViewNum(dayEntity, deptUserIds);
        List<Integer> integers = dayEntities.stream().map(DocDownStatDayEntity::getDay).collect(Collectors.toList());
        long totalDownload = dayEntities.stream().mapToInt(DocDownStatDayEntity::getDownloadNum).sum();
        long totalPreview = dayEntities.stream().mapToInt(DocDownStatDayEntity::getPreviewNum).sum();
        docDownStatisticsVo.setTotalDownload(totalDownload);
        docDownStatisticsVo.setTotalPreview(totalPreview);

        long systemAllUser = activeUserIds!=null? activeUserIds.size() : 0;
        if (systemAllUser > 0) {
            docDownStatisticsVo.setAvgDownload(Math.round((double)totalDownload / systemAllUser));
            docDownStatisticsVo.setAvgPreview(Math.round((double)totalPreview / systemAllUser));
        } else {
            docDownStatisticsVo.setAvgDownload(0);
            docDownStatisticsVo.setAvgPreview(0);
        }

        //全系统数据统计
        List<DocDownStatisticsVo.TotalSystem> systemList = new ArrayList<>();
        // 将allIntegerDay转换为HashSet
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(0);
            item.setTotalPreview(0);
            item.setAvgDownload(0);
            item.setAvgPreview(0);
            item.setPreviewAvg(0);
            item.setDownloadAvg(0);
            item.setTimePeriod(getDayInfo(missingInteger));
            systemList.add(item);
        }
        for(DocDownStatDayEntity entity :dayEntities){
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(entity.getDownloadNum());
            item.setTotalPreview(entity.getPreviewNum());
            if (systemAllUser > 0) {
                item.setAvgDownload(Math.round((double)entity.getDownloadNum() / systemAllUser));
                item.setAvgPreview(Math.round((double)entity.getPreviewNum() / systemAllUser));
            } else {
                item.setAvgDownload(0);
                item.setAvgPreview(0);
            }
            item.setTimePeriod(getDayInfo(entity.getDay()));
            if(fileNum > 0){
                item.setDownloadAvg(Math.round((double)entity.getDownloadNum() / fileNum));
                item.setPreviewAvg(Math.round((double)entity.getPreviewNum() / fileNum));
            }else {
                item.setDownloadAvg(0);
                item.setPreviewAvg(0);
            }
            systemList.add(item);
        }
        systemList = systemList.stream().sorted(Comparator.comparing(
                        DocDownStatisticsVo.TotalSystem::getTimePeriod,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )).collect(Collectors.toList());
        docDownStatisticsVo.setTotalSystem(systemList);

        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:x23dcw48cfz6d7514f450a4c60650c839e056352 */
    /* Started by AICoder, pid:x0bc0lc239z2b5814acb0bf05071072d8fa645bb */
    public String getDayInfo(int dateInt){
        // 将整数转换为字符串
        String dateStr = String.valueOf(dateInt);
        // 验证输入格式
        if (dateStr.length() != 8) {
            throw new IllegalArgumentException("日期格式必须为8位数字（yyyyMMdd），输入值：" + dateStr);
        }
        try {
            // 解析各部分日期
            String year = dateStr.substring(0, 4);
            String month = dateStr.substring(4, 6);
            String day = dateStr.substring(6, 8);
            // 返回格式化结果
            return year + "年" + month + "月" + day + "日";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("日期必须全部由数字组成，输入值：" + dateStr);
        }
    }
    /* Ended by AICoder, pid:x0bc0lc239z2b5814acb0bf05071072d8fa645bb */
    /* Started by AICoder, pid:88ad3u12ff3167314aeb0bede11dc71b25379505 */
    private DocDownStatisticsVo handleWeek(DocDownStatQueryDto dto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()),dto);
        //整合基础数据
        DocDownStatWeekEntity weekEntity = new DocDownStatWeekEntity();
        weekEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        weekEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        weekEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        //获得文档总数
        Long fileNum = documentService.getFileNum();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //获取当前时间段，当前部门活跃人数
        List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                ParseUtils.parseStringToInt(dto.getStartTime()),
                ParseUtils.parseStringToInt(dto.getEndTime()),
                dto.getTimeType(),
                dto.getDeptId());
        List<DocDownStatWeekEntity> weekEntities = weekRepository.getDownAndViewNum(weekEntity, deptUserIds);
        List<Integer> integers = weekEntities.stream().map(DocDownStatWeekEntity::getDay).collect(Collectors.toList());
        long totalDownload = weekEntities.stream().mapToInt(DocDownStatWeekEntity::getDownloadNum).sum();
        long totalPreview = weekEntities.stream().mapToInt(DocDownStatWeekEntity::getPreviewNum).sum();
        docDownStatisticsVo.setTotalDownload(totalDownload);
        docDownStatisticsVo.setTotalPreview(totalPreview);

        long systemAllUser = activeUserIds!=null? activeUserIds.size() : 0;
        if (systemAllUser > 0) {
            docDownStatisticsVo.setAvgDownload(Math.round((double)totalDownload / systemAllUser));
            docDownStatisticsVo.setAvgPreview(Math.round((double)totalPreview / systemAllUser));
        } else {
            docDownStatisticsVo.setAvgDownload(0);
            docDownStatisticsVo.setAvgPreview(0);
        }
        //全系统数据统计
        List<DocDownStatisticsVo.TotalSystem> systemList = new ArrayList<>();
        // 将allIntegerDay转换为HashSet
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(0);
            item.setTotalPreview(0);
            item.setAvgDownload(0);
            item.setAvgPreview(0);
            item.setPreviewAvg(0);
            item.setDownloadAvg(0);
            item.setTimePeriod(getWeekInfo(missingInteger));
            systemList.add(item);
        }
        for(DocDownStatWeekEntity entity :weekEntities){
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(entity.getDownloadNum());
            item.setTotalPreview(entity.getPreviewNum());
            if (systemAllUser > 0) {
                item.setAvgDownload(Math.round((double)entity.getDownloadNum() / systemAllUser));
                item.setAvgPreview(Math.round((double)entity.getPreviewNum() / systemAllUser));
            } else {
                item.setAvgDownload(0);
                item.setAvgPreview(0);
            }
            item.setTimePeriod(getWeekInfo(entity.getDay()));
            if(fileNum > 0){
                item.setDownloadAvg(Math.round((double)entity.getDownloadNum() / fileNum));
                item.setPreviewAvg(Math.round((double)entity.getPreviewNum() / fileNum));
            }else {
                item.setDownloadAvg(0);
                item.setPreviewAvg(0);
            }
            systemList.add(item);
        }
        systemList = systemList.stream().sorted(Comparator.comparing(
                DocDownStatisticsVo.TotalSystem::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docDownStatisticsVo.setTotalSystem(systemList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:88ad3u12ff3167314aeb0bede11dc71b25379505 */
    /* Started by AICoder, pid:g334bn1458ma227148a00a43b041a533e706f93b */
    public String getWeekInfo(int yearWeek){
        // 将整数转换为字符串
        String yearWeekStr = String.valueOf(yearWeek);
        // 验证基本长度
        if (yearWeekStr.length() != 6) {
            throw new IllegalArgumentException("输入必须是6位数字（格式：yyyyww），输入值：" + yearWeek);
        }
        try {
            // 分割年份和周数
            String yearPart = yearWeekStr.substring(0, 4);
            String weekPart = yearWeekStr.substring(4);
            return yearPart + "年" + weekPart + "周";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入必须全部由数字组成，输入值：" + yearWeek, e);
        }
    }
    /* Ended by AICoder, pid:g334bn1458ma227148a00a43b041a533e706f93b */
    /* Started by AICoder, pid:rf5a0s36fedc8dc14a990baf40dfe39d91e1fb09 */
    private DocDownStatisticsVo handleMonth(DocDownStatQueryDto dto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        //整合基础数据
        DocDownStatMonthEntity monthEntity = new DocDownStatMonthEntity();
        monthEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        monthEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        monthEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //获取当前时间段，当前部门活跃人数
        List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                ParseUtils.parseStringToInt(dto.getStartTime()),
                ParseUtils.parseStringToInt(dto.getEndTime()),
                dto.getTimeType(),
                dto.getDeptId());
        //获得文档总数
        Long fileNum = documentService.getFileNum();
        List<DocDownStatMonthEntity> monthEntities = monthRepository.getDownAndViewNum(monthEntity, deptUserIds);
        List<Integer> integers = monthEntities.stream().map(DocDownStatMonthEntity::getDay).collect(Collectors.toList());
        long totalDownload = monthEntities.stream().mapToInt(DocDownStatMonthEntity::getDownloadNum).sum();
        long totalPreview = monthEntities.stream().mapToInt(DocDownStatMonthEntity::getPreviewNum).sum();
        docDownStatisticsVo.setTotalDownload(totalDownload);
        docDownStatisticsVo.setTotalPreview(totalPreview);

        long systemAllUser = activeUserIds!=null? activeUserIds.size() : 0;
        if (systemAllUser > 0) {
            docDownStatisticsVo.setAvgDownload(Math.round((double)totalDownload / systemAllUser));
            docDownStatisticsVo.setAvgPreview(Math.round((double)totalPreview / systemAllUser));
        } else {
            docDownStatisticsVo.setAvgDownload(0);
            docDownStatisticsVo.setAvgPreview(0);
        }
        //全系统数据统计
        List<DocDownStatisticsVo.TotalSystem> systemList = new ArrayList<>();
        // 将allIntegerDay转换为HashSet
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(0);
            item.setTotalPreview(0);
            item.setAvgDownload(0);
            item.setAvgPreview(0);
            item.setPreviewAvg(0);
            item.setDownloadAvg(0);
            item.setTimePeriod(getMonthInfo(missingInteger));
            systemList.add(item);
        }
        for(DocDownStatMonthEntity entity :monthEntities){
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(entity.getDownloadNum());
            item.setTotalPreview(entity.getPreviewNum());
            if (systemAllUser > 0) {
                item.setAvgDownload(Math.round((double)entity.getDownloadNum() / systemAllUser));
                item.setAvgPreview(Math.round((double)entity.getPreviewNum() / systemAllUser));
            } else {
                item.setAvgDownload(0);
                item.setAvgPreview(0);
            }
            item.setTimePeriod(getMonthInfo(entity.getDay()));
            if(fileNum > 0){
                item.setDownloadAvg(Math.round((double)entity.getDownloadNum() / fileNum));
                item.setPreviewAvg(Math.round((double)entity.getPreviewNum() / fileNum));
            }else {
                item.setDownloadAvg(0);
                item.setPreviewAvg(0);
            }
            systemList.add(item);
        }
        systemList = systemList.stream().sorted(Comparator.comparing(
                DocDownStatisticsVo.TotalSystem::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docDownStatisticsVo.setTotalSystem(systemList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:rf5a0s36fedc8dc14a990baf40dfe39d91e1fb09 */
    /* Started by AICoder, pid:r77fale1e3325cc14ae90a94609c58376bf68fe7 */
    public String getMonthInfo(int yearMonth){
        // 将整数转换为字符串
        String yearWeekStr = String.valueOf(yearMonth);
        // 验证基本长度
        if (yearWeekStr.length() != 6) {
            throw new IllegalArgumentException("输入必须是6位数字（格式：yyyyww），输入值：" + yearMonth);
        }
        try {
            // 分割年份和周数
            String yearPart = yearWeekStr.substring(0, 4);
            String monthPart = yearWeekStr.substring(4);
            return yearPart + "年" + monthPart + "月";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入必须全部由数字组成，输入值：" + yearMonth, e);
        }
    }
    /* Ended by AICoder, pid:r77fale1e3325cc14ae90a94609c58376bf68fe7 */
    /* Started by AICoder, pid:4b632xbc5e79f1314afe087ae00c618826a94a79 */
    private DocDownStatisticsVo handleYear(DocDownStatQueryDto dto) {
        DocDownStatisticsVo docDownStatisticsVo = new DocDownStatisticsVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        //整合基础数据
        DocDownStatYearEntity yearEntity = new DocDownStatYearEntity();
        yearEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        yearEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        yearEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        //获得文档总数
        Long fileNum = documentService.getFileNum();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        //获取当前时间段，当前部门活跃人数
        List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                ParseUtils.parseStringToInt(dto.getStartTime()),
                ParseUtils.parseStringToInt(dto.getEndTime()),
                dto.getTimeType(),
                dto.getDeptId());
        List<DocDownStatYearEntity> yearEntities = yearRepository.getDownAndViewNum(yearEntity, deptUserIds);
        List<Integer> integers = yearEntities.stream().map(DocDownStatYearEntity::getDay).collect(Collectors.toList());
        long totalDownload = yearEntities.stream().mapToInt(DocDownStatYearEntity::getDownloadNum).sum();
        long totalPreview = yearEntities.stream().mapToInt(DocDownStatYearEntity::getPreviewNum).sum();
        docDownStatisticsVo.setTotalDownload(totalDownload);
        docDownStatisticsVo.setTotalPreview(totalPreview);

        long systemAllUser = activeUserIds!=null? activeUserIds.size() : 0;
        if (systemAllUser > 0) {
            docDownStatisticsVo.setAvgDownload(Math.round((double)totalDownload / systemAllUser));
            docDownStatisticsVo.setAvgPreview(Math.round((double)totalPreview / systemAllUser));
        } else {
            docDownStatisticsVo.setAvgDownload(0);
            docDownStatisticsVo.setAvgPreview(0);
        }
        //全系统数据统计
        List<DocDownStatisticsVo.TotalSystem> systemList = new ArrayList<>();
        // 将allIntegerDay转换为HashSet
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(0);
            item.setTotalPreview(0);
            item.setAvgDownload(0);
            item.setAvgPreview(0);
            item.setPreviewAvg(0);
            item.setDownloadAvg(0);
            item.setTimePeriod(missingInteger + "年");
            systemList.add(item);
        }
        for(DocDownStatYearEntity entity :yearEntities){
            DocDownStatisticsVo.TotalSystem item = new DocDownStatisticsVo.TotalSystem();
            item.setTotalDownload(entity.getDownloadNum());
            item.setTotalPreview(entity.getPreviewNum());
            if (systemAllUser > 0) {
                item.setAvgDownload(Math.round((double)entity.getDownloadNum() / systemAllUser));
                item.setAvgPreview(Math.round((double)entity.getPreviewNum() / systemAllUser));
            } else {
                item.setAvgDownload(0);
                item.setAvgPreview(0);
            }
            item.setTimePeriod(entity.getDay() +"年");
            if(fileNum > 0){
                item.setDownloadAvg(Math.round((double)entity.getDownloadNum() / fileNum));
                item.setPreviewAvg(Math.round((double)entity.getPreviewNum() / fileNum));
            }else {
                item.setDownloadAvg(0);
                item.setPreviewAvg(0);
            }
            systemList.add(item);
        }
        systemList = systemList.stream().sorted(Comparator.comparing(
                DocDownStatisticsVo.TotalSystem::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docDownStatisticsVo.setTotalSystem(systemList);
        return docDownStatisticsVo;
    }
    /* Ended by AICoder, pid:4b632xbc5e79f1314afe087ae00c618826a94a79 */
    /* Started by AICoder, pid:514b5i936965ecf14a7d0892e0725d74b11243a1 */
    private List<DocDownStatisticsTopVo> handleTopDay(DocDownStatQueryDto dto){
        List<DocDownStatisticsTopVo> docDownStatisticsTopVos = new ArrayList<>();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        DocDownStatDayEntity dayEntity = new DocDownStatDayEntity();
        dayEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        dayEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        dayEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        List<DocDownStatDayEntity> top50Data = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        if(dto.getType() == 1){
            top50Data = dayRepository.getPreviewDetailsData(dayEntity, deptUserIds);
            Map<String, List<DocDownStatDayEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatDayEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatDayEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatDayEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatDayEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatDayEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatDayEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getDayInfo(missingInteger), 0L);
                }
                totalNum = setDayViewTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }else {
            top50Data = dayRepository.getDownloadDetailsData(dayEntity, deptUserIds);
            Map<String, List<DocDownStatDayEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatDayEntity::getResourceId));

            for (Map.Entry<String, List<DocDownStatDayEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatDayEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatDayEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatDayEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatDayEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getDayInfo(missingInteger), 0L);
                }
                totalNum = setDayDownTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }
        List<DocDownStatisticsTopVo> resultList = docDownStatisticsTopVos.stream()
                .sorted(Comparator.comparingLong(DocDownStatisticsTopVo::getTotalNum).reversed())
                .collect(Collectors.toList());
        return resultList;
    }
    /* Ended by AICoder, pid:514b5i936965ecf14a7d0892e0725d74b11243a1 */
    public long setDayViewTopMap(Map<Integer, List<DocDownStatDayEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatDayEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatDayEntity entity : entry1.getValue()) {
                totalNum += entity.getPreviewNum();
                totalNum1 += entity.getPreviewNum();
            }
            topMap.put(getDayInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    public long setDayDownTopMap(Map<Integer, List<DocDownStatDayEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatDayEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatDayEntity entity : entry1.getValue()) {
                totalNum += entity.getDownloadNum();
                totalNum1 += entity.getDownloadNum();
            }
            topMap.put(getDayInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    public void getMissingIntegers(List<Integer> integers, Set<Integer> allIntegerDaySet){
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
    }
    /* Started by AICoder, pid:s0740n81b0ia8261463e083170b8a96b64d5f1aa */
    private List<DocDownStatisticsTopVo> handleTopWeek(DocDownStatQueryDto dto){
        List<DocDownStatisticsTopVo> docDownStatisticsTopVos = new ArrayList<>();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        DocDownStatWeekEntity weekEntity = new DocDownStatWeekEntity();
        weekEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        weekEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        weekEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        List<DocDownStatWeekEntity> top50Data = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        if(dto.getType() == 1){
            top50Data = weekRepository.getPreviewDetailsData(weekEntity, deptUserIds);
            Map<String, List<DocDownStatWeekEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatWeekEntity::getResourceId));

            for (Map.Entry<String, List<DocDownStatWeekEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatWeekEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatWeekEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatWeekEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatWeekEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getWeekInfo(missingInteger), 0L);
                }
                totalNum = setWeekViewTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }else {
            top50Data = weekRepository.getDownloadDetailsData(weekEntity, deptUserIds);
            Map<String, List<DocDownStatWeekEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatWeekEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatWeekEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatWeekEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatWeekEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatWeekEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatWeekEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getWeekInfo(missingInteger), 0L);
                }
                totalNum = setWeekDownTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }
        List<DocDownStatisticsTopVo> resultList = docDownStatisticsTopVos.stream()
                .sorted(Comparator.comparingLong(DocDownStatisticsTopVo::getTotalNum).reversed())
                .collect(Collectors.toList());
        return resultList;
    }
    /* Ended by AICoder, pid:s0740n81b0ia8261463e083170b8a96b64d5f1aa */
    public long setWeekViewTopMap(Map<Integer, List<DocDownStatWeekEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatWeekEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatWeekEntity entity : entry1.getValue()) {
                totalNum += entity.getPreviewNum();
                totalNum1 += entity.getPreviewNum();
            }
            topMap.put(getWeekInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    public long setWeekDownTopMap(Map<Integer, List<DocDownStatWeekEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatWeekEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatWeekEntity entity : entry1.getValue()) {
                totalNum += entity.getDownloadNum();
                totalNum1 += entity.getDownloadNum();
            }
            topMap.put(getWeekInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    /* Started by AICoder, pid:ia89fd4ff5b359e14ba9099ce0f3697025e45bdc */
    private List<DocDownStatisticsTopVo> handleTopMonth(DocDownStatQueryDto dto){
        List<DocDownStatisticsTopVo> docDownStatisticsTopVos = new ArrayList<>();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        DocDownStatMonthEntity monthEntity = new DocDownStatMonthEntity();
        monthEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        monthEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        monthEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        List<DocDownStatMonthEntity> top50Data = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        if(dto.getType() == 1){
            top50Data = monthRepository.getPreviewDetailsData(monthEntity, deptUserIds);
            Map<String, List<DocDownStatMonthEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatMonthEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatMonthEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatMonthEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatMonthEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatMonthEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatMonthEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getMonthInfo(missingInteger),0L);
                }
                totalNum = setMonthViewTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }else {
            top50Data = monthRepository.getDownloadDetailsData(monthEntity, deptUserIds);
            Map<String, List<DocDownStatMonthEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatMonthEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatMonthEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatMonthEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatMonthEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatMonthEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatMonthEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(getMonthInfo(missingInteger),0L);
                }
                totalNum = setMonthDownTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }
        List<DocDownStatisticsTopVo> resultList = docDownStatisticsTopVos.stream()
                .sorted(Comparator.comparingLong(DocDownStatisticsTopVo::getTotalNum).reversed())
                .collect(Collectors.toList());
        return resultList;
    }
    /* Ended by AICoder, pid:ia89fd4ff5b359e14ba9099ce0f3697025e45bdc */
    public long setMonthViewTopMap(Map<Integer, List<DocDownStatMonthEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatMonthEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatMonthEntity entity : entry1.getValue()) {
                totalNum += entity.getPreviewNum();
                totalNum1 += entity.getPreviewNum();
            }
            topMap.put(getMonthInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    public long setMonthDownTopMap(Map<Integer, List<DocDownStatMonthEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatMonthEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0L;
            for (DocDownStatMonthEntity entity : entry1.getValue()) {
                totalNum += entity.getDownloadNum();
                totalNum1 += entity.getDownloadNum();
            }
            topMap.put(getMonthInfo(entry1.getKey()),totalNum1);
        }
        return totalNum;
    }
    /* Started by AICoder, pid:38b28k08928ef871422d0969804a9f81cc46c9aa */
    private List<DocDownStatisticsTopVo> handleTopYear(DocDownStatQueryDto dto){
        List<DocDownStatisticsTopVo> docDownStatisticsTopVos = new ArrayList<>();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(dto.getStartTime()), ParseUtils.parseStringToInt(dto.getEndTime()), dto);
        DocDownStatYearEntity yearEntity = new DocDownStatYearEntity();
        yearEntity.setDeptId(dto.getDeptId());
        //字符串转数字
        yearEntity.setBeginTime(ParseUtils.parseStringToInt(dto.getStartTime()));
        yearEntity.setEndTime(ParseUtils.parseStringToInt(dto.getEndTime()));
        List<DocDownStatYearEntity> top50Data = new ArrayList<>();
        //获取部门成员总数
        List<String> deptUserIds = getDeptUserIds(dto.getDeptId());
        if(dto.getType() == 1){
            top50Data = yearRepository.getPreviewDetailsData(yearEntity, deptUserIds);
            Map<String, List<DocDownStatYearEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatYearEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatYearEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatYearEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatYearEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatYearEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatYearEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(missingInteger + "年", 0L);
                }
                totalNum = setYearViewTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }else {
            top50Data = yearRepository.getDownloadDetailsData(yearEntity, deptUserIds);
            Map<String, List<DocDownStatYearEntity>> groupedByResourceId = top50Data.stream()
                    .collect(Collectors.groupingBy(DocDownStatYearEntity::getResourceId));
            for (Map.Entry<String, List<DocDownStatYearEntity>> entry : groupedByResourceId.entrySet()) {
                DocDownStatisticsTopVo docDownStatisticsTopVo = new DocDownStatisticsTopVo();
                String resourceId = entry.getKey();
                FileInfoVo fileInfo = documentService.getFileInfo(resourceId);
                //根据resourceId获得文件名称
                List<DocDownStatYearEntity> entities = entry.getValue();
                List<Integer> integers = entities.stream().map(DocDownStatYearEntity::getDay).collect(Collectors.toList());
                // 将allIntegerDay转换为HashSet
                Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
                getMissingIntegers(integers, allIntegerDaySet);
                // 遍历integers，筛选不在HashSet中的数据
                List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
                Map<Integer, List<DocDownStatYearEntity>> collect = entities.stream()
                        .collect(Collectors.groupingBy(DocDownStatYearEntity::getDay));
                long totalNum = 0L;
                Map<String, Long> topMap = new HashMap<>();
                for (Integer missingInteger : missingIntegers) {
                    topMap.put(missingInteger + "年", 0L);
                }
                totalNum = setYearDownTopMap(collect, totalNum, topMap);
                topMap = topMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey()) // 按 key 自然排序（升序）
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new // 使用 LinkedHashMap 保持顺序
                        ));
                docDownStatisticsTopVo.setTopMap(topMap);
                setFileName(docDownStatisticsTopVo,fileInfo);
                docDownStatisticsTopVo.setTotalNum(totalNum);
                docDownStatisticsTopVos.add(docDownStatisticsTopVo);
            }
        }
        List<DocDownStatisticsTopVo> resultList = docDownStatisticsTopVos.stream()
                .sorted(Comparator.comparingLong(DocDownStatisticsTopVo::getTotalNum).reversed())
                .collect(Collectors.toList());
        return resultList;
    }
    /* Ended by AICoder, pid:38b28k08928ef871422d0969804a9f81cc46c9aa */
    public void setFileName(DocDownStatisticsTopVo docDownStatisticsTopVo, FileInfoVo fileInfo){
        if(fileInfo != null && fileInfo.getFileName() != null){
            docDownStatisticsTopVo.setFileName(fileInfo.getFileName());
        }else {
            docDownStatisticsTopVo.setFileName("--");
        }
    }
    public long setYearViewTopMap(Map<Integer, List<DocDownStatYearEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatYearEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0l;
            for (DocDownStatYearEntity entity : entry1.getValue()) {
                totalNum += entity.getPreviewNum();
                totalNum1 += entity.getPreviewNum();
            }
            topMap.put(entry1.getKey() + "年", totalNum1);
        }
        return totalNum;
    }
    public long setYearDownTopMap(Map<Integer, List<DocDownStatYearEntity>> collect, long totalNum, Map<String, Long> topMap){
        for (Map.Entry<Integer, List<DocDownStatYearEntity>> entry1 : collect.entrySet()) {
            long totalNum1 = 0l;
            for (DocDownStatYearEntity entity : entry1.getValue()) {
                totalNum += entity.getDownloadNum();
                totalNum1 += entity.getDownloadNum();
            }
            topMap.put(entry1.getKey() + "年", totalNum1);
        }
        return totalNum;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    /* Started by AICoder, pid:w9c3em9b49k8d4b143d0091df07cd9506935e8d8 */
    public void synchronizeDocDownStatData() {
        //获取前一天的日期
        Integer previousDayNumber= StatCurrentDateUtils.getPreviousDayNumber();
        //“昨天”是当年的第几周
        Integer previousWeekNumber = StatCurrentDateUtils.getPreviousWeekOfYear();
        //“昨天”是当年的第几个月
        Integer previousMonthNumber = StatCurrentDateUtils.getPreviousMonthOfYear();
        //“昨天”所在年份
        Integer previousYearNumber=StatCurrentDateUtils.getPreviousYear();
        List<DocDownStatDto> docDownStatWithDataList = dayRepository.getDocDownStatWithDataList(previousDayNumber, previousWeekNumber, previousMonthNumber, previousYearNumber);
        if(CollectionUtils.isEmpty(docDownStatWithDataList)){
            return;
        }
        //同步周表数据
        Map<String, List<DocDownStatWeekEntity>> weekMap= addOrUpdateWeekList(docDownStatWithDataList,previousWeekNumber);
        weekRepository.addDocDownStatWeekList(weekMap.getOrDefault(SystemConstants.TO_INSERT, Collections.emptyList()));
        weekRepository.updateDocDownStatWeekList(weekMap.getOrDefault(SystemConstants.TO_UPDATE, Collections.emptyList()));
        //同步月表数据
        Map<String, List<DocDownStatMonthEntity>> monthMap = addOrUpdateMonthList(docDownStatWithDataList,previousMonthNumber);
        monthRepository.addDocDownStatMonthList(monthMap.getOrDefault(SystemConstants.TO_INSERT, Collections.emptyList()));
        monthRepository.updateDocDownStatMonthList(monthMap.getOrDefault(SystemConstants.TO_UPDATE, Collections.emptyList()));
        //同步年表数据
        Map<String, List<DocDownStatYearEntity>> yearMap = addOrUpdateYearList(docDownStatWithDataList,previousYearNumber);
        yearRepository.addDocDownStatYearList(yearMap.getOrDefault(SystemConstants.TO_INSERT, Collections.emptyList()));
        yearRepository.updateDocDownStatYearList(yearMap.getOrDefault(SystemConstants.TO_UPDATE, Collections.emptyList()));
    }
    /* Ended by AICoder, pid:w9c3em9b49k8d4b143d0091df07cd9506935e8d8 */
    /**
     * 整合周统计数据列表，返回待新增与待更新的 Map 分组
     *
     * @param list    登录统计 DTO 列表
     * @param weekNum 目标周
     * @return Map<"toInsert", List>, Map<"toUpdate", List>
     */
    /* Started by AICoder, pid:5ba1cd29c3b69dd14c410ad920033246ec640385 */
    public Map<String, List<DocDownStatWeekEntity>> addOrUpdateWeekList(List<DocDownStatDto> list, int weekNum) {
        Map<String, List<DocDownStatWeekEntity>> result = new HashMap<>();
        //待新增的数据
        List<DocDownStatWeekEntity> toInsertList = new ArrayList<>();
        //待更新的数据
        List<DocDownStatWeekEntity> toUpdateList = new ArrayList<>();

        for (DocDownStatDto dto : list) {
            DocDownStatWeekEntity entity = new DocDownStatWeekEntity();
            entity.setDeptId(dto.getDeptId());
            entity.setUserId(dto.getUserId());
            entity.setResourceId(dto.getResourceId());
            entity.setDay(weekNum);

            if (dto.getWeekId() == null) {
                // 新增：weekId 为空，表示当前用户文档下载周表没有记录
                entity.setDownloadNum(dto.getDayDownNum());
                entity.setPreviewNum(dto.getDayViewNum());
                entity.setId(UUID.randomUUID().toString());
                toInsertList.add(entity);
            } else {
                // 更新：weekId 不为空，表示当前用户文档下载周表有记录
                entity.setDownloadNum(dto.getWeekDownNum() + dto.getDayDownNum());
                entity.setPreviewNum(dto.getWeekViewNum() + dto.getDayViewNum());
                entity.setId(dto.getWeekId());
                toUpdateList.add(entity);
            }
        }

        // 放入 Map
        result.put(SystemConstants.TO_INSERT, toInsertList);
        result.put(SystemConstants.TO_UPDATE, toUpdateList);
        return result;
    }
    /* Ended by AICoder, pid:5ba1cd29c3b69dd14c410ad920033246ec640385 */

    /**
     * 整合月统计数据列表，返回待新增与待更新的 Map 分组
     *
     * @param list    登录统计 DTO 列表
     * @param monthNum 目标月
     * @return Map<"toInsert", List>, Map<"toUpdate", List>
     */
    /* Started by AICoder, pid:e4e14jc87aqe177148a1091080892f4240b63b86 */
    public Map<String, List<DocDownStatMonthEntity>> addOrUpdateMonthList(List<DocDownStatDto> list, int monthNum) {
        Map<String, List<DocDownStatMonthEntity>> result = new HashMap<>();
        List<DocDownStatMonthEntity> toInsertList = new ArrayList<>();
        List<DocDownStatMonthEntity> toUpdateList = new ArrayList<>();

        for (DocDownStatDto dto : list) {
            DocDownStatMonthEntity entity = new DocDownStatMonthEntity();
            entity.setDeptId(dto.getDeptId());
            entity.setUserId(dto.getUserId());
            entity.setResourceId(dto.getResourceId());
            entity.setDay(monthNum);

            if (dto.getMonthId() == null) {
                entity.setDownloadNum(dto.getDayDownNum());
                entity.setPreviewNum(dto.getDayViewNum());
                entity.setId(UUID.randomUUID().toString());
                toInsertList.add(entity);
            } else {
                entity.setDownloadNum(dto.getMonthDownNum() + dto.getDayDownNum());
                entity.setPreviewNum(dto.getMonthViewNum() + dto.getDayViewNum());
                entity.setId(dto.getMonthId());
                toUpdateList.add(entity);
            }
        }

        result.put(SystemConstants.TO_INSERT, toInsertList);
        result.put(SystemConstants.TO_UPDATE, toUpdateList);
        return result;
    }
    /* Ended by AICoder, pid:e4e14jc87aqe177148a1091080892f4240b63b86 */
    /**
     * 整合年统计数据列表，返回待新增与待更新的 Map 分组
     *
     * @param list    登录统计 DTO 列表
     * @param yearNum 目标年
     * @return Map<"toInsert", List>, Map<"toUpdate", List>
     */
    /* Started by AICoder, pid:3c097h9347b7de914b96094ab09f8542b0c23471 */
    public Map<String, List<DocDownStatYearEntity>> addOrUpdateYearList(List<DocDownStatDto> list, int yearNum) {
        Map<String, List<DocDownStatYearEntity>> result = new HashMap<>();
        List<DocDownStatYearEntity> toInsertList = new ArrayList<>();
        List<DocDownStatYearEntity> toUpdateList = new ArrayList<>();

        for (DocDownStatDto dto : list) {
            DocDownStatYearEntity entity = new DocDownStatYearEntity();
            entity.setDeptId(dto.getDeptId());
            entity.setUserId(dto.getUserId());
            entity.setResourceId(dto.getResourceId());
            entity.setDay(yearNum);

            if (dto.getYearId() == null) {
                entity.setDownloadNum(dto.getDayDownNum());
                entity.setPreviewNum(dto.getDayViewNum());
                entity.setId(UUID.randomUUID().toString());
                toInsertList.add(entity);
            } else {
                entity.setDownloadNum(dto.getYearDownNum() + dto.getDayDownNum());
                entity.setPreviewNum(dto.getYearViewNum() + dto.getDayViewNum());
                entity.setId(dto.getYearId());
                toUpdateList.add(entity);
            }
        }

        result.put(SystemConstants.TO_INSERT, toInsertList);
        result.put(SystemConstants.TO_UPDATE, toUpdateList);
        return result;
    }
    /* Ended by AICoder, pid:3c097h9347b7de914b96094ab09f8542b0c23471 */

    //根据获取部门成员id列表(若部门id为空将默认返回所有部门的用户id列表)
    /* Started by AICoder, pid:kcc25b3b75bab3a144f208261035f8252d267e56 */
    public List<String> getDeptUserIds(String deptId) {
        if (StringUtils.isNotBlank(deptId)){
            DeptQueryDto queryDto=new DeptQueryDto();
            queryDto.setDeptId(deptId);
            //获取指定部门成员id列表
            List<DeptUserVo> subAllDeptUserList = deptDomainService.getSubAllDeptUserList(queryDto);
            if (CollectionUtils.isEmpty(subAllDeptUserList)){
                return Collections.emptyList();
            }
            return subAllDeptUserList.stream().map(DeptUserVo::getAccountId).collect(Collectors.toList());
        }else {
            //获取全部门成员id列表
            List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectAllDeptUserRelation();
            if (CollectionUtils.isEmpty(authDeptUserRelationPos)){
                return Collections.emptyList();
            }
            return authDeptUserRelationPos.stream().map(AuthDeptUserRelationPo::getUserId).collect(Collectors.toList());
        }
    }
    /* Ended by AICoder, pid:kcc25b3b75bab3a144f208261035f8252d267e56 */
    @Override
    public void export(DocDownStatQueryDto queryDto, HttpServletResponse response) {
        List<DocDownUserStatisticsVo> date = getUserStatisticFileDate(queryDto);
        exportUserLoginDetailExcel(response, date);
    }
    /* Started by AICoder, pid:h77244be20397151406c0b68e0c1af8d8a01fe45 */
    private void exportUserLoginDetailExcel(HttpServletResponse response, List<DocDownUserStatisticsVo> entityList) {
        response.reset();
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        String fileName = "用户下载文档统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            try (ServletOutputStream outputStream = response.getOutputStream();
                 ExcelWriter writer = EasyExcel.write(outputStream)
                         .excelType(ExcelTypeEnum.XLS)
                         .charset(StandardCharsets.UTF_8)
                         .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                         .build()) {

                List<String> allPeriods = extractAllPeriods(entityList);
                List<List<String>> headers = buildHeaders(allPeriods);
                List<List<Object>> data = buildData(entityList, allPeriods);

                WriteSheet sheet = EasyExcel.writerSheet("用户文档下载统计")
                        .head(headers)
                        .build();
                writer.write(data, sheet);
            }
        } catch (IOException e) {
            log.error("导出 Excel 失败", e);
        }
    }
    private List<String> extractAllPeriods(List<DocDownUserStatisticsVo> list) {
        Set<String> periods = new TreeSet<>();
        for (DocDownUserStatisticsVo item : list) {
            for (Map<String, String> periodMap : item.getPeriodDocDownTimes()) {
                periods.addAll(periodMap.keySet());
            }
        }
        return new ArrayList<>(periods);
    }

    private List<List<String>> buildHeaders(List<String> periods) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("用户名"));
        headers.add(Collections.singletonList("总数"));
        for (String period : periods) {
            headers.add(Collections.singletonList(period));
        }
        return headers;
    }

    private List<List<Object>> buildData(List<DocDownUserStatisticsVo> list, List<String> periods) {
        List<List<Object>> rows = new ArrayList<>();
        for (DocDownUserStatisticsVo item : list) {
            List<Object> row = new ArrayList<>();
            row.add(item.getUserName());
            row.add(item.getTotalNum());
            Map<String, String> periodMap = item.getPeriodDocDownTimes().stream()
                    .flatMap(m -> m.entrySet().stream())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            for (String period : periods) {
                row.add(periodMap.getOrDefault(period, ""));
            }
            rows.add(row);
        }
        return rows;
    }
    /* Ended by AICoder, pid:h77244be20397151406c0b68e0c1af8d8a01fe45 */
}
