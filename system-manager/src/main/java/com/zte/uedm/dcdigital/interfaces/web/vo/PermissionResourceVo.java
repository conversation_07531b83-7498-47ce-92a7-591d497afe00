/* Started by AICoder, pid:b847b2c55dz4ea314efe0be04037125ce8188323 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 权限资源视图对象。
 *
 * 该类用于表示权限资源的信息，包括权限的基本属性、关联的资源信息以及操作码。
 */
@Getter
@Setter
@ToString
public class PermissionResourceVo {

    /**
     * ID - 唯一标识符。
     *
     * 用于唯一标识一个权限资源。
     */
    private String id;

    /**
     * 名称 - 权限名称。
     *
     * 描述权限的具体名称。
     */
    private String name;

    /**
     * 资源类型 - 关联资源的类型。
     * 1:产品小类、2：项目、3：地区
     * 用于描述资源的类型。
     */
    private Integer type;

    /**
     * 产品小类ID/项目ID - 根据resourceType进行区分。
     *
     * 用于标识权限所关联的具体资源。
     */
    private String entityId;

    /**
     * 操作码 - 权限的操作码。
     *
     * 用于标识权限的具体操作。
     */
    private String permissionCode;

    /**
     * 地区父级id。
     */
    private String parentId;
}

/* Ended by AICoder, pid:b847b2c55dz4ea314efe0be04037125ce8188323 */