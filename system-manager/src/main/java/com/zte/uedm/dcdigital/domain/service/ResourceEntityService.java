package com.zte.uedm.dcdigital.domain.service;

/* Started by AICoder, pid:z23452002csaf4e1473c0b0d60221b37848102c5 */
import com.zte.uedm.dcdigital.common.bean.system.ResourceEntityDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ResourceEntityVo;

/**
 * 服务接口，用于管理资源实体的CRUD操作。
 */
public interface ResourceEntityService {

    /**
     * 插入一个新的资源实体。
     *
     * @param resourceEntityDto 要插入的资源实体数据传输对象
     * @return 插入后的资源实体值对象
     */
    ResourceEntityVo insert(ResourceEntityDto resourceEntityDto);

    /**
     * 根据实体ID查询资源实体。
     *
     * @param entityId 资源实体的唯一标识符
     * @return 查找到的资源实体值对象，如果没有找到则返回null
     */
    ResourceEntityVo selectByEntityId(String entityId);

    /**
     * 根据实体ID删除资源实体。
     *
     * @param entityId 要删除的资源实体的唯一标识符
     */
    void deleteByEntityId(String entityId);
}
/* Ended by AICoder, pid:z23452002csaf4e1473c0b0d60221b37848102c5 */
