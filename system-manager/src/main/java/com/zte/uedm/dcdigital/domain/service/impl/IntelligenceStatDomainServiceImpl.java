package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.application.stat.executor.LoginStatsQueryService;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.AuthDeptEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DeptRepository;
import com.zte.uedm.dcdigital.domain.service.IntelligenceStatDomainService;
import com.zte.uedm.dcdigital.domain.service.UserService;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.domain.utils.SearchStatTimeUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.*;
import com.zte.uedm.dcdigital.interfaces.web.dto.IntelligenceStatisticExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.IntelligenceStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.IntelligenceStatisticVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能体统计领域服务实现类
 */
@Service
@Slf4j
public class IntelligenceStatDomainServiceImpl implements IntelligenceStatDomainService {

    @Autowired
    private DeptUserRelationMapper deptUserRelationMapper;

    @Autowired
    private DeptRepository deptRepository;

    @Autowired
    private IntelligenceStatDayMapper intelligenceStatDayMapper;

    @Autowired
    private IntelligenceStatWeekMapper intelligenceStatWeekMapper;

    @Autowired
    private IntelligenceStatMonthMapper intelligenceStatMonthMapper;

    @Autowired
    private IntelligenceStatYearMapper intelligenceStatYearMapper;

    @Autowired
    private DeptMapper deptMapper;

    @Autowired
    private UserService userService;
    @Autowired
    private LoginStatsQueryService loginStatsQueryService;

    @Override
    public IntelligenceStatisticVo queryIntelligenceStatistic(IntelligenceStatisticQueryDto queryDto) {
        log.info("queryIntelligenceStatistic start, queryDto: {}", queryDto);

        IntelligenceStatisticVo result = new IntelligenceStatisticVo();

        try {
            // 1. 参数验证和时间格式转换
            String[] timeRange = SearchStatTimeUtils.convertTimeRangeForQuery(
                queryDto.getStartTime(), queryDto.getEndTime(), queryDto.getTimeType());
            String startTime = timeRange[0];
            String endTime = timeRange[1];

            // 2. 获取部门下的用户ID列表
            List<String> userIds = getDeptUserIds(queryDto.getDeptId());
            log.info("Found {} users for department {}: {}", userIds.size(), queryDto.getDeptId(), userIds);
            if (userIds.isEmpty()) {
                log.warn("No users found for department: {}", queryDto.getDeptId());
                return createEmptyIntelligenceResult();
            }

            // 3. 根据timeType选择对应的Mapper进行查询
            Map<String, Object> summaryData = getIntelligenceSummaryData(userIds, startTime, endTime, queryDto.getTimeType());
            log.info("summaryData: {}", JSON.toJSONString(summaryData));

            //获取当前时间段，当前部门活跃人数
            List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                    ParseUtils.parseStringToInt(queryDto.getStartTime()),
                    ParseUtils.parseStringToInt(queryDto.getEndTime()),
                    queryDto.getTimeType(),
                    queryDto.getDeptId());

            // 4. 设置基础统计数据
            setIntelligenceBasicStatistics(result, summaryData, activeUserIds, queryDto.getTimeType());

            // 5. 设置时间序列数据
            setIntelligenceTimeSeriesData(result, activeUserIds, startTime, endTime, queryDto.getTimeType());

            // 6. 设置智能体列表数据
            setIntelligenceListData(result, userIds, startTime, endTime, queryDto.getTimeType());

            // 7. 设置用户统计数据（需要分页）
            setIntelligenceUserStatData(result, userIds, startTime, endTime, queryDto.getTimeType(),
                queryDto.getPageNum(), queryDto.getPageSize());

            // 8. 设置部门统计数据
            setDepartmentStatData(result, userIds, startTime, endTime, queryDto.getTimeType(), queryDto.getDeptId());

            // 9. 设置智能体特有字段
            result.setIntelligenceNum(2L); // 物料推荐 + 标书分析

        } catch (Exception e) {
            log.error("queryIntelligenceStatistic error", e);
            throw new RuntimeException("Query intelligence statistics failed", e);
        }

        return result;
    }

    @Override
    public void exportIntelligenceStatistic(IntelligenceStatisticExportDto exportDto) {
        log.info("exportIntelligenceStatistic start, exportDto: {}", exportDto);

        try {
            // 1. 参数验证和时间格式转换
            String[] timeRange = SearchStatTimeUtils.convertTimeRangeForQuery(
                exportDto.getStartTime(), exportDto.getEndTime(), exportDto.getTimeType());
            String startTime = timeRange[0];
            String endTime = timeRange[1];

            // 2. 获取部门下的用户ID列表
            List<String> userIds = getDeptUserIds(exportDto.getDeptId());
            log.info("Found {} users for department {}: {}", userIds.size(), exportDto.getDeptId(), userIds);
            if (userIds.isEmpty()) {
                log.warn("No users found for department: {}", exportDto.getDeptId());
                return;
            }

            // 3. 获取用户统计数据（指定页面的数据）
            List<IntelligenceStatisticVo.UserStatDetail> userStatList = getIntelligenceUserStatListForExport(
                userIds, startTime, endTime, exportDto.getTimeType(),
                exportDto.getPageNum(), exportDto.getPageSize());

            // 4. 生成完整的时间范围用于Excel表头
            List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(
                startTime, endTime, exportDto.getTimeType());

            // 5. 导出Excel
            exportIntelligenceUserStatListToExcel(userStatList, completeTimeRange, exportDto.getTimeType());

        } catch (Exception e) {
            log.error("exportIntelligenceStatistic error", e);
            throw new RuntimeException("Export intelligence statistics failed", e);
        }
    }

    @Override
    public void processIntelligenceBuryingPoint(Integer operationType, String userId) {
        log.info("processIntelligenceBuryingPoint start, operationType: {}", operationType);

        try {
            if (StringUtils.isBlank(userId)) {
                log.warn("Unable to get user ID, skip intelligence burying point processing");
                return;
            }

            // 2. 根据用户ID查询部门ID（这里需要实现获取部门ID的逻辑，参考搜索统计的实现）
            String deptId = getDeptIdByUserId(userId);
            if (StringUtils.isBlank(deptId)) {
                log.warn("Unable to get department ID for user {}, skip intelligence burying point processing", userId);
                return;
            }

            // 3. 生成当前日期（YYYYMMDD格式）
            String currentDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 4. 处理智能体埋点数据（存储逻辑与搜索埋点一致）
            processIntelligenceBuryingPointData(userId, deptId, currentDay, operationType);

        } catch (Exception e) {
            log.error("processIntelligenceBuryingPoint error", e);
            // 埋点处理失败不应该影响主流程，只记录日志
        }
    }

    @Override
    @Async
    public void aggregateIntelligenceStatData() {
        log.info("Starting intelligence statistics data aggregation");

        try {
            // 获取昨天的日期
            LocalDate yesterday = LocalDate.now().minusDays(1);

            log.info("Aggregating intelligence statistics data for date: {}", yesterday);

            // 汇聚到周表
            aggregateToWeekTable(yesterday);

            // 汇聚到月表
            aggregateToMonthTable(yesterday);

            // 汇聚到年表
            aggregateToYearTable(yesterday);

            log.info("Intelligence statistics data aggregation completed successfully");

        } catch (Exception e) {
            log.error("Failed to aggregate intelligence statistics data", e);
            throw new RuntimeException("汇聚智能体统计数据失败", e);
        }
    }

    /**
     * 根据用户ID获取部门ID
     */
    private String getDeptIdByUserId(String userId) {
        try {
            List<AuthDeptUserRelationPo> relations = deptUserRelationMapper.selectByUserId(userId);
            if (relations != null && !relations.isEmpty()) {
                return relations.get(0).getDeptId();
            }
            log.warn("No department found for user: {}", userId);
            return null;
        } catch (Exception e) {
            log.error("Error getting department ID for user: {}", userId, e);
            return null;
        }
    }

    /**
     * 处理智能体埋点数据
     */
    private void processIntelligenceBuryingPointData(String userId, String deptId, String currentDay, Integer operationType) {
        log.info("Processing intelligence burying point data: userId={}, deptId={}, currentDay={}, operationType={}",
                userId, deptId, currentDay, operationType);

        try {
            // 查询是否存在同用户、同日期的记录
            IntelligenceStatDayPo existingRecord = intelligenceStatDayMapper.selectByUserAndDay(userId, currentDay);

            if (existingRecord != null) {
                // 存在记录，更新对应的计数
                updateOperationCount(userId, currentDay, operationType);
                log.info("Updated existing intelligence stat record for user: {}, day: {}, operationType: {}",
                        userId, currentDay, operationType);
            } else {
                // 不存在记录，插入新记录
                insertNewIntelligenceStatRecord(userId, deptId, currentDay, operationType);
                log.info("Inserted new intelligence stat record for user: {}, day: {}, operationType: {}",
                        userId, currentDay, operationType);
            }
        } catch (Exception e) {
            log.error("Error processing intelligence burying point data", e);
        }
    }

    /**
     * 更新操作计数
     */
    private void updateOperationCount(String userId, String day, Integer operationType) {
        switch (operationType) {
            case 1: // 物料智能体
                intelligenceStatDayMapper.updateMaterialNum(userId, day);
                break;
            case 2: // 标书分析智能体
                intelligenceStatDayMapper.updateBidNum(userId, day);
                break;
            default:
                log.warn("Unknown intelligence operation type: {}", operationType);
                break;
        }
    }

    /**
     * 插入新的智能体统计记录
     */
    private void insertNewIntelligenceStatRecord(String userId, String deptId, String day, Integer operationType) {
        IntelligenceStatDayPo newRecord = new IntelligenceStatDayPo();
        newRecord.setId(UUID.randomUUID().toString());
        newRecord.setUserId(userId);
        newRecord.setDeptId(deptId);
        newRecord.setDay(day);
        newRecord.setCreateTime(DateTimeUtils.getCurrentTime());

        // 根据操作类型设置初始计数
        switch (operationType) {
            case 1: // 物料智能体
                newRecord.setMaterialNum("1");
                newRecord.setBidNum("0");
                break;
            case 2: // 标书分析智能体
                newRecord.setMaterialNum("0");
                newRecord.setBidNum("1");
                break;
            default:
                newRecord.setMaterialNum("0");
                newRecord.setBidNum("0");
                break;
        }

        intelligenceStatDayMapper.insert(newRecord);
    }

    /**
     * 汇聚到周表
     */
    private void aggregateToWeekTable(LocalDate date) {
        try {
            // 计算周数
            int year = date.getYear();
            int weekOfYear = date.get(WeekFields.ISO.weekOfYear());
            String weekStr = String.format("%04d%02d", year, weekOfYear);

            // 计算该周的开始和结束日期
            LocalDate startOfWeek = date.with(WeekFields.ISO.dayOfWeek(), 1);
            LocalDate endOfWeek = date.with(WeekFields.ISO.dayOfWeek(), 7);

            String startDayStr = startOfWeek.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfWeek.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating intelligence data to week table for week: {}, date range: {} to {}",
                    weekStr, startDayStr, endDayStr);

            // 查询该周的汇聚数据
            List<IntelligenceStatDayPo> aggregatedData = intelligenceStatDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            for (IntelligenceStatDayPo data : aggregatedData) {
                // 查询是否已存在周记录
                IntelligenceStatWeekPo existingWeekRecord = intelligenceStatWeekMapper.selectByUserAndWeek(data.getUserId(), weekStr);

                if (existingWeekRecord != null) {
                    // 更新现有记录
                    int materialCount = Integer.parseInt(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    int bidCount = Integer.parseInt(data.getBidNum() != null ? data.getBidNum() : "0");

                    if (materialCount > 0) {
                        intelligenceStatWeekMapper.updateMaterialNum(data.getUserId(), weekStr, materialCount);
                    }
                    if (bidCount > 0) {
                        intelligenceStatWeekMapper.updateBidNum(data.getUserId(), weekStr, bidCount);
                    }
                } else {
                    // 插入新记录
                    IntelligenceStatWeekPo weekRecord = new IntelligenceStatWeekPo();
                    weekRecord.setId(UUID.randomUUID().toString());
                    weekRecord.setDay(weekStr);
                    weekRecord.setUserId(data.getUserId());
                    weekRecord.setDeptId(data.getDeptId());
                    weekRecord.setMaterialNum(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    weekRecord.setBidNum(data.getBidNum() != null ? data.getBidNum() : "0");

                    intelligenceStatWeekMapper.insert(weekRecord);
                }
            }

            log.info("Successfully aggregated {} records to intelligence week table for week: {}",
                    aggregatedData.size(), weekStr);

        } catch (Exception e) {
            log.error("Error aggregating intelligence data to week table for date: {}", date, e);
        }
    }

    /**
     * 汇聚到月表
     */
    private void aggregateToMonthTable(LocalDate date) {
        try {
            // 计算月份
            int year = date.getYear();
            int month = date.getMonthValue();
            String monthStr = String.format("%04d%02d", year, month);

            // 计算该月的开始和结束日期
            LocalDate startOfMonth = date.withDayOfMonth(1);
            LocalDate endOfMonth = date.withDayOfMonth(date.lengthOfMonth());

            String startDayStr = startOfMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating intelligence data to month table for month: {}, date range: {} to {}",
                    monthStr, startDayStr, endDayStr);

            // 查询该月的汇聚数据
            List<IntelligenceStatDayPo> aggregatedData = intelligenceStatDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            for (IntelligenceStatDayPo data : aggregatedData) {
                // 查询是否已存在月记录
                IntelligenceStatMonthPo existingMonthRecord = intelligenceStatMonthMapper.selectByUserAndMonth(data.getUserId(), monthStr);

                if (existingMonthRecord != null) {
                    // 更新现有记录
                    int materialCount = Integer.parseInt(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    int bidCount = Integer.parseInt(data.getBidNum() != null ? data.getBidNum() : "0");

                    if (materialCount > 0) {
                        intelligenceStatMonthMapper.updateMaterialNum(data.getUserId(), monthStr, materialCount);
                    }
                    if (bidCount > 0) {
                        intelligenceStatMonthMapper.updateBidNum(data.getUserId(), monthStr, bidCount);
                    }
                } else {
                    // 插入新记录
                    IntelligenceStatMonthPo monthRecord = new IntelligenceStatMonthPo();
                    monthRecord.setId(UUID.randomUUID().toString());
                    monthRecord.setDay(monthStr);
                    monthRecord.setUserId(data.getUserId());
                    monthRecord.setDeptId(data.getDeptId());
                    monthRecord.setMaterialNum(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    monthRecord.setBidNum(data.getBidNum() != null ? data.getBidNum() : "0");

                    intelligenceStatMonthMapper.insert(monthRecord);
                }
            }

            log.info("Successfully aggregated {} records to intelligence month table for month: {}",
                    aggregatedData.size(), monthStr);

        } catch (Exception e) {
            log.error("Error aggregating intelligence data to month table for date: {}", date, e);
        }
    }

    /**
     * 汇聚到年表
     */
    private void aggregateToYearTable(LocalDate date) {
        try {
            // 计算年份
            int year = date.getYear();
            String yearStr = String.valueOf(year);

            // 计算该年的开始和结束日期
            LocalDate startOfYear = date.withDayOfYear(1);
            LocalDate endOfYear = date.withDayOfYear(date.lengthOfYear());

            String startDayStr = startOfYear.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfYear.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating intelligence data to year table for year: {}, date range: {} to {}",
                    yearStr, startDayStr, endDayStr);

            // 查询该年的汇聚数据
            List<IntelligenceStatDayPo> aggregatedData = intelligenceStatDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            for (IntelligenceStatDayPo data : aggregatedData) {
                // 查询是否已存在年记录
                IntelligenceStatYearPo existingYearRecord = intelligenceStatYearMapper.selectByUserAndYear(data.getUserId(), yearStr);

                if (existingYearRecord != null) {
                    // 更新现有记录
                    int materialCount = Integer.parseInt(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    int bidCount = Integer.parseInt(data.getBidNum() != null ? data.getBidNum() : "0");

                    if (materialCount > 0) {
                        intelligenceStatYearMapper.updateMaterialNum(data.getUserId(), yearStr, materialCount);
                    }
                    if (bidCount > 0) {
                        intelligenceStatYearMapper.updateBidNum(data.getUserId(), yearStr, bidCount);
                    }
                } else {
                    // 插入新记录
                    IntelligenceStatYearPo yearRecord = new IntelligenceStatYearPo();
                    yearRecord.setId(UUID.randomUUID().toString());
                    yearRecord.setDay(yearStr);
                    yearRecord.setUserId(data.getUserId());
                    yearRecord.setDeptId(data.getDeptId());
                    yearRecord.setMaterialNum(data.getMaterialNum() != null ? data.getMaterialNum() : "0");
                    yearRecord.setBidNum(data.getBidNum() != null ? data.getBidNum() : "0");

                    intelligenceStatYearMapper.insert(yearRecord);
                }
            }

            log.info("Successfully aggregated {} records to intelligence year table for year: {}",
                    aggregatedData.size(), yearStr);

        } catch (Exception e) {
            log.error("Error aggregating intelligence data to year table for date: {}", date, e);
        }
    }

    /**
     * 获取部门下的用户ID列表（包括所有子部门）
     */
    private List<String> getDeptUserIds(String deptId) {
        try {
            // 1. 获取当前部门及其所有子部门
            List<AuthDeptEntity> allDepts = deptRepository.querySubDeptPathIds(deptId);
            if (allDepts.isEmpty()) {
                log.warn("No departments found for deptId: {}", deptId);
                return Collections.emptyList();
            }

            // 2. 提取所有部门ID
            List<String> allDeptIds = allDepts.stream()
                    .map(AuthDeptEntity::getDeptId)
                    .collect(Collectors.toList());
            log.info("Found {} departments (including sub-departments) for deptId: {}, deptIds: {}",
                    allDeptIds.size(), deptId, allDeptIds);

            // 3. 获取所有部门的用户关系
            List<AuthDeptUserRelationPo> allRelations = new ArrayList<>();
            for (String currentDeptId : allDeptIds) {
                List<AuthDeptUserRelationPo> relations = deptUserRelationMapper.selectByDeptId(currentDeptId);
                if (relations != null && !relations.isEmpty()) {
                    allRelations.addAll(relations);
                }
            }

            // 4. 去重并返回用户ID列表
            if (!allRelations.isEmpty()) {
                List<String> userIds = allRelations.stream()
                        .map(AuthDeptUserRelationPo::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
                log.info("Found {} unique users across all departments for deptId: {}", userIds.size(), deptId);
                return userIds;
            }
        } catch (Exception e) {
            log.error("Failed to query department users for deptId: {}", deptId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 创建空的智能体统计结果
     */
    private IntelligenceStatisticVo createEmptyIntelligenceResult() {
        IntelligenceStatisticVo result = new IntelligenceStatisticVo();
        result.setTotal(0L);
        result.setAvg(0L);
        result.setIntelligenceNum(2L);
        result.setTotalSystem(new ArrayList<>());
        result.setIntelligenceList(new ArrayList<>());
        result.setLevelDepart(new HashMap<>());
        result.setUserStatList(new ArrayList<>());
        return result;
    }

    /**
     * 获取智能体汇总统计数据
     */
    private Map<String, Object> getIntelligenceSummaryData(List<String> userIds, String startTime, String endTime, Integer timeType) {
        switch (timeType) {
            case 1: // 天
                return intelligenceStatDayMapper.selectSummaryByUserListAndTimeRange(userIds, startTime, endTime);
            case 2: // 周
                return intelligenceStatWeekMapper.selectSummaryByUserListAndTimeRange(userIds, startTime, endTime);
            case 3: // 月
                return intelligenceStatMonthMapper.selectSummaryByUserListAndTimeRange(userIds, startTime, endTime);
            case 4: // 年
                return intelligenceStatYearMapper.selectSummaryByUserListAndTimeRange(userIds, startTime, endTime);
            default:
                return intelligenceStatDayMapper.selectSummaryByUserListAndTimeRange(userIds, startTime, endTime);
        }
    }

    /**
     * 获取智能体类型统计数据
     */
    private List<Map<String, Object>> getIntelligenceTypeStatData(List<String> userIds, String startTime, String endTime, Integer timeType) {
        switch (timeType) {
            case 1: // 天
                return intelligenceStatDayMapper.selectIntelligenceTypeStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 2: // 周
                return intelligenceStatWeekMapper.selectIntelligenceTypeStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 3: // 月
                return intelligenceStatMonthMapper.selectIntelligenceTypeStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 4: // 年
                return intelligenceStatYearMapper.selectIntelligenceTypeStatByUserListAndTimeRange(userIds, startTime, endTime);
            default:
                return intelligenceStatDayMapper.selectIntelligenceTypeStatByUserListAndTimeRange(userIds, startTime, endTime);
        }
    }

    /**
     * 获取部门统计数据
     */
    private List<Map<String, Object>> getDepartmentStatData(List<String> userIds, String startTime, String endTime, Integer timeType) {
        switch (timeType) {
            case 1: // 天
                return intelligenceStatDayMapper.selectDepartmentStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 2: // 周
                return intelligenceStatWeekMapper.selectDepartmentStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 3: // 月
                return intelligenceStatMonthMapper.selectDepartmentStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 4: // 年
                return intelligenceStatYearMapper.selectDepartmentStatByUserListAndTimeRange(userIds, startTime, endTime);
            default:
                return intelligenceStatDayMapper.selectDepartmentStatByUserListAndTimeRange(userIds, startTime, endTime);
        }
    }

    /**
     * 设置智能体基础统计数据
     */
    private void setIntelligenceBasicStatistics(IntelligenceStatisticVo result, Map<String, Object> summaryData, List<String> userIds, Integer timeType) {
        log.info("setIntelligenceBasicStatistics - summaryData: {}", JSON.toJSONString(summaryData));

        Long total = getLongValue(summaryData, "total");

        // 使用部门总人数作为分母
        Long deptTotalUserCount = (long) userIds.size();

        log.info("setIntelligenceBasicStatistics - total: {}, deptTotalUserCount: {}", total, deptTotalUserCount);

        result.setTotal(total);

        // 计算人均使用次数 - 基于部门总人数计算平均值（四舍五入）
        Long avg = deptTotalUserCount > 0 ? Math.round((double) total / deptTotalUserCount) : 0L;
        result.setAvg(avg);

        log.info("setIntelligenceBasicStatistics - final avg: {}", avg);
    }

    /**
     * 获取Long类型值的辅助方法
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                log.warn("Failed to parse string to long: {}", value);
                return 0L;
            }
        }
        return 0L;
    }

    /**
     * 设置智能体时间序列数据
     */
    private void setIntelligenceTimeSeriesData(IntelligenceStatisticVo result, List<String> userIds, String startTime, String endTime, Integer timeType) {
        List<Map<String, Object>> timeSeriesData = getIntelligenceTimeSeriesData(userIds, startTime, endTime, timeType);
        log.info("timeSeriesData size: {}, data: {}", timeSeriesData.size(), JSON.toJSONString(timeSeriesData));

        // 使用部门总人数作为人均计算的分母
        Long deptTotalUserCount = (long) userIds.size();
        log.info("Dept total user count: {}", deptTotalUserCount);

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, timeType);
        log.info("Generated complete time range: {}", completeTimeRange);

        // 将查询结果转换为Map，便于查找
        Map<String, Map<String, Object>> timeDataMap = new HashMap<>();
        Map<String, String> timeFormatMap = new HashMap<>(); // 存储显示时间到原始时间的映射
        for (Map<String, Object> data : timeSeriesData) {
            String day = (String) data.get("day");
            String formattedTime = SearchStatTimeUtils.formatTimeDisplay(day, timeType);
            timeDataMap.put(formattedTime, data);
            timeFormatMap.put(formattedTime, day); // 保存原始时间
        }

        List<IntelligenceStatisticVo.TotalSystem> totalSystemList = new ArrayList<>();

        // 遍历完整的时间范围，确保每个时间点都有数据
        for (String timePoint : completeTimeRange) {
            Map<String, Object> data = timeDataMap.get(timePoint);

            Long total = 0L;
            Long avg = 0L;

            if (data != null) {
                total = getLongValue(data, "total");

                // 使用部门总人数计算人均（统一分母，四舍五入）
                avg = deptTotalUserCount > 0 ? Math.round((double) total / deptTotalUserCount) : 0L;

                // 获取SQL查询返回的userCount用于对比
                Long sqlUserCount = getLongValue(data, "usercount");

                log.info("TimePoint: {}, total: {}, sqlUserCount: {}, deptTotalUserCount: {}, avg: {}",
                    timePoint, total, sqlUserCount, deptTotalUserCount, avg);
            }

            IntelligenceStatisticVo.TotalSystem totalSystem = new IntelligenceStatisticVo.TotalSystem();
            totalSystem.setTotal(total);
            totalSystem.setAvg(avg);
            totalSystem.setTimePeriod(timePoint);
            totalSystemList.add(totalSystem);
        }

        result.setTotalSystem(totalSystemList);
    }

    /**
     * 设置智能体列表数据
     */
    private void setIntelligenceListData(IntelligenceStatisticVo result, List<String> userIds, String startTime, String endTime, Integer timeType) {
        List<Map<String, Object>> intelligenceTypeData = getIntelligenceTypeStatData(userIds, startTime, endTime, timeType);
        log.info("intelligenceTypeData size: {}, data: {}", intelligenceTypeData.size(), JSON.toJSONString(intelligenceTypeData));

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, timeType);
        log.info("Generated complete time range for intelligence list: {}", completeTimeRange);

        // 按时间点分组并汇总数据
        Map<String, IntelligenceStatisticVo.IntelligenceDetail> timePointMap = new HashMap<>();

        // 初始化所有时间点的数据
        for (String timePoint : completeTimeRange) {
            IntelligenceStatisticVo.IntelligenceDetail detail = new IntelligenceStatisticVo.IntelligenceDetail();
            detail.setMaterialNum(0L);
            detail.setBidNum(0L);
            detail.setTimePeriod(timePoint);
            timePointMap.put(timePoint, detail);
        }

        // 处理查询到的数据
        for (Map<String, Object> data : intelligenceTypeData) {
            String day = (String) data.get("day");
            String intelligenceType = (String) data.get("intelligencetype");
            Long num = getLongValue(data, "num");

            // 格式化时间显示
            String formattedTime = SearchStatTimeUtils.formatTimeDisplay(day, timeType);

            IntelligenceStatisticVo.IntelligenceDetail detail = timePointMap.get(formattedTime);
            if (detail != null) {
                if ("material".equals(intelligenceType)) {
                    detail.setMaterialNum(detail.getMaterialNum() + num);
                } else if ("bid".equals(intelligenceType)) {
                    detail.setBidNum(detail.getBidNum() + num);
                }
            }
        }

        // 转换为列表，按时间顺序排序
        List<IntelligenceStatisticVo.IntelligenceDetail> intelligenceList = new ArrayList<>();
        for (String timePoint : completeTimeRange) {
            intelligenceList.add(timePointMap.get(timePoint));
        }

        result.setIntelligenceList(intelligenceList);

        log.info("setIntelligenceListData completed, intelligenceList size: {}", intelligenceList.size());
    }

    /**
     * 设置部门统计数据
     */
    private void setDepartmentStatData(IntelligenceStatisticVo result, List<String> userIds, String startTime, String endTime, Integer timeType, String parentDeptId) {
        // 1. 获取直接子部门
        List<AuthDeptPo> childDepts = getChildDepartments(parentDeptId);
        if (childDepts.isEmpty()) {
            log.info("No child departments found for parentDeptId: {}, not returning department statistics", parentDeptId);
            result.setLevelDepart(new HashMap<>());
            return;
        }

        // 2. 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, timeType);
        log.info("Generated complete time range for department stat: {}", completeTimeRange);

        // 3. 为每个直接子部门聚合其所有底层部门的数据
        Map<String, Map<String, IntelligenceStatisticVo.DepartmentDetail>> timePointDeptMap = aggregateDepartmentDataByHierarchy(childDepts, startTime, endTime, timeType);

        // 4. 补充缺失的部门数据
        Set<String> allDeptIds = childDepts.stream().map(AuthDeptPo::getDeptId).collect(Collectors.toSet());
        Map<String, List<IntelligenceStatisticVo.DepartmentDetail>> timePointDepartmentMap = fillMissingDepartmentData(completeTimeRange, timePointDeptMap, allDeptIds);

        result.setLevelDepart(timePointDepartmentMap);
        log.info("setDepartmentStatData completed, levelDepart size: {}", timePointDepartmentMap.size());
    }




    /**
     * 创建部门详情对象
     */
    private IntelligenceStatisticVo.DepartmentDetail createDepartmentDetail(String deptId, Long totalNum, Long userCount) {
        Long avgNum = userCount > 0 ? Math.round((double) totalNum / userCount) : 0L;
        String departName = getDepartmentName(deptId);

        IntelligenceStatisticVo.DepartmentDetail departmentDetail = new IntelligenceStatisticVo.DepartmentDetail();
        departmentDetail.setDepartName(departName);
        departmentDetail.setTotalNum(totalNum);
        departmentDetail.setAvgNum(avgNum);

        return departmentDetail;
    }

    /**
     * 补充缺失的部门数据
     */
    private Map<String, List<IntelligenceStatisticVo.DepartmentDetail>> fillMissingDepartmentData(
            List<String> completeTimeRange,
            Map<String, Map<String, IntelligenceStatisticVo.DepartmentDetail>> timePointDeptMap,
            Set<String> allDeptIds) {

        Map<String, List<IntelligenceStatisticVo.DepartmentDetail>> timePointDepartmentMap = new LinkedHashMap<>();

        for (String timePoint : completeTimeRange) {
            List<IntelligenceStatisticVo.DepartmentDetail> departmentList = new ArrayList<>();
            Map<String, IntelligenceStatisticVo.DepartmentDetail> deptMap = timePointDeptMap.get(timePoint);

            for (String deptId : allDeptIds) {
                IntelligenceStatisticVo.DepartmentDetail departmentDetail;

                if (deptMap != null && deptMap.containsKey(deptId)) {
                    departmentDetail = deptMap.get(deptId);
                } else {
                    departmentDetail = createDepartmentDetailWithZeroUsage(deptId, timePoint);
                }

                departmentList.add(departmentDetail);
            }

            timePointDepartmentMap.put(timePoint, departmentList);
        }

        return timePointDepartmentMap;
    }

    /**
     * 创建使用次数为0的部门详情对象
     */
    private IntelligenceStatisticVo.DepartmentDetail createDepartmentDetailWithZeroUsage(String deptId, String timePoint) {
        String departName = getDepartmentName(deptId);
        IntelligenceStatisticVo.DepartmentDetail departmentDetail = new IntelligenceStatisticVo.DepartmentDetail();
        departmentDetail.setDepartName(departName);
        departmentDetail.setTotalNum(0L);
        departmentDetail.setAvgNum(0L);

        log.debug("Added missing department {} for time point {} with zero usage", departName, timePoint);
        return departmentDetail;
    }

    /**
     * 根据父部门ID获取直接子部门（只获取下一层级的部门）
     */
    private List<AuthDeptPo> getChildDepartments(String parentDeptId) {
        try {
            if (StringUtils.isBlank(parentDeptId)) {
                log.warn("Parent department ID is blank, returning empty list");
                return new ArrayList<>();
            }
            List<AuthDeptPo> childDepts = deptMapper.selectChildDeptsByParentId(parentDeptId);
            log.info("Found {} direct child departments for parentDeptId: {}", childDepts.size(), parentDeptId);
            return childDepts;
        } catch (Exception e) {
            log.error("Error getting child departments for parentDeptId: {}", parentDeptId, e);
            return new ArrayList<>();
        }
    }

    /* Started by AICoder, pid:h90cbn62a1k13191433d09f111a8523ee582514e */
    /**
     * 按层级聚合部门数据
     * 为每个直接子部门聚合其所有底层部门的数据
     */
    private Map<String, Map<String, IntelligenceStatisticVo.DepartmentDetail>> aggregateDepartmentDataByHierarchy(
            List<AuthDeptPo> childDepts, String startTime, String endTime, Integer timeType) {

        Map<String, Map<String, IntelligenceStatisticVo.DepartmentDetail>> timePointDeptMap = new HashMap<>();

        for (AuthDeptPo childDept : childDepts) {
            String childDeptId = childDept.getDeptId();

            //获取当前时间段，当前部门活跃人数
            List<String> activeUserIds = loginStatsQueryService.getActiveUsers(
                    ParseUtils.parseStringToInt(startTime),
                    ParseUtils.parseStringToInt(endTime),
                    timeType,
                    childDeptId);
            if (activeUserIds.isEmpty()) {
                log.info("No users found for child department: {}", childDeptId);
                continue;
            }

            // 查询该子部门的统计数据
            List<Map<String, Object>> deptStatData = getDepartmentStatData(activeUserIds, startTime, endTime, timeType);

            // 按时间点聚合数据
            for (Map<String, Object> data : deptStatData) {
                String day = (String) data.get("day");
                Long totalNum = getLongValue(data, "totalnum");

                String formattedTime = SearchStatTimeUtils.formatTimeDisplay(day, timeType);

                // 重新计算该部门在统计表中的实际用户数（不限时间）
                Long actualUserCount = (long) activeUserIds.size();
                IntelligenceStatisticVo.DepartmentDetail departmentDetail = createDepartmentDetail(childDeptId, totalNum, actualUserCount);

                // 如果该时间点已有该部门数据，则累加
                timePointDeptMap.computeIfAbsent(formattedTime, k -> new HashMap<>())
                        .merge(childDeptId, departmentDetail, (existing, newDetail) -> {
                            Long newTotalNum = existing.getTotalNum() + newDetail.getTotalNum();
                            Long newAvgNum = actualUserCount > 0 ? Math.round((double) newTotalNum / actualUserCount) : 0L;

                            IntelligenceStatisticVo.DepartmentDetail merged = new IntelligenceStatisticVo.DepartmentDetail();
                            merged.setDepartName(existing.getDepartName());
                            merged.setTotalNum(newTotalNum);
                            merged.setAvgNum(newAvgNum);
                            return merged;
                        });
            }
        }

        return timePointDeptMap;
    }

    /**
     * 获取部门总人数（用于正确计算平均数）
     */
    private Long getActualDeptUserCount(String deptId, String startTime, String endTime, Integer timeType) {
        try {
            // 获取该部门的所有用户（包括子部门用户）
            List<String> deptUserIds = getDeptUserIds(deptId);
            if (deptUserIds.isEmpty()) {
                return 0L;
            }

            // 使用部门总人数作为分母
            Long deptTotalUserCount = (long) deptUserIds.size();

            log.info("getActualDeptUserCount - deptId: {}, timeType: {}, deptUserIds size: {}, deptTotalUserCount: {}",
                    deptId, timeType, deptUserIds.size(), deptTotalUserCount);

            return deptTotalUserCount > 0 ? deptTotalUserCount : 1L; // 避免除零错误
        } catch (Exception e) {
            log.error("Error getting actual user count for deptId: {}, timeType: {}", deptId, timeType, e);
            return 1L; // 默认返回1，避免除零错误
        }
    }

    /**
     * 获取部门在整个时间范围内所有使用过智能体的用户数（作为人均计算的统一分母）
     */
    private Long getTotalUsersInTimeRange(List<String> userIds, String startTime, String endTime, Integer timeType) {
        try {
            if (userIds.isEmpty()) {
                log.warn("getTotalUsersInTimeRange - userIds is empty");
                return 0L;
            }

            log.debug("getTotalUsersInTimeRange - userIds size: {}, startTime: {}, endTime: {}, timeType: {}",
                    userIds.size(), startTime, endTime, timeType);

            Long totalUsersInRange = 0L;

            // 根据时间类型查询对应表在时间范围内的所有用户数
            switch (timeType) {
                case 1: // 天
                    totalUsersInRange = intelligenceStatDayMapper.countUsersInTimeRange(userIds, startTime, endTime);
                    break;
                case 2: // 周
                    totalUsersInRange = intelligenceStatWeekMapper.countUsersInTimeRange(userIds, startTime, endTime);
                    break;
                case 3: // 月
                    totalUsersInRange = intelligenceStatMonthMapper.countUsersInTimeRange(userIds, startTime, endTime);
                    break;
                case 4: // 年
                    totalUsersInRange = intelligenceStatYearMapper.countUsersInTimeRange(userIds, startTime, endTime);
                    break;
                default:
                    totalUsersInRange = intelligenceStatDayMapper.countUsersInTimeRange(userIds, startTime, endTime);
                    break;
            }

            log.debug("getTotalUsersInTimeRange - query result: {}", totalUsersInRange);

            return totalUsersInRange > 0 ? totalUsersInRange : 1L; // 避免除零错误
        } catch (Exception e) {
            log.error("Error getting total users in time range for userIds size: {}, startTime: {}, endTime: {}",
                    userIds.size(), startTime, endTime, e);
            return 1L; // 默认返回1，避免除零错误
        }
    }


    /**
     * 根据部门ID获取部门名称
     */
    private String getDepartmentName(String deptId) {
        try {
            if (StringUtils.isBlank(deptId)) {
                return "未知部门";
            }
            AuthDeptPo deptPo = deptMapper.selectByDeptId(deptId);
            return deptPo != null ? deptPo.getDeptName() : "未知部门";
        } catch (Exception e) {
            log.error("Error getting department name for deptId: {}", deptId, e);
            return "未知部门";
        }
    }

    /**
     * 获取智能体时间序列数据
     */
    private List<Map<String, Object>> getIntelligenceTimeSeriesData(List<String> userIds, String startTime, String endTime, Integer timeType) {
        switch (timeType) {
            case 1: // 天
                return intelligenceStatDayMapper.selectTimeSeriesDataByUserListAndTimeRange(userIds, startTime, endTime);
            case 2: // 周
                return intelligenceStatWeekMapper.selectTimeSeriesDataByUserListAndTimeRange(userIds, startTime, endTime);
            case 3: // 月
                return intelligenceStatMonthMapper.selectTimeSeriesDataByUserListAndTimeRange(userIds, startTime, endTime);
            case 4: // 年
                return intelligenceStatYearMapper.selectTimeSeriesDataByUserListAndTimeRange(userIds, startTime, endTime);
            default:
                return intelligenceStatDayMapper.selectTimeSeriesDataByUserListAndTimeRange(userIds, startTime, endTime);
        }
    }

    /**
     * 设置智能体用户统计数据（需要分页）
     */
    private void setIntelligenceUserStatData(IntelligenceStatisticVo result, List<String> userIds, String startTime, String endTime,
                                           Integer timeType, String pageNum, String pageSize) {
        List<Map<String, Object>> userStatData = getIntelligenceUserStatData(userIds, startTime, endTime, timeType);
        log.info("userStatData size: {}, data: {}", userStatData.size(), JSON.toJSONString(userStatData));

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, timeType);
        log.info("Generated complete time range: {}", completeTimeRange);

        // 按用户ID分组并汇总数据
        Map<String, IntelligenceStatisticVo.UserStatDetail> userStatMap = new HashMap<>();

        for (Map<String, Object> data : userStatData) {
            String userId = (String) data.get("userid");
            String day = (String) data.get("day");
            Long num = getLongValue(data, "num");

            IntelligenceStatisticVo.UserStatDetail userStat = userStatMap.computeIfAbsent(userId, k -> {
                IntelligenceStatisticVo.UserStatDetail stat = new IntelligenceStatisticVo.UserStatDetail();
                // 查询用户名称
                try {
                    UserVo userVo = userService.queryUserById(userId);
                    String userName = (userVo != null && userVo.getName() != null) ? userVo.getName() : userId;
                    stat.setUserName(userName);
                } catch (Exception e) {
                    log.warn("Failed to get user name for userId: {}, using userId as name", userId, e);
                    stat.setUserName(userId);
                }
                stat.setNum(0L);
                // 初始化完整的时间序列数据，所有时间点都设为0
                stat.setTimeSeriesData(new LinkedHashMap<>());
                for (String timePoint : completeTimeRange) {
                    stat.getTimeSeriesData().put(timePoint, 0L);
                }
                return stat;
            });

            // 累加总数
            userStat.setNum(userStat.getNum() + num);

            // 更新时间序列数据
            String formattedTime = SearchStatTimeUtils.formatTimeDisplay(day, timeType);
            userStat.getTimeSeriesData().merge(formattedTime, num, Long::sum);
        }

        // 转换为列表并排序
        List<IntelligenceStatisticVo.UserStatDetail> userStatList = new ArrayList<>(userStatMap.values());
        userStatList.sort((a, b) -> Long.compare(b.getNum(), a.getNum())); // 按使用次数降序排列

        // 设置分页前总数据量
        result.setTotalSize(userStatList.size());

        // 分页处理
        List<IntelligenceStatisticVo.UserStatDetail> pagedUserStatList = paginateUserStatList(userStatList, pageNum, pageSize);
        result.setUserStatList(pagedUserStatList);
    }

    /**
     * 获取智能体用户统计数据
     */
    private List<Map<String, Object>> getIntelligenceUserStatData(List<String> userIds, String startTime, String endTime, Integer timeType) {
        switch (timeType) {
            case 1: // 天
                return intelligenceStatDayMapper.selectUserStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 2: // 周
                return intelligenceStatWeekMapper.selectUserStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 3: // 月
                return intelligenceStatMonthMapper.selectUserStatByUserListAndTimeRange(userIds, startTime, endTime);
            case 4: // 年
                return intelligenceStatYearMapper.selectUserStatByUserListAndTimeRange(userIds, startTime, endTime);
            default:
                return intelligenceStatDayMapper.selectUserStatByUserListAndTimeRange(userIds, startTime, endTime);
        }
    }

    /**
     * 分页处理用户统计列表
     */
    private List<IntelligenceStatisticVo.UserStatDetail> paginateUserStatList(List<IntelligenceStatisticVo.UserStatDetail> userStatList, String pageNum, String pageSize) {
        try {
            int pageNumInt = Integer.parseInt(pageNum);
            int pageSizeInt = Integer.parseInt(pageSize);

            int fromIndex = (pageNumInt - 1) * pageSizeInt;
            int toIndex = Math.min(fromIndex + pageSizeInt, userStatList.size());

            if (fromIndex >= userStatList.size()) {
                return new ArrayList<>();
            }

            return userStatList.subList(fromIndex, toIndex);
        } catch (NumberFormatException e) {
            log.error("Invalid pagination parameters, pageNum: {}, pageSize: {}", pageNum, pageSize, e);
            return userStatList.subList(0, Math.min(10, userStatList.size())); // 默认返回前10条
        }
    }

    /**
     * 获取智能体用户统计数据列表用于导出（复用setIntelligenceUserStatData的逻辑）
     */
    private List<IntelligenceStatisticVo.UserStatDetail> getIntelligenceUserStatListForExport(List<String> userIds, String startTime, String endTime,
                                                                                             Integer timeType, String pageNum, String pageSize) {
        List<Map<String, Object>> userStatData = getIntelligenceUserStatData(userIds, startTime, endTime, timeType);
        log.info("getIntelligenceUserStatListForExport - userStatData size: {}", userStatData.size());

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, timeType);

        // 按用户ID分组并汇总数据
        Map<String, IntelligenceStatisticVo.UserStatDetail> userStatMap = new HashMap<>();

        for (Map<String, Object> data : userStatData) {
            String userId = (String) data.get("userid");
            String day = (String) data.get("day");
            Long num = getLongValue(data, "num");

            IntelligenceStatisticVo.UserStatDetail userStat = userStatMap.computeIfAbsent(userId, k -> {
                IntelligenceStatisticVo.UserStatDetail stat = new IntelligenceStatisticVo.UserStatDetail();
                // 查询用户名称 - 修复：将用户名获取逻辑移到computeIfAbsent内部，与搜索统计保持一致
                try {
                    UserVo userVo = userService.queryUserById(userId);
                    String userName = (userVo != null && userVo.getName() != null) ? userVo.getName() : userId;
                    stat.setUserName(userName);
                } catch (Exception e) {
                    log.warn("Failed to get user name for userId: {}, using userId as name", userId, e);
                    stat.setUserName(userId);
                }
                stat.setNum(0L);
                stat.setTimeSeriesData(new LinkedHashMap<>());
                // 初始化时间序列数据，确保所有时间点都有值
                for (String timePoint : completeTimeRange) {
                    stat.getTimeSeriesData().put(timePoint, 0L);
                }
                return stat;
            });

            // 累加总数
            userStat.setNum(userStat.getNum() + num);

            // 更新时间序列数据
            String formattedTime = SearchStatTimeUtils.formatTimeDisplay(day, timeType);
            userStat.getTimeSeriesData().merge(formattedTime, num, Long::sum);
        }

        // 转换为列表并按使用次数排序
        List<IntelligenceStatisticVo.UserStatDetail> userStatList = new ArrayList<>(userStatMap.values());
        userStatList.sort((a, b) -> Long.compare(b.getNum(), a.getNum()));

        // 分页处理
        return paginateUserStatList(userStatList, pageNum, pageSize);
    }

    /**
     * 导出智能体用户统计数据到Excel
     */
    private void exportIntelligenceUserStatListToExcel(List<IntelligenceStatisticVo.UserStatDetail> userStatList,
                                                      List<String> completeTimeRange, Integer timeType) {
        HttpServletResponse response = getHttpServletResponse();
        if (response == null) {
            log.error("HttpServletResponse is null, cannot export Excel");
            return;
        }

        // 设置响应头
        response.reset();
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        String fileName = "用户智能体统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";

        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            try (ServletOutputStream outputStream = response.getOutputStream();
                 ExcelWriter writer = EasyExcel.write(outputStream)
                         .excelType(ExcelTypeEnum.XLS)
                         .charset(StandardCharsets.UTF_8)
                         .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                         .build()) {

                // 构建表头
                List<List<String>> headers = buildIntelligenceExcelHeaders(completeTimeRange);

                // 构建数据
                List<List<Object>> data = buildIntelligenceExcelData(userStatList, completeTimeRange);

                WriteSheet sheet = EasyExcel.writerSheet("User Intelligence Statistics")
                        .head(headers)
                        .build();
                writer.write(data, sheet);
            }
        } catch (IOException e) {
            log.error("Export Excel failed", e);
            throw new RuntimeException("Export Excel failed", e);
        }
    }

    /**
     * 构建智能体Excel表头
     */
    private List<List<String>> buildIntelligenceExcelHeaders(List<String> completeTimeRange) {
        List<List<String>> headers = new ArrayList<>();

        // 添加基础列
        headers.add(Collections.singletonList("用户"));
        headers.add(Collections.singletonList("总次数"));

        // 添加时间序列列
        for (String timePoint : completeTimeRange) {
            headers.add(Collections.singletonList(timePoint));
        }

        return headers;
    }

    /**
     * 构建智能体Excel数据
     */
    private List<List<Object>> buildIntelligenceExcelData(List<IntelligenceStatisticVo.UserStatDetail> userStatList,
                                                        List<String> completeTimeRange) {
        List<List<Object>> data = new ArrayList<>();

        for (IntelligenceStatisticVo.UserStatDetail userStat : userStatList) {
            List<Object> row = new ArrayList<>();

            // 添加基础数据
            row.add(userStat.getUserName());
            row.add(userStat.getNum());

            // 添加时间序列数据
            for (String timePoint : completeTimeRange) {
                Long count = userStat.getTimeSeriesData().getOrDefault(timePoint, 0L);
                row.add(count);
            }

            data.add(row);
        }

        return data;
    }

    /**
     * 获取HttpServletResponse
     */
    private HttpServletResponse getHttpServletResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getResponse();
        }
        return null;
    }
}
