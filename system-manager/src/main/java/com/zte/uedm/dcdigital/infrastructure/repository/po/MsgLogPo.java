package com.zte.uedm.dcdigital.infrastructure.repository.po;

/* Started by AICoder, pid:n4b7ducc6ex981a143c80bac20d4ad48bfb7d301 */
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MsgLogPo 类表示消息日志的持久化对象。
 *
 * <AUTHOR>
 */
@Data
public class MsgLogPo {

    /**
     * logId 属性表示消息日志的唯一标识符。
     */
    private String logId;

    /**
     * msgName 属性表示消息的名称或标题。
     */
    private String msgName;

    /**
     * msgContent 属性表示消息的内容。
     */
    private String msgContent;

    /**
     * msgType 属性表示消息的类型（例如：1 - 任务，2 - 与我有关，其他 - 其他）。
     */
    private Integer msgType;

    /**
     * notifyType 属性表示通知的类型（例如：1 - 站内通知，2 - 邮件等）。
     */
    private Integer notifyType;

    /**
     * notifyUsers 属性表示接收通知的用户列表，以逗号分隔。
     */
    private String notifyUsers;

    /**
     * createTime 属性表示消息的创建时间。
     */
    private LocalDateTime createTime;

    private String link;
}
/* Ended by AICoder, pid:n4b7ducc6ex981a143c80bac20d4ad48bfb7d301 */

