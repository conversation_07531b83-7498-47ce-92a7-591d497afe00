/* Started by AICoder, pid:ida0dmd11aae7d314ff409a892ca51931ca682cc */
package com.zte.uedm.dcdigital.domain.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索统计时间处理工具类。
 * 提供时间格式转换和时间范围生成功能。
 */
@Slf4j
public class StatTimeUtils {

    /**
     * 格式化天显示：20250620 -> 2025-06-20。
     *
     * @param dayValue 天值（格式：yyyyMMdd）
     * @return 格式化后的天显示字符串
     */
    public static String formatDayDisplay(String dayValue) {
        if (dayValue.length() == 8) {
            String year = dayValue.substring(0, 4);
            String month = dayValue.substring(4, 6);
            String day = dayValue.substring(6, 8);
            return year + "年" + month + "月" + day + "日";
        }
        return dayValue;
    }

    /**
     * 格式化周显示：202530 -> 2025年30周。
     *
     * @param weekValue 周值（格式：yyyyww）
     * @return 格式化后的周显示字符串
     */
    public static String formatWeekDisplay(String weekValue) {
        if (weekValue.length() == 6) {
            String year = weekValue.substring(0, 4);
            String week = weekValue.substring(4, 6);
            // 去掉前导零
            int weekNum = Integer.parseInt(week);
            return year + "年" + weekNum + "周";
        }
        return weekValue;
    }

    /**
     * 格式化月显示：202506 -> 2025年06月。
     *
     * @param monthValue 月值（格式：yyyyMM）
     * @return 格式化后的月显示字符串
     */
    public static String formatMonthDisplay(String monthValue) {
        if (monthValue.length() == 6) {
            String year = monthValue.substring(0, 4);
            String month = monthValue.substring(4, 6);
            return year + "年" + month + "月";
        }
        return monthValue;
    }

    /**
     * 格式化年显示：2025 -> 2025年。
     *
     * @param yearValue 年值（格式：yyyy）
     * @return 格式化后的年显示字符串
     */
    public static String formatYeayDisplay(String yearValue) {
        return yearValue + "年";
    }

    /**
     * 根据时间类型格式化时间显示
     *
     * @param timeValue 时间值
     * @param timeType  时间类型：1-天，2-周，3-月，4-年
     * @return 格式化后的时间显示字符串
     */
    public static String formatTimeDisplay(String timeValue, Integer timeType) {
        if (timeValue == null || timeType == null) {
            return timeValue;
        }

        switch (timeType) {
            case 1: // 天
                return formatDayDisplay(timeValue);
            case 2: // 周
                return formatWeekDisplay(timeValue);
            case 3: // 月
                return formatMonthDisplay(timeValue);
            case 4: // 年
                return formatYeayDisplay(timeValue);
            default:
                return timeValue;
        }
    }

    /**
     * 将时间范围转换为数据库查询格式。
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param timeType  时间类型：1-天，2-周，3-月，4-年
     * @return 转换后的时间范围数组 [startTime, endTime]
     */
    public static String[] convertTimeRangeForQuery(String startTime, String endTime, Integer timeType) {
        try {
            switch (timeType) {
                case 1: // 天
                    return convertDayRange(startTime, endTime);
                case 2: // 周
                    return convertWeekRange(startTime, endTime);
                case 3: // 月
                    return convertMonthRange(startTime, endTime);
                case 4: // 年
                    return convertYearRange(startTime, endTime);
                default:
                    return new String[]{startTime, endTime};
            }
        } catch (Exception e) {
            log.error("Convert time range error, startTime: {}, endTime: {}, timeType: {}",
                    startTime, endTime, timeType, e);
            return new String[]{startTime, endTime};
        }
    }

    /**
     * 转换天范围：2025-06-20 -> 20250620。
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd）
     * @param endTime   结束时间（格式：yyyy-MM-dd）
     * @return 转换后的时间范围数组 [startTime, endTime]
     */
    private static String[] convertDayRange(String startTime, String endTime) {
        String start = startTime.replace("-", "");
        String end = endTime.replace("-", "");
        return new String[]{start, end};
    }

    /**
     * 转换周范围：202506-202510 -> [202506, 202510]。
     *
     * @param startTime 开始时间（格式：yyyyww）
     * @param endTime   结束时间（格式：yyyyww）
     * @return 转换后的时间范围数组 [startTime, endTime]
     */
    private static String[] convertWeekRange(String startTime, String endTime) {
        // 如果是范围格式：202506-202510
        if (startTime.contains("-") && startTime.length() > 6) {
            String[] parts = startTime.split("-");
            return new String[]{parts[0], parts[1]};
        }
        // 如果是单独的周格式
        return new String[]{startTime, endTime};
    }

    /**
     * 转换月范围：2025-06 -> 202506。
     *
     * @param startTime 开始时间（格式：yyyy-MM）
     * @param endTime   结束时间（格式：yyyy-MM）
     * @return 转换后的时间范围数组 [startTime, endTime]
     */
    private static String[] convertMonthRange(String startTime, String endTime) {
        String start = startTime.replace("-", "");
        String end = endTime.replace("-", "");
        return new String[]{start, end};
    }

    /**
     * 转换年范围：2025 -> 2025。
     *
     * @param startTime 开始时间（格式：yyyy）
     * @param endTime   结束时间（格式：yyyy）
     * @return 转换后的时间范围数组 [startTime, endTime]
     */
    private static String[] convertYearRange(String startTime, String endTime) {
        return new String[]{startTime, endTime};
    }

    /**
     * 生成完整的时间范围列表，包含范围内的每一个时间点。
     *
     * @param startTime 开始时间（数据库格式）
     * @param endTime   结束时间（数据库格式）
     * @param timeType  时间类型：1-天，2-周，3-月，4-年
     * @return 完整的时间范围列表（格式化后的显示格式）
     */
    public static List<String> generateCompleteTimeRange(String startTime, String endTime, Integer timeType) {
        List<String> timeRange = new ArrayList<>();

        try {
            switch (timeType) {
                case 1: // 天
                    timeRange = generateDayRange(startTime, endTime);
                    break;
                case 2: // 周
                    timeRange = generateWeekRange(startTime, endTime);
                    break;
                case 3: // 月
                    timeRange = generateMonthRange(startTime, endTime);
                    break;
                case 4: // 年
                    timeRange = generateYearRange(startTime, endTime);
                    break;
                default:
                    log.warn("Unsupported time type: {}", timeType);
                    break;
            }
        } catch (Exception e) {
            log.error("Generate complete time range error, startTime: {}, endTime: {}, timeType: {}",
                    startTime, endTime, timeType, e);
        }

        return timeRange;
    }

    /**
     * 生成天范围：20250620 到 20250625 -> [2025年06月20日, 2025年06月21日, ...]。
     *
     * @param startTime 开始时间（格式：yyyyMMdd）
     * @param endTime   结束时间（格式：yyyyMMdd）
     * @return 天范围列表
     */
    private static List<String> generateDayRange(String startTime, String endTime) {
        List<String> dayRange = new ArrayList<>();

        LocalDate start = LocalDate.parse(startTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate end = LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyyMMdd"));

        LocalDate current = start;
        while (!current.isAfter(end)) {
            String dayStr = current.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            dayRange.add(dayStr);
            current = current.plusDays(1);
        }

        return dayRange;
    }

    /**
     * 生成周范围：202506 到 202510 -> [2025年6周, 2025年7周, ...]。
     *
     * @param startTime 开始时间（格式：yyyyww）
     * @param endTime   结束时间（格式：yyyyww）
     * @return 周范围列表
     */
    private static List<String> generateWeekRange(String startTime, String endTime) {
        List<String> weekRange = new ArrayList<>();

        int startYear = Integer.parseInt(startTime.substring(0, 4));
        int startWeek = Integer.parseInt(startTime.substring(4, 6));
        int endYear = Integer.parseInt(endTime.substring(0, 4));
        int endWeek = Integer.parseInt(endTime.substring(4, 6));

        int currentYear = startYear;
        int currentWeek = startWeek;

        while (currentYear < endYear || (currentYear == endYear && currentWeek <= endWeek)) {
            String weekStr = String.format("%d%02d", currentYear, currentWeek);
            weekRange.add(weekStr);

            currentWeek++;
            // 检查是否需要进入下一年，使用更准确的方法计算每年的周数
            LocalDate yearEnd = LocalDate.of(currentYear, 12, 31);
            int weeksInYear = yearEnd.get(WeekFields.ISO.weekOfYear());
            if (currentWeek > weeksInYear) {
                currentYear++;
                currentWeek = 1;
            }
        }

        return weekRange;
    }

    /**
     * 生成月范围：202506 到 202510 -> [2025年06月, 2025年07月, ...]。
     *
     * @param startTime 开始时间（格式：yyyyMM）
     * @param endTime   结束时间（格式：yyyyMM）
     * @return 月范围列表
     */
    private static List<String> generateMonthRange(String startTime, String endTime) {
        List<String> monthRange = new ArrayList<>();

        LocalDate start = LocalDate.parse(startTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate end = LocalDate.parse(endTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

        LocalDate current = start;
        while (!current.isAfter(end)) {
            String monthStr = current.format(DateTimeFormatter.ofPattern("yyyyMM"));
            monthRange.add(monthStr);
            current = current.plusMonths(1);
        }

        return monthRange;
    }

    /**
     * 生成年范围：2023 到 2025 -> [2023年, 2024年, 2025年]。
     *
     * @param startTime 开始时间（格式：yyyy）
     * @param endTime   结束时间（格式：yyyy）
     * @return 年范围列表
     */
    private static List<String> generateYearRange(String startTime, String endTime) {
        List<String> yearRange = new ArrayList<>();

        int startYear = Integer.parseInt(startTime);
        int endYear = Integer.parseInt(endTime);

        for (int year = startYear; year <= endYear; year++) {
            yearRange.add(String.valueOf(year));
        }

        return yearRange;
    }
}
/* Ended by AICoder, pid:ida0dmd11aae7d314ff409a892ca51931ca682cc */