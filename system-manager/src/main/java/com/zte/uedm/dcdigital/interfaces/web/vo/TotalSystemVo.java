/* Started by AICoder, pid:dbb1b7610fl5b9f146180a7d405ab2005901b3f2 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.domain.common.enums.StatTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TotalSystemVo {
    private int totalBrand;
    private int avgBrand;
    private int totalMaterial;
    private int avgMaterial;
    private int totalFile;
    private int avgFile;
    private int totalFaq;
    private int avgFaq;
    private String timePeriod;
    public TotalSystemVo(int totalBrand, int avgBrand, int totalMaterial,
                         int avgMaterial, int totalFile, int avgFile,
                         int totalFaq, int avgFaq) {
        this.totalBrand = totalBrand;
        this.avgBrand = avgBrand;
        this.totalMaterial = totalMaterial;
        this.avgMaterial = avgMaterial;
        this.totalFile = totalFile;
        this.avgFile = avgFile;
        this.totalFaq = totalFaq;
        this.avgFaq = avgFaq;
    }
}
/* Ended by AICoder, pid:dbb1b7610fl5b9f146180a7d405ab2005901b3f2 */