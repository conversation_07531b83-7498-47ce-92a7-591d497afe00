package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetUpdEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.service.DocAssetDomainService;
import com.zte.uedm.dcdigital.domain.utils.DocAssetTimeUtils;
import com.zte.uedm.dcdigital.domain.utils.ParseUtils;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetJuniorQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocAssetJuniorNumVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocAssetNumVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocDownStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocDownUserStatisticsVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;
@Service
@Slf4j
public class DocAssetDomainServiceImpl implements DocAssetDomainService {
    @Autowired
    private DocumentService documentService;
    @Autowired
    private ProductService productService;
    @Autowired
    private DocAssetDayRepository docAssetDayRepository;
    @Autowired
    private DocAssetWeekRepository docAssetWeekRepository;
    @Autowired
    private DocAssetMonthRepository docAssetMonthRepository;
    @Autowired
    private DocAssetYearRepository docAssetYearRepository;
    @Autowired
    private DocAssetRepository docAssetRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronizeDocAssetDayData() {
        //获取node_type为3的小类id列表
        List<String> productSubcategoryId = productService.getProductSubcategoryId();
        //同步日表数据
        insertDayInfo(productSubcategoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronizeDocAssetData() {
        //获取node_type为3的小类id列表
        List<String> productSubcategoryId = productService.getProductSubcategoryId();

        //同步周表数据
        insertOrUpdateWeekInfo(productSubcategoryId);
        //同步月表数据
        insertOrUpdateMonthInfo(productSubcategoryId);
        //同步年表数据
        insertOrUpdateYearInfo(productSubcategoryId);
    }

    /* Started by AICoder, pid:056f42c0c53ca8b145a20951103f4c828d0901d4 */
    /* Started by AICoder, pid:af50fr154cv3965143ee08e780fb539c3bd7a0f2 */
    public void insertDayInfo(List<String> productSubcategoryId){
        //获取当天的日期
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        // 获取现在时间
        LocalDateTime nowTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startOfDayTime = nowTime.format(formatter);
        //获取前一天的日期
        Integer previousDayNumber = StatCurrentDateUtils.getPreviousDayNumber();
        //当天日期
        List<DocAssetEntity> dataList = docAssetDayRepository.getDataByTime(currentDayNumber);
        List<String> idList = dataList.stream().map(DocAssetEntity::getProductCategoryId).collect(Collectors.toList());
        if(!dataList.isEmpty()){
            //表中已有当天数据
            for (DocAssetEntity entity : dataList) {
                //更新总文档数
                if(entity.getProductCategoryId() != null){
                    Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, entity.getProductCategoryId());
                    entity.setAllNum(num);
                }else {
                    Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
                    entity.setAllNum(num1);
                }
                //更新变化数
                DocAssetEntity docAssetEntity = docAssetDayRepository.getBeforeDayEntity(previousDayNumber, entity.getProductCategoryId());
                if(docAssetEntity != null){
                    entity.setChangeNum(entity.getAllNum() - docAssetEntity.getAllNum());
                }else {
                    entity.setChangeNum(entity.getAllNum());
                }
                //获取当天更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(currentDayNumber, currentDayNumber, entity.getProductCategoryId());
                entity.setUpdateNum(updateNum);
            }
            docAssetDayRepository.batchUpdate(dataList);
            //如果有新增的产品小类
            insertNewProductCategoryDay(productSubcategoryId, idList, currentDayNumber, startOfDayTime);
        }else {
            List<DocAssetEntity> dayEntity = new ArrayList<>();
            //文档存在属于产品小类的情况
            for (String id : productSubcategoryId) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(currentDayNumber);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                //根据前一天时间及所属id获取总文档数量
                DocAssetEntity entity = docAssetDayRepository.getBeforeDayEntity(previousDayNumber, id);
                if(entity != null){
                    newEntity.setChangeNum(num - entity.getAllNum());
                }else {
                    newEntity.setChangeNum(num);
                }
                //获取当天更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(currentDayNumber, currentDayNumber, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                dayEntity.add(newEntity);
            }
            DocAssetEntity newEntity1 = new DocAssetEntity();
            newEntity1.setId(UUID.randomUUID().toString());
            newEntity1.setDay(currentDayNumber);
            Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
            newEntity1.setAllNum(num1);
            DocAssetEntity entity = docAssetDayRepository.getBeforeDayEntity(previousDayNumber, null);
            if(entity != null){
                newEntity1.setChangeNum(num1 - entity.getAllNum());
            }else {
                newEntity1.setChangeNum(num1);
            }
            Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(currentDayNumber, currentDayNumber, null);
            newEntity1.setUpdateNum(updateNum);
            newEntity1.setCreateTime(DateTimeUtils.getCurrentTime());
            dayEntity.add(newEntity1);
            docAssetDayRepository.batchInsert(dayEntity);
        }
    }
    /* Ended by AICoder, pid:af50fr154cv3965143ee08e780fb539c3bd7a0f2 */
    /* Ended by AICoder, pid:056f42c0c53ca8b145a20951103f4c828d0901d4 */
    /* Started by AICoder, pid:jd041216a1bb95614d65084000abb038fea7290a */
    public void insertNewProductCategoryDay(List<String> productSubcategoryId, List<String> idList, Integer currentDayNumber, String startOfDayTime){
        List<String> difference = productSubcategoryId.stream()
                .filter(element -> !idList.contains(element))
                .collect(Collectors.toList());
        if(!difference.isEmpty()){
            List<DocAssetEntity> dayEntity = new ArrayList<>();
            for (String id : difference) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(currentDayNumber);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                newEntity.setChangeNum(num);
                //获取当天更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(currentDayNumber, currentDayNumber, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                dayEntity.add(newEntity);
            }
            docAssetDayRepository.batchInsert(dayEntity);
        }
    }
    /* Ended by AICoder, pid:jd041216a1bb95614d65084000abb038fea7290a */
    /* Started by AICoder, pid:vbb75u7788k6ae414dba0b1391751c0234d9a6ba */
    public void insertOrUpdateWeekInfo(List<String> productSubcategoryId){
        // 获取当天的 00:00:00
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startOfDayTime = startOfDay.format(formatter);
        //“昨天”是当年的第几周
        Integer previousWeekOfYear = StatCurrentDateUtils.getPreviousWeekOfYear();
        //获得当前周的前一周
        int previousWeek = DocAssetTimeUtils.getPreviousWeek(previousWeekOfYear);
        //获得当前周的周一和周日
        int mondayOfWeek = DocAssetTimeUtils.getMondayOfWeek(previousWeekOfYear);
        int sundayOfWeek = DocAssetTimeUtils.getSundayOfWeek(previousWeekOfYear);

        List<DocAssetEntity> dataList = docAssetWeekRepository.getDataByTime(previousWeekOfYear);
        List<String> idList = dataList.stream().map(DocAssetEntity::getProductCategoryId).collect(Collectors.toList());
        if(!dataList.isEmpty()){
            //不为空说明需要更新
            for (DocAssetEntity entity : dataList) {
                //更新总文档数
                if(entity.getProductCategoryId() != null){
                    Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, entity.getProductCategoryId());
                    entity.setAllNum(num);
                }else {
                    Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
                    entity.setAllNum(num1);
                }
                //更新变化数
                DocAssetEntity docAssetEntity = docAssetWeekRepository.getBeforeWeekEntity(previousWeek, entity.getProductCategoryId());
                if(docAssetEntity != null){
                    entity.setChangeNum(entity.getAllNum() - docAssetEntity.getAllNum());
                }else {
                    entity.setChangeNum(entity.getAllNum());
                }
                //获取当周更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(mondayOfWeek, sundayOfWeek, entity.getProductCategoryId());
                entity.setUpdateNum(updateNum);
            }
            docAssetWeekRepository.batchUpdate(dataList);
            //如果有新增的产品小类的情况
            insertNewProductCategoryWeek(productSubcategoryId, idList, previousWeekOfYear, startOfDayTime, mondayOfWeek, sundayOfWeek);
        }else {
            //list为空说明需要新增插入
            List<DocAssetEntity> weekEntity = new ArrayList<>();
            //文档存在属于产品小类的情况
            for (String id : productSubcategoryId) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousWeekOfYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                //变化数
                DocAssetEntity entity = docAssetWeekRepository.getBeforeWeekEntity(previousWeek, id);
                if(entity != null){
                    newEntity.setChangeNum(num - entity.getAllNum());
                }else {
                    newEntity.setChangeNum(num);
                }
                //获取当周更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(mondayOfWeek, sundayOfWeek, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                weekEntity.add(newEntity);
            }
            DocAssetEntity newEntity1 = new DocAssetEntity();
            newEntity1.setId(UUID.randomUUID().toString());
            newEntity1.setDay(previousWeekOfYear);
            Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
            newEntity1.setAllNum(num1);
            DocAssetEntity entity = docAssetWeekRepository.getBeforeWeekEntity(previousWeek, null);
            if(entity != null){
                newEntity1.setChangeNum(num1 - entity.getAllNum());
            }else {
                newEntity1.setChangeNum(num1);
            }
            Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(mondayOfWeek, sundayOfWeek, null);
            newEntity1.setUpdateNum(updateNum);
            newEntity1.setCreateTime(DateTimeUtils.getCurrentTime());
            weekEntity.add(newEntity1);
            docAssetWeekRepository.batchInsert(weekEntity);
        }
    }
    /* Ended by AICoder, pid:vbb75u7788k6ae414dba0b1391751c0234d9a6ba */
    /* Started by AICoder, pid:laade73474i205c140e80a00402a7f42f06565c7 */
    public void insertNewProductCategoryWeek(List<String> productSubcategoryId, List<String> idList, Integer previousWeekOfYear, String startOfDayTime, int mondayOfWeek, int sundayOfWeek){
        List<String> difference = productSubcategoryId.stream()
                .filter(element -> !idList.contains(element))
                .collect(Collectors.toList());
        if(!difference.isEmpty()){
            List<DocAssetEntity> dayEntity = new ArrayList<>();
            for (String id : difference) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousWeekOfYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                newEntity.setChangeNum(num);
                //获取当周更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(mondayOfWeek, sundayOfWeek, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                dayEntity.add(newEntity);
            }
            docAssetWeekRepository.batchInsert(dayEntity);
        }
    }
    /* Ended by AICoder, pid:laade73474i205c140e80a00402a7f42f06565c7 */
    /* Started by AICoder, pid:w17bffba4a650b3141410994708d6a913e58ca5c */
    public void insertOrUpdateMonthInfo(List<String> productSubcategoryId){
        // 获取当天的 00:00:00
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startOfDayTime = startOfDay.format(formatter);
        //“昨天”是当年的第几个月
        Integer previousMonthOfYear = StatCurrentDateUtils.getPreviousMonthOfYear();
        //获得当前月的前一月
        int previousMonth = DocAssetTimeUtils.getPreviousMonth(previousMonthOfYear);
        //获得当前月的月初和月末
        int firstDayOfMonth = DocAssetTimeUtils.getFirstDayOfMonth(previousMonthOfYear);
        int lastDayOfMonth = DocAssetTimeUtils.getLastDayOfMonth(previousMonthOfYear);

        List<DocAssetEntity> dataList = docAssetMonthRepository.getDataByTime(previousMonthOfYear);
        List<String> idList = dataList.stream().map(DocAssetEntity::getProductCategoryId).collect(Collectors.toList());
        if(!dataList.isEmpty()){
            //不为空说明需要更新
            for (DocAssetEntity entity : dataList) {
                //更新总文档数
                if(entity.getProductCategoryId() != null){
                    Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, entity.getProductCategoryId());
                    entity.setAllNum(num);
                }else {
                    Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
                    entity.setAllNum(num1);
                }
                //更新变化数
                DocAssetEntity docAssetEntity = docAssetMonthRepository.getBeforeMonthEntity(previousMonth, entity.getProductCategoryId());
                if(docAssetEntity != null){
                    entity.setChangeNum(entity.getAllNum() - docAssetEntity.getAllNum());
                }else {
                    entity.setChangeNum(entity.getAllNum());
                }
                //获取当月更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(firstDayOfMonth, lastDayOfMonth, entity.getProductCategoryId());
                entity.setUpdateNum(updateNum);
            }
            docAssetMonthRepository.batchUpdate(dataList);
            //如果有新增的产品小类的情况
            insertNewProductCategoryMonth(productSubcategoryId, idList, previousMonthOfYear, startOfDayTime, firstDayOfMonth, lastDayOfMonth);
        }else {
            //list为空说明需要新增插入
            List<DocAssetEntity> monthEntity = new ArrayList<>();
            //文档存在属于产品小类的情况
            for (String id : productSubcategoryId) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousMonthOfYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                //变化数
                DocAssetEntity entity = docAssetMonthRepository.getBeforeMonthEntity(previousMonth, id);
                if(entity != null){
                    newEntity.setChangeNum(num - entity.getAllNum());
                }else {
                    newEntity.setChangeNum(num);
                }
                //获取当月更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(firstDayOfMonth, lastDayOfMonth, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                monthEntity.add(newEntity);
            }
            DocAssetEntity newEntity1 = new DocAssetEntity();
            newEntity1.setId(UUID.randomUUID().toString());
            newEntity1.setDay(previousMonthOfYear);
            Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
            newEntity1.setAllNum(num1);
            DocAssetEntity entity = docAssetMonthRepository.getBeforeMonthEntity(previousMonth, null);
            if(entity != null){
                newEntity1.setChangeNum(num1 - entity.getAllNum());
            }else {
                newEntity1.setChangeNum(num1);
            }
            Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(firstDayOfMonth, lastDayOfMonth, null);
            newEntity1.setUpdateNum(updateNum);
            newEntity1.setCreateTime(DateTimeUtils.getCurrentTime());
            monthEntity.add(newEntity1);
            docAssetMonthRepository.batchInsert(monthEntity);
        }
    }
    /* Ended by AICoder, pid:w17bffba4a650b3141410994708d6a913e58ca5c */
    /* Started by AICoder, pid:q9348h97e9f95b814d2709d9e0515c34b9994a56 */
    public void insertNewProductCategoryMonth(List<String> productSubcategoryId, List<String> idList, Integer previousMonthOfYear, String startOfDayTime, int firstDayOfMonth, int lastDayOfMonth){
        List<String> difference = productSubcategoryId.stream()
                .filter(element -> !idList.contains(element))
                .collect(Collectors.toList());
        if(!difference.isEmpty()){
            List<DocAssetEntity> dayEntity = new ArrayList<>();
            for (String id : difference) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousMonthOfYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                newEntity.setChangeNum(num);
                //获取当周更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(firstDayOfMonth, lastDayOfMonth, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                dayEntity.add(newEntity);
            }
            docAssetMonthRepository.batchInsert(dayEntity);
        }
    }
    /* Ended by AICoder, pid:q9348h97e9f95b814d2709d9e0515c34b9994a56 */
    /* Started by AICoder, pid:kf073le5cchd088141fa092f215fcc10eae13830 */
    public void insertOrUpdateYearInfo(List<String> productSubcategoryId){
        // 获取当天的 00:00:00
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startOfDayTime = startOfDay.format(formatter);
        //“昨天”的年份
        Integer previousYear = StatCurrentDateUtils.getPreviousYear();
        //获得当前年的前一年
        int beforePreviousYear = previousYear -1;
        //获得当前年的年初和年末
        int FirstDayOfYear = Integer.parseInt(LocalDate.of(previousYear, 1, 1).format(DateTimeFormatter.BASIC_ISO_DATE));
        int EndDayOfYear = Integer.parseInt(LocalDate.of(previousYear, 12, 31).format(DateTimeFormatter.BASIC_ISO_DATE));

        List<DocAssetEntity> dataList = docAssetYearRepository.getDataByTime(previousYear);
        List<String> idList = dataList.stream().map(DocAssetEntity::getProductCategoryId).collect(Collectors.toList());
        if(!dataList.isEmpty()){
            //不为空说明需要更新
            for (DocAssetEntity entity : dataList) {
                //更新总文档数
                if(entity.getProductCategoryId() != null){
                    Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, entity.getProductCategoryId());
                    entity.setAllNum(num);
                }else {
                    Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
                    entity.setAllNum(num1);
                }
                //更新变化数
                DocAssetEntity docAssetEntity = docAssetYearRepository.getBeforeYearEntity(beforePreviousYear, entity.getProductCategoryId());
                if(docAssetEntity != null){
                    entity.setChangeNum(entity.getAllNum() - docAssetEntity.getAllNum());
                }else {
                    entity.setChangeNum(entity.getAllNum());
                }
                //获取当年更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(FirstDayOfYear, EndDayOfYear, entity.getProductCategoryId());
                entity.setUpdateNum(updateNum);
            }
            docAssetYearRepository.batchUpdate(dataList);
            //如果有新增的产品小类的情况
            insertNewProductCategoryYear(productSubcategoryId, idList, previousYear, startOfDayTime, FirstDayOfYear, EndDayOfYear);
        }else{
            //list为空说明需要新增插入
            List<DocAssetEntity> yearEntity = new ArrayList<>();
            //文档存在属于产品小类的情况
            for (String id : productSubcategoryId) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                //变化数
                DocAssetEntity entity = docAssetYearRepository.getBeforeYearEntity(beforePreviousYear, id);
                if(entity != null){
                    newEntity.setChangeNum(num - entity.getAllNum());
                }else {
                    newEntity.setChangeNum(num);
                }
                //获取当年更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(FirstDayOfYear, EndDayOfYear, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                yearEntity.add(newEntity);
            }
            DocAssetEntity newEntity1 = new DocAssetEntity();
            newEntity1.setId(UUID.randomUUID().toString());
            newEntity1.setDay(previousYear);
            Integer num1 = documentService.getNumByTimeAndNotInIds(startOfDayTime, productSubcategoryId);
            newEntity1.setAllNum(num1);
            DocAssetEntity entity = docAssetYearRepository.getBeforeYearEntity(beforePreviousYear, null);
            if(entity != null){
                newEntity1.setChangeNum(num1 - entity.getAllNum());
            }else {
                newEntity1.setChangeNum(num1);
            }
            Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(FirstDayOfYear, EndDayOfYear, null);
            newEntity1.setUpdateNum(updateNum);
            newEntity1.setCreateTime(DateTimeUtils.getCurrentTime());
            yearEntity.add(newEntity1);
            docAssetYearRepository.batchInsert(yearEntity);
        }
    }
    /* Ended by AICoder, pid:kf073le5cchd088141fa092f215fcc10eae13830 */
    /* Started by AICoder, pid:743d7nd5f3be88414a0a0b1d20410059e6984301 */
    public void insertNewProductCategoryYear(List<String> productSubcategoryId, List<String> idList, Integer previousYear, String startOfDayTime, int FirstDayOfYear, int EndDayOfYear){
        List<String> difference = productSubcategoryId.stream()
                .filter(element -> !idList.contains(element))
                .collect(Collectors.toList());
        if(!difference.isEmpty()){
            List<DocAssetEntity> dayEntity = new ArrayList<>();
            for (String id : difference) {
                DocAssetEntity newEntity = new DocAssetEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(previousYear);
                newEntity.setProductCategoryId(id);
                //根据当天时间及所属id获取总文档数量
                Integer num = documentService.getNumByCreateTimeAndAscriptionId(startOfDayTime, id);
                newEntity.setAllNum(num);
                newEntity.setChangeNum(num);
                //获取当周更新文档数
                Integer updateNum = docAssetRepository.getDataByDayAndProductCategoryId(FirstDayOfYear, EndDayOfYear, id);
                newEntity.setUpdateNum(updateNum);
                newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
                dayEntity.add(newEntity);
            }
            docAssetYearRepository.batchInsert(dayEntity);
        }
    }
    /* Ended by AICoder, pid:743d7nd5f3be88414a0a0b1d20410059e6984301 */
    @Override
    /* Started by AICoder, pid:vf65dzddd33ed9c148a50853a0a71d4abf562bb7 */
    public void addDocAssetRecord(DocAssetAddDto addDto) {
        // 获取当前日期并转换为整数格式
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        addDto.setDay(currentDayNumber);
        DocAssetUpdEntity dataByDayAndId = docAssetRepository.getDataByDayAndId(addDto);
        //根据文档id和时间查询是否有数据
        if(dataByDayAndId != null){
            return;
        }
        DocAssetUpdEntity newEntity = new DocAssetUpdEntity();
        newEntity.setId(UUID.randomUUID().toString());
        newEntity.setDay(currentDayNumber);
        newEntity.setProductCategoryId(addDto.getProductCategoryId());
        newEntity.setDocumentId(addDto.getDocumentId());
        newEntity.setCreateTime(DateTimeUtils.getCurrentTime());
        docAssetRepository.addRecord(newEntity);
    }
    /* Ended by AICoder, pid:vf65dzddd33ed9c148a50853a0a71d4abf562bb7 */
    @Override
    public DocAssetNumVo getStatDocument(DocAssetQueryDto queryDto) {
        DocAssetNumVo docAssetNumVo = new DocAssetNumVo();
        if(queryDto.getTimeType() == 1){
            docAssetNumVo  = getStatDocumentDay(queryDto);
        }else if(queryDto.getTimeType() == 2){
            docAssetNumVo = getStatDocumentWeek(queryDto);
        }else if(queryDto.getTimeType() == 3){
            docAssetNumVo = getStatDocumentMonth(queryDto);
        }else if(queryDto.getTimeType() == 4){
            docAssetNumVo = getStatDocumentYear(queryDto);
        }
        return docAssetNumVo;
    }
    /* Started by AICoder, pid:m75c1m33begbd5c1464c095b0039d4548f6152b2 */
    public DocAssetNumVo getStatDocumentDay(DocAssetQueryDto queryDto){
        DocAssetNumVo docAssetNumVo = new DocAssetNumVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(queryDto.getStartTime()), ParseUtils.parseStringToInt(queryDto.getEndTime()), queryDto);
        //根据入参的小类id获得下级所有小类id（node_type为3）
        List<String> ids = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        if(list.isEmpty()){
            ids.add(queryDto.getProductCategoryId());
        }else {
            ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        }
        //查询条件
        Integer beginTime = ParseUtils.parseStringToInt(queryDto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(queryDto.getEndTime());
        List<DocAssetEntity> docNumData = docAssetDayRepository.getDocNumData(beginTime, endTime, ids);
        List<Integer> integers = docNumData.stream().map(DocAssetEntity::getDay).collect(Collectors.toList());
        List<DocAssetNumVo.DocAssetNum> dataList = new ArrayList<>();
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setAllNum(0);
            entity.setChangeNum(0);
            entity.setUpdateNum(0);
            entity.setTimePeriod(getDayInfo(missingInteger));
            dataList.add(entity);
        }
        for (DocAssetEntity item : docNumData) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setTimePeriod(getDayInfo(item.getDay()));
            entity.setAllNum(item.getAllNum());
            entity.setChangeNum(item.getChangeNum());
            entity.setUpdateNum(item.getUpdateNum());
            dataList.add(entity);
        }
        dataList = dataList.stream().sorted(Comparator.comparing(
                DocAssetNumVo.DocAssetNum::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docAssetNumVo.setDataList(dataList);
        return docAssetNumVo;
    }
    /* Ended by AICoder, pid:m75c1m33begbd5c1464c095b0039d4548f6152b2 */
    /* Started by AICoder, pid:53a6etbcfcc107a14a690beab012af607e95ceca */
    public DocAssetNumVo getStatDocumentWeek(DocAssetQueryDto queryDto){
        DocAssetNumVo docAssetNumVo = new DocAssetNumVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(queryDto.getStartTime()), ParseUtils.parseStringToInt(queryDto.getEndTime()), queryDto);
        //根据入参的小类id获得下级所有小类id（node_type为3）
        List<String> ids = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        if(list.isEmpty()){
            ids.add(queryDto.getProductCategoryId());
        }else {
            ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        }
        //查询条件
        Integer beginTime = ParseUtils.parseStringToInt(queryDto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(queryDto.getEndTime());
        List<DocAssetEntity> docNumData = docAssetWeekRepository.getDocNumData(beginTime, endTime, ids);
        List<Integer> integers = docNumData.stream().map(DocAssetEntity::getDay).collect(Collectors.toList());
        List<DocAssetNumVo.DocAssetNum> dataList = new ArrayList<>();
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setAllNum(0);
            entity.setChangeNum(0);
            entity.setUpdateNum(0);
            entity.setTimePeriod(getWeekInfo(missingInteger));
            dataList.add(entity);
        }
        for (DocAssetEntity item : docNumData) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setTimePeriod(getWeekInfo(item.getDay()));
            entity.setAllNum(item.getAllNum());
            entity.setChangeNum(item.getChangeNum());
            entity.setUpdateNum(item.getUpdateNum());
            dataList.add(entity);
        }
        dataList = dataList.stream().sorted(Comparator.comparing(
                DocAssetNumVo.DocAssetNum::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docAssetNumVo.setDataList(dataList);
        return docAssetNumVo;
    }
    /* Ended by AICoder, pid:53a6etbcfcc107a14a690beab012af607e95ceca */
    /* Started by AICoder, pid:uc37ad73fd562c01458a083530130b51d2d16001 */
    public DocAssetNumVo getStatDocumentMonth(DocAssetQueryDto queryDto){
        DocAssetNumVo docAssetNumVo = new DocAssetNumVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(queryDto.getStartTime()), ParseUtils.parseStringToInt(queryDto.getEndTime()), queryDto);
        //根据入参的小类id获得下级所有小类id（node_type为3）
        List<String> ids = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        if(list.isEmpty()){
            ids.add(queryDto.getProductCategoryId());
        }else {
            ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        }
        //查询条件
        Integer beginTime = ParseUtils.parseStringToInt(queryDto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(queryDto.getEndTime());
        List<DocAssetEntity> docNumData = docAssetMonthRepository.getDocNumData(beginTime, endTime, ids);
        List<Integer> integers = docNumData.stream().map(DocAssetEntity::getDay).collect(Collectors.toList());
        List<DocAssetNumVo.DocAssetNum> dataList = new ArrayList<>();
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setAllNum(0);
            entity.setChangeNum(0);
            entity.setUpdateNum(0);
            entity.setTimePeriod(getMonthInfo(missingInteger));
            dataList.add(entity);
        }
        for (DocAssetEntity item : docNumData) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setTimePeriod(getMonthInfo(item.getDay()));
            entity.setAllNum(item.getAllNum());
            entity.setChangeNum(item.getChangeNum());
            entity.setUpdateNum(item.getUpdateNum());
            dataList.add(entity);
        }
        dataList = dataList.stream().sorted(Comparator.comparing(
                DocAssetNumVo.DocAssetNum::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docAssetNumVo.setDataList(dataList);
        return docAssetNumVo;
    }
    /* Ended by AICoder, pid:uc37ad73fd562c01458a083530130b51d2d16001 */
    /* Started by AICoder, pid:304e408a2c63e4b144bf0ba7e0fea85030a28ed7 */
    public DocAssetNumVo getStatDocumentYear(DocAssetQueryDto queryDto){
        DocAssetNumVo docAssetNumVo = new DocAssetNumVo();
        List<Integer> allIntegerDay = getAllIntegerDay(ParseUtils.parseStringToInt(queryDto.getStartTime()), ParseUtils.parseStringToInt(queryDto.getEndTime()), queryDto);
        //根据入参的小类id获得下级所有小类id（node_type为3）
        List<String> ids = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        if(list.isEmpty()){
            ids.add(queryDto.getProductCategoryId());
        }else {
            ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        }
        //查询条件
        Integer beginTime = ParseUtils.parseStringToInt(queryDto.getStartTime());
        Integer endTime = ParseUtils.parseStringToInt(queryDto.getEndTime());
        List<DocAssetEntity> docNumData = docAssetYearRepository.getDocNumData(beginTime, endTime, ids);
        List<Integer> integers = docNumData.stream().map(DocAssetEntity::getDay).collect(Collectors.toList());
        List<DocAssetNumVo.DocAssetNum> dataList = new ArrayList<>();
        Set<Integer> allIntegerDaySet = new HashSet<>(allIntegerDay);
        for (Integer num : integers) {
            if (allIntegerDaySet.contains(num)) {
                allIntegerDaySet.remove(num);
            }
        }
        // 遍历integers，筛选不在HashSet中的数据
        List<Integer> missingIntegers = new ArrayList<>(allIntegerDaySet);
        for (Integer missingInteger : missingIntegers) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setAllNum(0);
            entity.setChangeNum(0);
            entity.setUpdateNum(0);
            entity.setTimePeriod(missingInteger + "年");
            dataList.add(entity);
        }
        for (DocAssetEntity item : docNumData) {
            DocAssetNumVo.DocAssetNum entity = new DocAssetNumVo.DocAssetNum();
            entity.setTimePeriod(item.getDay() + "年");
            entity.setAllNum(item.getAllNum());
            entity.setChangeNum(item.getChangeNum());
            entity.setUpdateNum(item.getUpdateNum());
            dataList.add(entity);
        }
        dataList = dataList.stream().sorted(Comparator.comparing(
                DocAssetNumVo.DocAssetNum::getTimePeriod,
                Comparator.nullsLast(Comparator.naturalOrder())
        )).collect(Collectors.toList());
        docAssetNumVo.setDataList(dataList);
        return docAssetNumVo;
    }
    /* Ended by AICoder, pid:304e408a2c63e4b144bf0ba7e0fea85030a28ed7 */
    @Override
    public DocAssetJuniorNumVo getStatJuniorStatistics(DocAssetJuniorQueryDto queryDto) {
        DocAssetJuniorNumVo docAssetJuniorNumVo = new DocAssetJuniorNumVo();
        if(queryDto.getTimeType() == 1){
            docAssetJuniorNumVo  = getJuniorStatDocumentDay(queryDto);
        }else if(queryDto.getTimeType() == 2){
            docAssetJuniorNumVo = getJuniorStatDocumentWeek(queryDto);
        }else if(queryDto.getTimeType() == 3){
            docAssetJuniorNumVo = getJuniorStatDocumentMonth(queryDto);
        }else if(queryDto.getTimeType() == 4){
            docAssetJuniorNumVo = getJuniorStatDocumentYear(queryDto);
        }
        return docAssetJuniorNumVo;
    }
    /* Started by AICoder, pid:tf0b0i8a1bl370814fd208d900cae275e9189f2f */
    public DocAssetJuniorNumVo getJuniorStatDocumentDay(DocAssetJuniorQueryDto queryDto){
        DocAssetJuniorNumVo docAssetJuniorNumVo = new DocAssetJuniorNumVo();
        List<DocAssetJuniorNumVo.DocAssetJuniorNum> juniorNumList = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        Map<String, String> stringMap = list.stream().collect(Collectors.toMap(
                ProductCategoryInfoVo::getId,
                ProductCategoryInfoVo::getPathName,
                (oldValue, newValue) -> newValue));
        List<String> ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        List<DocAssetEntity> dataByDayAndIds = docAssetDayRepository.getDataByDayAndIds(ParseUtils.parseStringToInt(queryDto.getDay()), ids);
        long allNum = 0L;
        long updateNum = 0L;
        long changeNum = 0L;
        for (DocAssetEntity data : dataByDayAndIds) {
            DocAssetJuniorNumVo.DocAssetJuniorNum entity = new DocAssetJuniorNumVo.DocAssetJuniorNum();
            String pathName = stringMap.getOrDefault(data.getProductCategoryId(), "--");
            entity.setProductCategoryName(getNameInfo(pathName));
            entity.setAllNum(data.getAllNum());
            entity.setChangeNum(data.getChangeNum());
            entity.setUpdateNum(data.getUpdateNum());
            allNum += data.getAllNum();
            changeNum += data.getChangeNum();
            updateNum += data.getUpdateNum();
            juniorNumList.add(entity);
        }
        juniorNumList = juniorNumList.stream()
                .sorted(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getChangeNum).reversed()))
                .collect(Collectors.toList());
        for (DocAssetJuniorNumVo.DocAssetJuniorNum data : juniorNumList) {
            if(data.getChangeNum() > 0){
                data.setChangeFlag(SystemConstants.STR_ONE);
            }else if (data.getChangeNum() < 0){
                data.setChangeNum(Math.abs(data.getChangeNum()));
                data.setChangeFlag(SystemConstants.STR_TWO);
            }else {
                data.setChangeFlag(SystemConstants.STR_ZERO);
            }
        }
        docAssetJuniorNumVo.setJuniorList(juniorNumList);
        docAssetJuniorNumVo.setAllNum(allNum);
        if(changeNum > 0){
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ONE);
        }else if (changeNum < 0){
            docAssetJuniorNumVo.setChangeNum(Math.abs(changeNum));
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_TWO);
        }else {
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ZERO);
        }
        docAssetJuniorNumVo.setUpdateNum(updateNum);
        return docAssetJuniorNumVo;
    }
    /* Ended by AICoder, pid:tf0b0i8a1bl370814fd208d900cae275e9189f2f */
    public String getNameInfo(String fullName){
        int firstIndex = fullName.indexOf("/"); // 第一个 "/" 的位置
        return (firstIndex != -1) ? fullName.substring(firstIndex + 1) : "--";// 处理无 "/" 的情况
    }
    /* Started by AICoder, pid:v9137o1febu8b051410e097b5095ad70cbf9c4d0 */
    public DocAssetJuniorNumVo getJuniorStatDocumentWeek(DocAssetJuniorQueryDto queryDto){
        DocAssetJuniorNumVo docAssetJuniorNumVo = new DocAssetJuniorNumVo();
        List<DocAssetJuniorNumVo.DocAssetJuniorNum> juniorNumList = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        Map<String, String> stringMap = list.stream().collect(Collectors.toMap(
                ProductCategoryInfoVo::getId,
                ProductCategoryInfoVo::getPathName,
                (oldValue, newValue) -> newValue));
        List<String> ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        List<DocAssetEntity> dataByDayAndIds = docAssetWeekRepository.getDataByDayAndIds(ParseUtils.parseStringToInt(queryDto.getDay()), ids);
        long allNum = 0L;
        long updateNum = 0L;
        long changeNum = 0L;
        for (DocAssetEntity data : dataByDayAndIds) {
            DocAssetJuniorNumVo.DocAssetJuniorNum entity = new DocAssetJuniorNumVo.DocAssetJuniorNum();
            String pathName = stringMap.getOrDefault(data.getProductCategoryId(), "--");
            entity.setProductCategoryName(getNameInfo(pathName));
            entity.setAllNum(data.getAllNum());
            entity.setChangeNum(data.getChangeNum());
            entity.setUpdateNum(data.getUpdateNum());
            allNum += data.getAllNum();
            changeNum += data.getChangeNum();
            updateNum += data.getUpdateNum();
            juniorNumList.add(entity);
        }
        juniorNumList = juniorNumList.stream()
                .sorted(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getChangeNum).reversed()))
                .collect(Collectors.toList());
        for (DocAssetJuniorNumVo.DocAssetJuniorNum data : juniorNumList) {
            if(data.getChangeNum() > 0){
                data.setChangeFlag(SystemConstants.STR_ONE);
            }else if (data.getChangeNum() < 0){
                data.setChangeNum(Math.abs(data.getChangeNum()));
                data.setChangeFlag(SystemConstants.STR_TWO);
            }else {
                data.setChangeFlag(SystemConstants.STR_ZERO);
            }
        }
        docAssetJuniorNumVo.setJuniorList(juniorNumList);
        docAssetJuniorNumVo.setAllNum(allNum);
        if(changeNum > 0){
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ONE);
        }else if (changeNum < 0){
            docAssetJuniorNumVo.setChangeNum(Math.abs(changeNum));
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_TWO);
        }else {
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ZERO);
        }
        docAssetJuniorNumVo.setUpdateNum(updateNum);
        return docAssetJuniorNumVo;
    }
    /* Ended by AICoder, pid:v9137o1febu8b051410e097b5095ad70cbf9c4d0 */
    /* Started by AICoder, pid:s060baaee684362143450a6f30291489e402061f */
    public DocAssetJuniorNumVo getJuniorStatDocumentMonth(DocAssetJuniorQueryDto queryDto){
        DocAssetJuniorNumVo docAssetJuniorNumVo = new DocAssetJuniorNumVo();
        List<DocAssetJuniorNumVo.DocAssetJuniorNum> juniorNumList = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        Map<String, String> stringMap = list.stream().collect(Collectors.toMap(
                ProductCategoryInfoVo::getId,
                ProductCategoryInfoVo::getPathName,
                (oldValue, newValue) -> newValue));
        List<String> ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        List<DocAssetEntity> dataByDayAndIds = docAssetMonthRepository.getDataByDayAndIds(ParseUtils.parseStringToInt(queryDto.getDay()), ids);
        long allNum = 0L;
        long updateNum = 0L;
        long changeNum = 0L;
        for (DocAssetEntity data : dataByDayAndIds) {
            DocAssetJuniorNumVo.DocAssetJuniorNum entity = new DocAssetJuniorNumVo.DocAssetJuniorNum();
            String pathName = stringMap.getOrDefault(data.getProductCategoryId(), "--");
            entity.setProductCategoryName(getNameInfo(pathName));
            entity.setAllNum(data.getAllNum());
            entity.setChangeNum(data.getChangeNum());
            entity.setUpdateNum(data.getUpdateNum());
            allNum += data.getAllNum();
            changeNum += data.getChangeNum();
            updateNum += data.getUpdateNum();
            juniorNumList.add(entity);
        }
        juniorNumList = juniorNumList.stream()
                .sorted(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getChangeNum).reversed()))
                .collect(Collectors.toList());
        for (DocAssetJuniorNumVo.DocAssetJuniorNum data : juniorNumList) {
            if(data.getChangeNum() > 0){
                data.setChangeFlag(SystemConstants.STR_ONE);
            }else if (data.getChangeNum() < 0){
                data.setChangeNum(Math.abs(data.getChangeNum()));
                data.setChangeFlag(SystemConstants.STR_TWO);
            }else {
                data.setChangeFlag(SystemConstants.STR_ZERO);
            }
        }
        docAssetJuniorNumVo.setJuniorList(juniorNumList);
        docAssetJuniorNumVo.setAllNum(allNum);
        if(changeNum > 0){
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ONE);
        }else if (changeNum < 0){
            docAssetJuniorNumVo.setChangeNum(Math.abs(changeNum));
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_TWO);
        }else {
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ZERO);
        }
        docAssetJuniorNumVo.setUpdateNum(updateNum);
        return docAssetJuniorNumVo;
    }
    /* Ended by AICoder, pid:s060baaee684362143450a6f30291489e402061f */
    /* Started by AICoder, pid:610b6ubd2al5be31416f0b3a00fb27803e83ca2a */
    public DocAssetJuniorNumVo getJuniorStatDocumentYear(DocAssetJuniorQueryDto queryDto){
        DocAssetJuniorNumVo docAssetJuniorNumVo = new DocAssetJuniorNumVo();
        List<DocAssetJuniorNumVo.DocAssetJuniorNum> juniorNumList = new ArrayList<>();
        List<ProductCategoryInfoVo> list = productService.getIdsByParentId(queryDto.getProductCategoryId());
        Map<String, String> stringMap = list.stream().collect(Collectors.toMap(
                ProductCategoryInfoVo::getId,
                ProductCategoryInfoVo::getPathName,
                (oldValue, newValue) -> newValue));
        List<String> ids = list.stream().map(ProductCategoryInfoVo::getId).collect(Collectors.toList());
        List<DocAssetEntity> dataByDayAndIds = docAssetYearRepository.getDataByDayAndIds(ParseUtils.parseStringToInt(queryDto.getDay()), ids);
        long allNum = 0L;
        long updateNum = 0L;
        long changeNum = 0L;
        for (DocAssetEntity data : dataByDayAndIds) {
            DocAssetJuniorNumVo.DocAssetJuniorNum entity = new DocAssetJuniorNumVo.DocAssetJuniorNum();
            String pathName = stringMap.getOrDefault(data.getProductCategoryId(), "--");
            entity.setProductCategoryName(getNameInfo(pathName));
            entity.setAllNum(data.getAllNum());
            entity.setChangeNum(data.getChangeNum());
            entity.setUpdateNum(data.getUpdateNum());
            allNum += data.getAllNum();
            changeNum += data.getChangeNum();
            updateNum += data.getUpdateNum();
            juniorNumList.add(entity);
        }
        juniorNumList = juniorNumList.stream()
                .sorted(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(DocAssetJuniorNumVo.DocAssetJuniorNum::getChangeNum).reversed()))
                .collect(Collectors.toList());
        for (DocAssetJuniorNumVo.DocAssetJuniorNum data : juniorNumList) {
            if(data.getChangeNum() > 0){
                data.setChangeFlag(SystemConstants.STR_ONE);
            }else if (data.getChangeNum() < 0){
                data.setChangeNum(Math.abs(data.getChangeNum()));
                data.setChangeFlag(SystemConstants.STR_TWO);
            }else {
                data.setChangeFlag(SystemConstants.STR_ZERO);
            }
        }
        docAssetJuniorNumVo.setJuniorList(juniorNumList);
        docAssetJuniorNumVo.setAllNum(allNum);
        if(changeNum > 0){
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ONE);
        }else if (changeNum < 0){
            docAssetJuniorNumVo.setChangeNum(Math.abs(changeNum));
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_TWO);
        }else {
            docAssetJuniorNumVo.setChangeNum(changeNum);
            docAssetJuniorNumVo.setChangeFlag(SystemConstants.STR_ZERO);
        }
        docAssetJuniorNumVo.setUpdateNum(updateNum);
        return docAssetJuniorNumVo;
    }
    /* Ended by AICoder, pid:610b6ubd2al5be31416f0b3a00fb27803e83ca2a */
    @Override
    public void export(DocAssetJuniorQueryDto queryDto, HttpServletResponse respons) {
        DocAssetJuniorNumVo statJuniorStatistics = getStatJuniorStatistics(queryDto);
        exportUserLoginDetailExcel(respons,statJuniorStatistics.getJuniorList());
    }
    /* Started by AICoder, pid:ff52157f7bz24d3142870b45705b273446260f49 */
    private void exportUserLoginDetailExcel(HttpServletResponse response, List<DocAssetJuniorNumVo.DocAssetJuniorNum> entityList) {
        response.reset();
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        String fileName = "下级文档数量统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            try (ServletOutputStream outputStream = response.getOutputStream();
                 ExcelWriter writer = EasyExcel.write(outputStream)
                         .excelType(ExcelTypeEnum.XLS)
                         .charset(StandardCharsets.UTF_8)
                         .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                         .build()) {

                List<List<String>> headers = buildHeaders();
                List<List<Object>> data = buildData(entityList);

                WriteSheet sheet = EasyExcel.writerSheet("文档数量统计")
                        .head(headers)
                        .build();
                writer.write(data, sheet);
            }
        } catch (IOException e) {
            log.error("导出 Excel 失败", e);
        }
    }
    /* Ended by AICoder, pid:ff52157f7bz24d3142870b45705b273446260f49 */
    /* Started by AICoder, pid:w37d6j0c0db83d914cbe084010f45d3c662484fd */
    private List<List<String>> buildHeaders() {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("产品小类"));
        headers.add(Collections.singletonList("文档总数"));
        headers.add(Collections.singletonList("更新文档"));
        headers.add(Collections.singletonList("变化数"));
        return headers;
    }

    private List<List<Object>> buildData(List<DocAssetJuniorNumVo.DocAssetJuniorNum> list) {
        List<List<Object>> rows = new ArrayList<>();
        for (DocAssetJuniorNumVo.DocAssetJuniorNum item : list) {
            List<Object> row = new ArrayList<>();
            row.add(item.getProductCategoryName());
            row.add(item.getAllNum());
            row.add(item.getUpdateNum());
            if(SystemConstants.STR_TWO.equals(item.getChangeFlag())){
                row.add(-item.getChangeNum());
            }else {
                row.add(item.getChangeNum());
            }
            rows.add(row);
        }
        return rows;
    }
    /* Ended by AICoder, pid:w37d6j0c0db83d914cbe084010f45d3c662484fd */
    /* Started by AICoder, pid:y37e3t83e1320ec141660b32e1f3aa43e1e6a496 */
    public String getDayInfo(int dateInt){
        // 将整数转换为字符串
        String dateStr = String.valueOf(dateInt);
        // 验证输入格式
        if (dateStr.length() != 8) {
            throw new IllegalArgumentException("日期格式必须为8位数字（yyyyMMdd），输入值：" + dateStr);
        }
        try {
            // 解析各部分日期
            String year = dateStr.substring(0, 4);
            String month = dateStr.substring(4, 6);
            String day = dateStr.substring(6, 8);
            // 返回格式化结果
            return year + "年" + month + "月" + day + "日";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("日期必须全部由数字组成，输入值：" + dateStr);
        }
    }
    public String getWeekInfo(int yearWeek){
        // 将整数转换为字符串
        String yearWeekStr = String.valueOf(yearWeek);
        // 验证基本长度
        if (yearWeekStr.length() != 6) {
            throw new IllegalArgumentException("输入必须是6位数字（格式：yyyyww），输入值：" + yearWeek);
        }
        try {
            // 分割年份和周数
            String yearPart = yearWeekStr.substring(0, 4);
            String weekPart = yearWeekStr.substring(4);
            return yearPart + "年" + weekPart + "周";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入必须全部由数字组成，输入值：" + yearWeek, e);
        }
    }
    public String getMonthInfo(int yearMonth){
        // 将整数转换为字符串
        String yearWeekStr = String.valueOf(yearMonth);
        // 验证基本长度
        if (yearWeekStr.length() != 6) {
            throw new IllegalArgumentException("输入必须是6位数字（格式：yyyyww），输入值：" + yearMonth);
        }
        try {
            // 分割年份和周数
            String yearPart = yearWeekStr.substring(0, 4);
            String monthPart = yearWeekStr.substring(4);
            return yearPart + "年" + monthPart + "月";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入必须全部由数字组成，输入值：" + yearMonth, e);
        }
    }
    public List<Integer> getAllIntegerDay(int beginTime, int endTime, DocAssetQueryDto queryDto){
        List<Integer> result = new ArrayList<>();
        if(queryDto.getTimeType() == 1){
            getDayInfo(beginTime, endTime, result);
        }else if(queryDto.getTimeType() == 3){
            getMonthInfo(beginTime, endTime, result);
        }else if(queryDto.getTimeType() == 2){
            getWeekInfo(beginTime, endTime, result);
        }else {
            for(int i = beginTime;i <= endTime;i++){
                result.add(i);
            }
        }
        return result;
    }
    public void getDayInfo(int beginTime, int endTime, List<Integer> result){
        int year = beginTime / 10000;
        int month = (beginTime % 10000) / 100;
        int day = beginTime % 100;
        LocalDate start = LocalDate.of(year, month, day);
        int year1 = endTime / 10000;
        int month1 = (endTime % 10000) / 100;
        int day1 = endTime % 100;
        LocalDate end = LocalDate.of(year1, month1, day1);
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            result.add(date.getYear() * 10000 + date.getMonthValue() * 100 + date.getDayOfMonth());
        }
    }
    public void getWeekInfo(int beginTime, int endTime, List<Integer> result){
        int year = beginTime / 100;
        int weekNumber = beginTime % 100;
        LocalDate start = LocalDate.of(year, 1, 1)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, weekNumber)
                .with(ChronoField.DAY_OF_WEEK, 1);
        int year1 = endTime / 100;
        int weekNumber1 = endTime % 100;
        LocalDate end = LocalDate.of(year1, 1, 1)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, weekNumber1)
                .with(ChronoField.DAY_OF_WEEK, 1);
        for (LocalDate weekStart = start; !weekStart.isAfter(end); weekStart = weekStart.plusWeeks(1)) {
            result.add(weekStart.get(IsoFields.WEEK_BASED_YEAR ) * 100 + weekStart.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
        }
    }
    public void getMonthInfo(int beginTime, int endTime, List<Integer> result){
        int year1 = beginTime / 100;
        int month1 = beginTime % 100;
        YearMonth start = YearMonth.of(year1, month1);
        int year2 = endTime / 100;
        int month2 = endTime % 100;
        YearMonth end = YearMonth.of(year2, month2);
        for (YearMonth month = start; !month.isAfter(end); month = month.plusMonths(1)) {
            result.add(month.getYear() * 100 + month.getMonthValue());
        }
    }
    /* Ended by AICoder, pid:y37e3t83e1320ec141660b32e1f3aa43e1e6a496 */
}
