package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DocDownStatMonthEntity {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 统计周（格式：YYYYWW，如202501表示2025年第1周）
     */
    private Integer day;

    /**
     * 部门/组织ID
     */
    private String deptId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 下载次数
     */
    private Integer downloadNum;

    /**
     * 预览次数
     */
    private Integer previewNum;

    // 查询条件字段（非数据库字段）
    private Integer beginTime;

    // 查询条件字段（非数据库字段）
    private Integer endTime;
}
