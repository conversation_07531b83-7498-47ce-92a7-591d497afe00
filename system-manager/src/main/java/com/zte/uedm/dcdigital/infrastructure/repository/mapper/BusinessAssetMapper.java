package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机资产数据访问接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface BusinessAssetMapper {

    /**
     * 日表根据时间范围查询统计记录
     */
    List<BusinessAssetDayEntity> selectDayDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 周表根据时间范围查询统计记录
     */
    List<BusinessAssetWeekEntity> selectWeekDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 月表根据时间范围查询统计记录
     */
    List<BusinessAssetMonthEntity> selectMonthDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 年表根据时间范围查询统计记录
     */
    List<BusinessAssetYearEntity> selectYearDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 日表根据地区ID和时间范围查询统计记录
     */
    List<BusinessAssetDayEntity> selectDayDateByAreaIdAndTimeRange(@Param("areaId") String areaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 周表根据地区ID和时间范围查询统计记录
     */
    List<BusinessAssetWeekEntity> selectWeekDateByAreaIdAndTimeRange(@Param("areaId") String areaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 月表根据地区ID和时间范围查询统计记录
     */
    List<BusinessAssetMonthEntity> selectMonthDateByAreaIdAndTimeRange(@Param("areaId") String areaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 年表根据地区ID和时间范围查询统计记录
     */
    List<BusinessAssetYearEntity> selectYearDateByAreaIdAndTimeRange(@Param("areaId") String areaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询指定地区的下级地区商机统计数据
     */
    List<BusinessAssetDayEntity> selectJuniorAreaBusinessData(@Param("areaId") String areaId, @Param("timeType") Integer timeType, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 插入商机启动投标记录
     */
    void insertBusinessStartRecord(@Param("projectId") String projectId, @Param("areaId") String areaId, @Param("day") String day);

    /**
     * 插入商机启动投标记录（使用实体对象）
     */
    void insertBusinessStartRecordEntity(BusinessAssetUpdEntity entity);

    /**
     * 批量插入日表数据
     */
    void batchInsertDayData(@Param("list") List<BusinessAssetDayEntity> list);

    /**
     * 批量插入周表数据
     */
    void batchInsertWeekData(@Param("list") List<BusinessAssetWeekEntity> list);

    /**
     * 批量插入月表数据
     */
    void batchInsertMonthData(@Param("list") List<BusinessAssetMonthEntity> list);

    /**
     * 批量插入年表数据
     */
    void batchInsertYearData(@Param("list") List<BusinessAssetYearEntity> list);

    /**
     * 根据日期查询启动投标记录数量
     */
    Long countBusinessStartRecordsByDay(@Param("day") String day, @Param("areaId") String areaId);

    /**
     * 根据时间范围查询启动投标记录数量
     */
    Long countBusinessStartRecordsByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("areaId") String areaId);

    /**
     * 查询指定日期的日表数据是否存在
     */
    Integer checkDayDataExists(@Param("day") String day, @Param("areaId") String areaId);

    /**
     * 查询指定周期的周表数据是否存在
     */
    Integer checkWeekDataExists(@Param("day") String day, @Param("areaId") String areaId);

    /**
     * 查询指定月份的月表数据是否存在
     */
    Integer checkMonthDataExists(@Param("day") String day, @Param("areaId") String areaId);

    /**
     * 查询指定年份的年表数据是否存在
     */
    Integer checkYearDataExists(@Param("day") String day, @Param("areaId") String areaId);

    /**
     * 更新日表数据
     */
    void updateDayData(BusinessAssetDayEntity entity);

    /**
     * 更新周表数据
     */
    void updateWeekData(BusinessAssetWeekEntity entity);

    /**
     * 更新月表数据
     */
    void updateMonthData(BusinessAssetMonthEntity entity);

    /**
     * 更新年表数据
     */
    void updateYearData(BusinessAssetYearEntity entity);

    /**
     * 根据地区ID查询所有下级地区ID
     */
    List<String> selectSubAreaIds(@Param("areaId") String areaId);

    /**
     * 查询地区名称
     */
    String selectAreaNameById(@Param("areaId") String areaId);

    /**
     * 统计天表中的总记录数
     */
    Integer countAllDayData();

    /**
     * 查询天表中所有不重复的日期
     */
    List<String> selectAllDistinctDays();

    /**
     * 根据父节点ID和时间点查询所有子节点的日表数据
     */
    List<BusinessAssetDayEntity> selectChildAreaDayDataByTimePoint(@Param("parentAreaId") String parentAreaId, @Param("timePoint") String timePoint);

    /**
     * 根据父节点ID和时间点查询所有子节点的周表数据
     */
    List<BusinessAssetWeekEntity> selectChildAreaWeekDataByTimePoint(@Param("parentAreaId") String parentAreaId, @Param("timePoint") String timePoint);

    /**
     * 根据父节点ID和时间点查询所有子节点的月表数据
     */
    List<BusinessAssetMonthEntity> selectChildAreaMonthDataByTimePoint(@Param("parentAreaId") String parentAreaId, @Param("timePoint") String timePoint);

    /**
     * 根据父节点ID和时间点查询所有子节点的年表数据
     */
    List<BusinessAssetYearEntity> selectChildAreaYearDataByTimePoint(@Param("parentAreaId") String parentAreaId, @Param("timePoint") String timePoint);

    /**
     * 根据父节点ID和时间范围查询所有子节点的日表数据（用于统计列表）
     */
    List<BusinessAssetDayEntity> selectChildAreaDayDataByTimeRange(@Param("parentAreaId") String parentAreaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据父节点ID和时间范围查询所有子节点的周表数据（用于统计列表）
     */
    List<BusinessAssetWeekEntity> selectChildAreaWeekDataByTimeRange(@Param("parentAreaId") String parentAreaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据父节点ID和时间范围查询所有子节点的月表数据（用于统计列表）
     */
    List<BusinessAssetMonthEntity> selectChildAreaMonthDataByTimeRange(@Param("parentAreaId") String parentAreaId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据父节点ID和时间范围查询所有子节点的年表数据（用于统计列表）
     */
    List<BusinessAssetYearEntity> selectChildAreaYearDataByTimeRange(@Param("parentAreaId") String parentAreaId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
