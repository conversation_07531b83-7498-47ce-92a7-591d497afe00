package com.zte.uedm.dcdigital.interfaces.web.vo;


import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/* Started by AICoder, pid:g81b01ca19kc68f145fe0a2680123f77a8c7589d */
@Getter
@Setter
@ToString
public class LoginStatisticsVo {
    //总用户
    private long totalUser;

    //总活跃用户
    private long activeUser;

    //人均登陆天数
    private long avgLoginDays;

    //总活跃用户比例
    private double activeUserRate;

    private List<TotalSystem> totalSystem; //系统访问量

    private List<LevelDepart> levelDepart; //部门访问

    private List<UserLoginDetail> userLoginDetails; //个人访问天数


    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TotalSystem {

        //活跃用户数
        private long activeUser;

        //人均登陆天数
        private double avgLoginDays;

        //活跃用户比
        private double activeUserRate;

        //周期
        private String timePeriod;

    }

    @Getter
    @Setter
    @ToString
    public static class LevelDepart {

        //颜色
        private String color;

        //部门
        private String departName;

        //部门活跃人数
        private List<Integer> departActiveName;

        //活跃用户比
        private List<Integer> activeUserRate;

        //时间
        private List<String> timePeriod;

    }

    @Getter
    @Setter
    @ToString
    public static class UserLoginDetail {
        private String userName;
        private long total; // 个人总登录次数
        private List<Map<String, Long>> periodLoginStatTimes;// <周期, 登录次数>
    }


}
/* Ended by AICoder, pid:g81b01ca19kc68f145fe0a2680123f77a8c7589d */