<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- Started by AICoder, pid:m14a4m8057xb8ce140ca0a66400ed912153492bd -->
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.RoleMapper">

    <!-- 根据角色名称查询角色信息 -->
    <select id="selectByName" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo">
        SELECT *
        FROM auth_role
        WHERE name = #{roleName}
    </select>

    <select id="selectByCode" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo">
        SELECT *
        FROM auth_role
        WHERE code = #{code}
    </select>

    <select id="selectByCodes" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo">
        SELECT *
        FROM auth_role
        WHERE code in
            <foreach collection="codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
    </select>


</mapper>
        <!-- Ended by AICoder, pid:m14a4m8057xb8ce140ca0a66400ed912153492bd -->