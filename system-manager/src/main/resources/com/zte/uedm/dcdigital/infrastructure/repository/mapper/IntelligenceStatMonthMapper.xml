<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.IntelligenceStatMonthMapper">

    <!-- 根据用户ID和月份查询统计记录 -->
    <select id="selectByUserAndMonth" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.IntelligenceStatMonthPo">
        SELECT id, day, dept_id, user_id, material_num, bid_num
        FROM intelligence_stat_month
        WHERE user_id = #{userId}
          AND day = #{month}
        LIMIT 1
    </select>

    <!-- 更新物料智能体次数 -->
    <update id="updateMaterialNum">
        UPDATE intelligence_stat_month
        SET material_num = #{count}
        WHERE user_id = #{userId}
          AND day = #{month}
    </update>

    <!-- 更新标书分析智能体次数 -->
    <update id="updateBidNum">
        UPDATE intelligence_stat_month
        SET bid_num = #{count}
        WHERE user_id = #{userId}
          AND day = #{month}
    </update>

    <!-- 根据用户ID列表和时间范围查询汇总统计数据 -->
    <select id="selectSummaryByUserListAndTimeRange" resultType="map">
        SELECT
            SUM(CAST(material_num AS INTEGER)) as totalMaterial,
            SUM(CAST(bid_num AS INTEGER)) as totalBid,
            SUM(CAST(material_num AS INTEGER) + CAST(bid_num AS INTEGER)) as total,
            COUNT(DISTINCT user_id) as userCount
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 根据用户ID列表和时间范围查询时间序列数据 -->
    <select id="selectTimeSeriesDataByUserListAndTimeRange" resultType="map">
        SELECT
            day,
            SUM(CAST(material_num AS INTEGER) + CAST(bid_num AS INTEGER)) as total,
            COUNT(DISTINCT user_id) as userCount
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day
    </select>

    <!-- 根据用户ID列表和时间范围查询用户统计数据 -->
    <select id="selectUserStatByUserListAndTimeRange" resultType="map">
        SELECT
            user_id as userId,
            day,
            SUM(CAST(material_num AS INTEGER) + CAST(bid_num AS INTEGER)) as num
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        GROUP BY user_id, day
        ORDER BY day, user_id
    </select>

    <!-- 根据用户ID列表和时间范围查询智能体类型统计数据 -->
    <select id="selectIntelligenceTypeStatByUserListAndTimeRange" resultType="map">
        SELECT
            day,
            'material' as intelligenceType,
            SUM(CAST(material_num AS INTEGER)) as num
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day

        UNION ALL

        SELECT
            day,
            'bid' as intelligenceType,
            SUM(CAST(bid_num AS INTEGER)) as num
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day, intelligenceType
    </select>

    <!-- 根据用户ID列表和时间范围查询部门统计数据 -->
    <select id="selectDepartmentStatByUserListAndTimeRange" resultType="map">
        SELECT
            day,
            dept_id,
            SUM(CAST(material_num AS INTEGER) + CAST(bid_num AS INTEGER)) as totalNum,
            COUNT(DISTINCT user_id) as userCount
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day, dept_id
        ORDER BY day, dept_id
    </select>
    <select id="selectByTimeRange"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.IntelligenceStatMonthPo">
        SELECT * from intelligence_stat_month where day BETWEEN #{startTime} AND #{endTime}
    </select>


    <!-- 统计指定用户列表在时间范围内的所有用户数 -->
    <select id="countUsersInTimeRange" resultType="Long">
        SELECT COUNT(DISTINCT user_id)
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 统计指定用户列表在统计表中出现的所有用户数（不限时间） -->
    <select id="countAllUsersInStatTable" resultType="Long">
        SELECT COUNT(DISTINCT user_id)
        FROM intelligence_stat_month
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

</mapper>
