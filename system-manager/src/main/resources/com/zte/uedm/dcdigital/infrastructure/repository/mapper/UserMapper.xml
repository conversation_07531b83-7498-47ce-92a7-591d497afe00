<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.UserMapper">

    <select id="selectBySearchText" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.UserPo">
        SELECT *
        FROM auth_user
        WHERE
        name LIKE CONCAT('%', #{searchText}, '%')
        OR
        employee_id LIKE CONCAT('%', #{searchText}, '%')
        OR
        (name || employee_id) LIKE CONCAT('%', #{searchText}, '%')
    </select>
    <select id="queryDepartmentNode" resultType="java.lang.String">
        WITH RECURSIVE dept_hierarchy AS (
        -- 初始查询：从用户关联的所有部门开始查找，并确保路径数组类型一致
        SELECT
        udr.dept_id,
        d.parent_id,
        ARRAY[d.dept_id::text]::text[] AS path  -- 初始化路径
        FROM
        auth_dept_user_relation udr
        JOIN
        auth_dept d ON udr.dept_id = d.dept_id
        WHERE
        udr.user_id = #{userId} -- 将这里替换为实际要查询的用户ID
        UNION ALL

        -- 递归部分：找到每个部门的父部门，并添加到结果集，保持路径数组类型一致
        SELECT
        parent_d.dept_id,  -- 注意这里的改变，使用父级部门的dept_id
        parent_d.parent_id,
        (dh.path || parent_d.dept_id::text)::text[]  -- 更新并保持路径
        FROM
        dept_hierarchy dh
        JOIN
        auth_dept parent_d ON dh.parent_id = parent_d.dept_id  -- 连接条件变更为寻找父级部门
        WHERE
        NOT parent_d.dept_id = ANY(dh.path) -- 防止循环引用
        )
        -- 最终选择所有相关的dept_id及其完整的路径信息（可选）
        SELECT DISTINCT dept_id, path
        FROM dept_hierarchy
        ORDER BY dept_id;
    </select>
    <select id="selectAllUserIds" resultType="String">
        SELECT DISTINCT id
        FROM auth_user;
    </select>
</mapper>