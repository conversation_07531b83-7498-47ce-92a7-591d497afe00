package com.zte.uedm.dcdigital.infrastructure.repository.converter;

/* Started by AICoder, pid:p0bfaf61c53447c14f1408d1119efe07fd06f5aa */

import com.zte.uedm.dcdigital.common.bean.system.ResourceEntityDto;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.ResourceEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ResourceEntityPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ResourceEntityVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ResourceEntityConverterTest {

    private ResourceEntityPoConverter converter;

    @BeforeEach
    public void setUp() {
        converter = ResourceEntityPoConverter.INSTANCE;
    }

    @Test
    public void testConvertEntityToPo() {
        ResourceEntity entity = new ResourceEntity();
        entity.setId("1");
        entity.setEntityId("2");
        entity.setType(1);
        entity.setCreateTime("2023-01-01T00:00:00Z");
        entity.setUpdateTime("2023-01-01T00:00:00Z");
        entity.setCreateBy("User1");
        entity.setUpdateBy("User2");

        ResourceEntityPo po = converter.convertEntityToPo(entity);

        assertEquals("1", po.getId());
        assertEquals("2", po.getEntityId());
        assertEquals(1, po.getType());
        assertEquals("2023-01-01T00:00:00Z", po.getCreateTime());
        assertEquals("2023-01-01T00:00:00Z", po.getUpdateTime());
        assertEquals("User1", po.getCreateBy());
        assertEquals("User2", po.getUpdateBy());
    }

    @Test
    public void testConvertPoToEntity() {
        ResourceEntityPo po = new ResourceEntityPo();
        po.setId("1");
        po.setEntityId("2");
        po.setType(1);
        po.setCreateTime("2023-01-01T00:00:00Z");
        po.setUpdateTime("2023-01-01T00:00:00Z");
        po.setCreateBy("User1");
        po.setUpdateBy("User2");

        ResourceEntity entity = converter.convertPoToEntity(po);

        assertEquals("1", entity.getId());
        assertEquals("2", entity.getEntityId());
        assertEquals(1, entity.getType());
        assertEquals("2023-01-01T00:00:00Z", entity.getCreateTime());
        assertEquals("2023-01-01T00:00:00Z", entity.getUpdateTime());
        assertEquals("User1", entity.getCreateBy());
        assertEquals("User2", entity.getUpdateBy());
    }

    @Test
    public void testConverterDtoToEntity() {
        ResourceEntityDto dto = new ResourceEntityDto();
        dto.setEntityId("2");
        dto.setType(1);

        ResourceEntity entity = converter.convertDtoToEntity(dto);

        assertEquals("2", entity.getEntityId());
        assertEquals(1, entity.getType());
    }

    @Test
    public void testConvertEntityToVo() {
        ResourceEntity entity = new ResourceEntity();
        entity.setId("1");
        entity.setEntityId("2");
        entity.setType(2);
        entity.setCreateTime("2023-01-01T00:00:00Z");
        entity.setUpdateTime("2023-01-01T00:00:00Z");
        entity.setCreateBy("User1");
        entity.setUpdateBy("User2");

        ResourceEntityVo vo = converter.convertEntityToVo(entity);

        assertEquals("1", vo.getId());
        assertEquals("2", vo.getEntityId());
        assertEquals(2, vo.getType());
        assertEquals("2023-01-01T00:00:00Z", vo.getCreateTime());
        assertEquals("2023-01-01T00:00:00Z", vo.getUpdateTime());
        assertEquals("User1", vo.getCreateBy());
        assertEquals("User2", vo.getUpdateBy());
    }
}

/* Ended by AICoder, pid:p0bfaf61c53447c14f1408d1119efe07fd06f5aa */
