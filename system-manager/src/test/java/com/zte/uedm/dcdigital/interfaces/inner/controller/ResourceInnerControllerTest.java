package com.zte.uedm.dcdigital.interfaces.inner.controller;

/* Started by AICoder, pid:w2b78041d2u7df114e260bc8408e3c892586b51b */

import com.zte.uedm.dcdigital.application.system.executor.ResourceCommandService;
import com.zte.uedm.dcdigital.application.system.executor.ResourceQueryService;
import com.zte.uedm.dcdigital.common.bean.system.ResourceDto;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.UserRoleResourceService;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ResourceQueryVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(MockitoJUnitRunner.class)
public class ResourceInnerControllerTest {

    @InjectMocks
    private ResourceInnerController resourceInnerController;

    @Mock
    private ResourceCommandService resourceCommandService;

    @Mock
    private ResourceQueryService resourceQueryService;

    @Mock
    private UserRoleResourceService userRoleResourceService;

    private ResourceDto resourceDto;

    @Before
    public void setUp() {
        resourceDto = new ResourceDto();
    }

    // 测试创建资源的方法
    @Test
    public void given_ResourceDto_when_CreateResource_then_ReturnSuccess() {
        BaseResult<Object> result = resourceInnerController.createResource(resourceDto);
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    // 测试更新资源的方法
    @Test
    public void given_ResourceDto_when_UpdateResource_then_ReturnSuccess() {
        BaseResult<Object> result = resourceInnerController.updateResource(resourceDto);
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    // 测试删除资源的方法
    @Test
    public void given_EntityId_when_DeleteResource_then_ReturnSuccess() {
        String entityId = "123";
        BaseResult<Object> result = resourceInnerController.deleteResource(entityId);
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    // 测试通过用户ID查询资源的方法
    @Test
    public void given_UserIdAndType_when_QueryResourceByUserId_then_ReturnListOfIds() {
        String userId = "user1";
        Integer type = 1;
        List<String> ids = Arrays.asList("1", "2", "3");
        Mockito.when(resourceQueryService.queryResourceByUserId(eq(userId), eq(type))).thenReturn(ids);
        BaseResult<List<String>> result = resourceInnerController.queryResourceByUserId(userId, type);
        assertEquals(BaseResult.success(ids).getCode(), result.getCode());
    }

    // 测试查询所有资源的方法
    @Test
    public void given_Type_when_QueryAllResources_then_ReturnListOfResourceQueryVo() {
        Integer type = 1;
        List<ResourceQueryVo> resources = Collections.singletonList(new ResourceQueryVo());
        Mockito.when(resourceQueryService.queryAllResource(eq(type))).thenReturn(resources);
        BaseResult<List<ResourceQueryVo>> result = resourceInnerController.queryAllResources(type);
        assertEquals(BaseResult.success(resources).getCode(), result.getCode());
    }

    /* Started by AICoder, pid:j0c97af09ed82a914642082d70a11a07b3977f9e */
    @Test
    public void testRueryUserByRoleCodeAndResourceId(){
        List<UserVo> userVos = Collections.singletonList(new UserVo());
        Mockito.when(userRoleResourceService.selectUserByRoleCodeAndResourceId("roleCode", "resourceId")).thenReturn(userVos);
        BaseResult<List<UserVo>> listBaseResult = resourceInnerController.queryUserByRoleCodeAndResourceId("roleCode", "resourceId");
        assertEquals(BaseResult.success(userVos).getCode(), listBaseResult.getCode());
    }
    /* Ended by AICoder, pid:j0c97af09ed82a914642082d70a11a07b3977f9e */

    @Test
    public void testQueryRoleByUserIdAndResourceId(){
        List<RoleVo> roleVos = Collections.singletonList(new RoleVo());
        Mockito.when(userRoleResourceService.selectRoleByUserIdAndResourceId("userId", "resourceId")).thenReturn(roleVos);
        BaseResult<List<RoleVo>> listBaseResult = resourceInnerController.queryRoleByUserIdAndResourceId("userId", "resourceId");
        assertEquals(BaseResult.success(roleVos).getCode(), listBaseResult.getCode());
    }

    @Test
    public void testGetEntityIdsByUserId(){
        BaseResult<List<String>> listBaseResult = resourceInnerController.getEntityIdsByUserId("userId");
        assertEquals(BaseResult.success().getCode(), listBaseResult.getCode());
    }

    @Test
    public void testGetAreaIdsByUserId(){
        List<String> list = new ArrayList<>();
        list.add("1");
        Mockito.when(resourceQueryService.getAreaIdsByUserId("userId",null)).thenReturn(list);
        BaseResult<List<String>> result = resourceInnerController.getAreaIdsByUserId("userId",null);
        assertEquals(BaseResult.success(list).getCode(), result.getCode());
    }
}

/* Ended by AICoder, pid:w2b78041d2u7df114e260bc8408e3c892586b51b */
