package com.zte.uedm.dcdigital.application.brand.executor.Impl;

/* Started by AICoder, pid:s1b47025cdtb9a614f960a60f163cd194a71aef2 */

import com.zte.uedm.dcdigital.domain.aggregate.model.MenuEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.MenuRepository;
import com.zte.uedm.dcdigital.interfaces.web.vo.MenuVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.powermock.api.mockito.PowerMockito.when;


public class MenuQueryServiceImplTest {

    @InjectMocks
    private MenuQueryServiceImpl menuQueryService;

    @Mock
    private MenuRepository menuRepository;

    @Mock
    private AuthService authService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试根据用户ID查询菜单列表的正常情况。
     */
    @Test
    public void given_UserId_when_queryMenusByUserId_then_returnMenuList() {
        // Given
        String userId = "user123";
        MenuEntity entity1 = new MenuEntity();
        entity1.setId("1");
        entity1.setName("Menu1");

        MenuEntity entity2 = new MenuEntity();
        entity2.setId("2");
        entity2.setName("Menu2");

        List<MenuEntity> entities = Arrays.asList(entity1, entity2);

        when(authService.getUserId()).thenReturn(userId);
        when(menuRepository.selectByUserId(userId)).thenReturn(entities);

        // When
        List<MenuVo> result = menuQueryService.queryMenuList();

        // Then
        assertEquals(2, result.size());
        assertEquals("Menu1", result.get(0).getName());
        assertEquals("Menu2", result.get(1).getName());
    }

    /**
     * 测试当用户ID为空时的情况。
     */
    @Test
    public void given_NullUserId_when_queryMenusByUserId_then_returnEmptyList() {
        // Given

        // When
        List<MenuVo> result = menuQueryService.queryMenuList();

        // Then
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当用户ID不存在于数据库中时的情况。
     */
    @Test
    public void given_NonExistingUserId_when_queryMenusByUserId_then_returnEmptyList() {
        // Given
        String userId = "nonexistent";

        when(menuRepository.selectByUserId(userId)).thenReturn(Collections.emptyList());

        // When
        List<MenuVo> result = menuQueryService.queryMenuList();

        // Then
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当数据库返回空列表时的情况。
     */
    @Test
    public void given_EmptyDatabase_when_queryMenusByUserId_then_returnEmptyList() {
        // Given
        String userId = "user123";

        when(menuRepository.selectByUserId(userId)).thenReturn(Collections.emptyList());

        // When
        List<MenuVo> result = menuQueryService.queryMenuList();

        // Then
        assertTrue(result.isEmpty());
    }
}

/* Ended by AICoder, pid:s1b47025cdtb9a614f960a60f163cd194a71aef2 */
