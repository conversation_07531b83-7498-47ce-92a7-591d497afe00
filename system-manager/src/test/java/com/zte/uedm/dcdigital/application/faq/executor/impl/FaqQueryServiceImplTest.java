package com.zte.uedm.dcdigital.application.faq.executor.impl;

/* Started by AICoder, pid:f9827w3f1eh33ec148610aca8053ef958453636e */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.FaqDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.FaqVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FaqQueryServiceImplTest {

    @InjectMocks
    private FaqQueryServiceImpl faqQueryService;

    @Mock
    private FaqDomainService faqDomainService;

    private FaqVo mockFaqVo;
    private FaqQueryDto mockFaqQueryDto;

    @Before
    public void setUp() {
        // 初始化模拟对象
        mockFaqVo = new FaqVo();
        mockFaqQueryDto = new FaqQueryDto();
    }

    @Test
    public void given_FaqId_when_getById_then_returnFaqVo() {
        // 预置条件：定义一个FAQ ID
        String faqId = "1";

        // 模拟域服务的行为
        when(faqDomainService.getById(faqId)).thenReturn(mockFaqVo);

        // 执行：调用getById方法
        FaqVo result = faqQueryService.getById(faqId);

        // 验证：验证域服务的getById方法是否被调用了一次，并且参数是"1"
        verify(faqDomainService, times(1)).getById(faqId);

        // 断言：验证返回的结果是否与预期一致
        assertEquals(mockFaqVo, result);
    }

    @Test
    public void given_QueryCondition_when_queryByCondition_then_returnListOfFaqVo() {
        // 预置条件：创建一个FaqQueryDto实例

        // 模拟域服务的行为
        PageVO<FaqVo> mockPageInfo = new PageVO<>(2, Arrays.asList(mockFaqVo, mockFaqVo));
        when(faqDomainService.queryByCondition(any(FaqQueryDto.class))).thenReturn(mockPageInfo);

        // 执行：调用queryByCondition方法
        PageVO<FaqVo> result = faqQueryService.queryByCondition(mockFaqQueryDto);

        // 验证：验证域服务的queryByCondition方法是否被调用了一次，并且参数是mockFaqQueryDto
        verify(faqDomainService, times(1)).queryByCondition(mockFaqQueryDto);

        // 断言：验证返回的结果列表是否与预期一致
        assertEquals(mockPageInfo, result);
    }

    @Test
    public void given_EmptyQueryCondition_when_queryByCondition_then_returnEmptyList() {
        // 预置条件：创建一个空的FaqQueryDto实例
        FaqQueryDto emptyQueryDto = new FaqQueryDto();

        // 模拟域服务的行为
        when(faqDomainService.queryByCondition(emptyQueryDto)).thenReturn(new PageVO<>());

        // 执行：调用queryByCondition方法
        PageVO<FaqVo> result = faqQueryService.queryByCondition(emptyQueryDto);

        // 验证：验证域服务的queryByCondition方法是否被调用了一次，并且参数是emptyQueryDto
        verify(faqDomainService, times(1)).queryByCondition(emptyQueryDto);

        // 断言：验证返回的结果列表是否为空
        Assertions.assertEquals(0, result.getTotal());
    }

    @Test
    public void selectCitedList()
    {
        faqQueryService.selectCitedList(Arrays.asList("id"),1);
        verify(faqDomainService, times(1)).selectCitedList(Mockito.anyList());
    }
    /* Started by AICoder, pid:pe1768d58cd793814fc0091c305729149f41cd01 */
    @Test
    public void testExistRelatedFaq_WithValidResourceId() {
        String resourceId = "validId";
        when(faqDomainService.existRelatedFaqByResourceId(resourceId)).thenReturn(true);

        Boolean result = faqQueryService.existRelatedFaq(resourceId);

        assertNotNull(result);
        assertTrue(result);
        verify(faqDomainService).existRelatedFaqByResourceId(resourceId);
    }
    /* Ended by AICoder, pid:pe1768d58cd793814fc0091c305729149f41cd01 */
}

/* Ended by AICoder, pid:f9827w3f1eh33ec148610aca8053ef958453636e */