package com.zte.uedm.dcdigital.domain.service.impl;/* Started by AICoder, pid:242360fa2csc98a147680a1a80611e8ef8341021 */
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PermissionResourceEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PermissionMapper;
import com.zte.uedm.dcdigital.interfaces.web.vo.PermissionResourceVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserPermissionServiceImplTest {

    @InjectMocks
    private UserPermissionServiceImpl userPermissionService;

    @Mock
    private PermissionMapper permissionMapper;

    @Before
    public void setUp() {
    }

    @Test
    public void testSelectPermissionResourceByEmployeeId() {
        // Arrange
        String employeeId = "testEmployeeId";
        PermissionResourceEntity permissionResourceEntity1=new PermissionResourceEntity();
        permissionResourceEntity1.setId("1");
        permissionResourceEntity1.setId("2");
        PermissionResourceEntity permissionResourceEntity2=new PermissionResourceEntity();
        List<PermissionResourceEntity> entityList = new ArrayList<>();
        entityList.add(permissionResourceEntity1);
        entityList.add(permissionResourceEntity2);

        when(permissionMapper.selectPermissionResourceByEmployeeId(employeeId)).thenReturn(entityList);

        // Act
        List<PermissionResourceVo> result = userPermissionService.selectPermissionResourceByEmployeeId(employeeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
    }
}

/* Ended by AICoder, pid:242360fa2csc98a147680a1a80611e8ef8341021 */