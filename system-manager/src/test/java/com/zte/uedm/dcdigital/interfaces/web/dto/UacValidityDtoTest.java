package com.zte.uedm.dcdigital.interfaces.web.dto;/* Started by AICoder, pid:85d70d52b9g9f2f14d2d092c2065aa6d6f283be1 */
import com.zte.uedm.dcdigital.interfaces.web.dto.UacValidityDto;
import static org.junit.Assert.*;

import org.junit.Test;

public class UacValidityDtoTest {

    @Test
    public void testConstructorAndGetters() {
        // 创建一个 UacValidityDto 对象并设置所有字段
        UacValidityDto dto = new UacValidityDto();
        dto.setAccountId("123456");
        dto.setToken("encrypted-token");
        dto.setFailureCycle("86400"); // 一天的秒数
        dto.setStartValidTime("2023-10-01 00:00:00");
        dto.setMessage("Test message");
        dto.setResourceId("all");
        dto.setVlanId("VLAN-123");
        dto.setExpiredTime("2023-10-02 00:00:00");
        dto.setDelay("3600");
        dto.setRemain("7200");

        // 施加断言检查 DTO 是否具有正确的值
        assertEquals("123456", dto.getAccountId());
        assertEquals("encrypted-token", dto.getToken());
        assertEquals("86400", dto.getFailureCycle());
        assertEquals("2023-10-01 00:00:00", dto.getStartValidTime());
        assertEquals("Test message", dto.getMessage());
        assertEquals("all", dto.getResourceId());
        assertEquals("VLAN-123", dto.getVlanId());
        assertEquals("2023-10-02 00:00:00", dto.getExpiredTime());
        assertEquals("3600", dto.getDelay());
        assertEquals("7200", dto.getRemain());
    }

    @Test
    public void testToString() {
        // 创建一个 UacValidityDto 对象并设置所有字段
        UacValidityDto dto = new UacValidityDto();
        dto.setAccountId("123456");
        dto.setToken("encrypted-token");
        dto.setFailureCycle("86400");
        dto.setStartValidTime("2023-10-01 00:00:00");
        dto.setMessage("Test message");
        dto.setResourceId("all");
        dto.setVlanId("VLAN-123");
        dto.setExpiredTime("2023-10-02 00:00:00");
        dto.setDelay("3600");
        dto.setRemain("7200");

        // 施加断言检查 toString 方法输出是否合理
        String actualString = dto.toString();
        assertNotNull(actualString);
        assertNotEquals("", actualString);

        // 检查字符串是否包含预期的字段值
        assertTrue(actualString.contains("accountId=123456"));
        assertTrue(actualString.contains("token=encrypted-token"));
        assertTrue(actualString.contains("failureCycle=86400"));
        assertTrue(actualString.contains("startValidTime=2023-10-01 00:00:00"));
        assertTrue(actualString.contains("message=Test message"));
        assertTrue(actualString.contains("resourceId=all"));
        assertTrue(actualString.contains("vlanId=VLAN-123"));
        assertTrue(actualString.contains("expiredTime=2023-10-02 00:00:00"));
        assertTrue(actualString.contains("delay=3600"));
        assertTrue(actualString.contains("remain=7200"));
    }
}

/* Ended by AICoder, pid:85d70d52b9g9f2f14d2d092c2065aa6d6f283be1 */