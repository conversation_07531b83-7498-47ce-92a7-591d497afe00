package com.zte.uedm.dcdigital.interfaces.web.controller;/* Started by AICoder, pid:3d9ea5d08934ec9148ba09d760c465812af9ff73 */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.gateway.UacApiService;
import com.zte.uedm.dcdigital.domain.utils.AuthUtil;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoQueryDto;
import com.zte.uedm.dcdigital.sdk.system.AuthConstant;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UacApiAportalControllerTest {

    @InjectMocks
    private UacApiAportalController controller;

    @Mock
    private UacApiService uacApiService;

    @Mock
    private HttpServletRequest request;

    @Before
    public void setUp() {
    }

    /**
     * 测试模糊查询UAC用户信息。
     */
    @Test
    public void given_queryCondition_when_searchUser_then_returnBaseResult() throws BusinessException {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("testKey");

        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn("testToken");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn("testAccountId");

        List<UacUserInfoDto> userInfoList = Collections.singletonList(new UacUserInfoDto());
        when(uacApiService.searchUser(anyList(), anyString(), anyString())).thenReturn(userInfoList);

        BaseResult<Object> result = controller.searchUser(queryDto, request);
        assertEquals(BaseResult.success(userInfoList).getCode(), result.getCode());

        verify(uacApiService, times(1)).searchUser(Collections.singletonList("testKey"), "testToken", "testAccountId");
    }

    /**
     * 测试空查询条件返回空列表。
     */
    @Test
    public void given_emptyQueryCondition_when_searchUser_then_returnEmptyList() {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("");
        // 模拟 AuthUtil.getAuthValueFromRequest 返回有效的 token 和 accountId
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn("validToken");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn("validAccountId");
        BaseResult<Object> result = controller.searchUser(queryDto, request);
        assertEquals(BaseResult.success(Collections.emptyList()).getCode(), result.getCode());
    }

    /**
     * 测试无效令牌时抛出BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidToken_when_searchUser_then_throwBusinessException() {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("testKey");

        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn(null);
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn(null);

        controller.searchUser(queryDto, request);
    }
}

/* Ended by AICoder, pid:3d9ea5d08934ec9148ba09d760c465812af9ff73 */