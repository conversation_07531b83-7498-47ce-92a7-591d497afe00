/* Started by AICoder, pid:z4e36vbc5dm252c14f130927d0ff8536f9405da7 */
package com.zte.uedm.dcdigital.application.material;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.PriceHistoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.ProcurementQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.SelectionFormQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.PriceHistoryVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.ProcurementCostVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.SelectionFormVo;

/**
 * 采购查询服务接口。
 * 提供对采购成本和价格历史记录的查询功能。
 */
public interface ProcurementQueryService {

    /**
     * 根据选型单信息查询采购成本。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 采购成本视图对象，包含查询结果
     */
    ProcurementCostVo queryByModelSelection(ProcurementQueryDto queryDto);

    /**
     * 查询价格历史记录。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 分页的价格历史记录视图对象列表
     */
    PageVO<PriceHistoryVo> queryPriceHistory(PriceHistoryQueryDto queryDto);

    PageVO<SelectionFormVo> querySelectionFormByCondition(SelectionFormQueryDto queryDto);
}
/* Ended by AICoder, pid:z4e36vbc5dm252c14f130927d0ff8536f9405da7 */