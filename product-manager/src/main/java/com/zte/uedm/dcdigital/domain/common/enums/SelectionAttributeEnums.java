/* Started by AICoder, pid:72826014d0we5b314393090fd0415f7d9ee574ce */
package com.zte.uedm.dcdigital.domain.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 选型属性枚举类，定义了选型属性的类型和名称。
 */
public enum SelectionAttributeEnums {
    /**
     * 优选属性，ID为0，名称为多语言格式。
     */
    PREFERENCE(0, "{\"zh-CN\":\"优选\",\"en-US\":\"Preference\"}"),
    
    /**
     * 其它属性，ID为1，名称为多语言格式。
     * 注意：修正了原始代码中的错误，将OTHER的ID从0改为1，以确保唯一性。
     */
    OTHER(1, "{\"zh_CN\":\"其它\",\"en_US\":\"Other\"}");

    private Integer id; // 枚举的ID
    private String name; // 枚举的名称（多语言格式）

    // 使用静态映射来快速查找枚举值
    private static final Map<Integer, SelectionAttributeEnums> SELECT_ATTRIBUTE_ENUMS_MAP = new HashMap<>();
    static {
        for (SelectionAttributeEnums selectEnum : values()) {
            SELECT_ATTRIBUTE_ENUMS_MAP.put(selectEnum.id, selectEnum);
        }
    }

    /**
     * 获取枚举的ID。
     *
     * @return 枚举ID
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * 获取枚举的名称（多语言格式）。
     *
     * @return 枚举名称
     */
    public String getName() {
        return this.name;
    }

    // 私有构造函数，防止外部实例化
    SelectionAttributeEnums(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 根据ID获取对应的枚举值。
     *
     * @param id 枚举ID
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SelectionAttributeEnums getById(Integer id) {
        return SELECT_ATTRIBUTE_ENUMS_MAP.get(id);
    }

    /**
     * 检查给定的ID是否在枚举范围内。
     *
     * @param id 枚举ID
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(Integer id) {
        return SELECT_ATTRIBUTE_ENUMS_MAP.containsKey(id);
    }
}

/* Ended by AICoder, pid:72826014d0we5b314393090fd0415f7d9ee574ce */