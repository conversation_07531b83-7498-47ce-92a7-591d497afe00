package com.zte.uedm.dcdigital.domain.common.constant;

public class PdmConstants {


    //系统编码请求头
    public static final String X_SYSTEM_CODE = "X-System-Code";

    // token请求头
    public static final String X_AUTH_VALUE = "X-Auth-Value";

    public static final String QUERYVER = "v1";

    public static final String QUERYTYPE = "P0000";

    public static final String BLOCK = "P0001";


    public static final String QUERYTYPE2 = "P0002";

    public static final String SUCCESS = "0000";

    // 分类基础信息
    public static final String CATEGORYQUERYURL = "/zte-plm-iproduct-category/category/v1/query";

    //查询基础报价项-中间报价项-机型关系信息
    public static final String SBOMRELATTION = "/zte-plm-iproduct-sbom/sbomRelation/v1/getParent";


    // 分类关系层级
    public static final String  CATEGORYRELATION = "/zte-plm-iproduct-category/relation/v1/query";

    //B0801 产品下层关系获取
    public static final String ACQUIRE = "/zte-plm-iproduct-category/acquire/v1/acquire";

    //B0812  sbom详细信息查询
    public static final String SBOMINFO = "/zte-plm-iproduct-sbom/sbom/v1/querysbomdetailinfo";

    // B0811 SBOM基础信息查询服务
    public static final String QUERYINFO = "/zte-plm-iproduct-sbom/sbom/v1/queryinfo";

    // B8018 被使用销售件查询服务
    public static final String BASICQUERY = "/zte-plm-iproduct-sbom/sbom/used/basicQuery";

    //B0815 SBOM下层关系查询
    public static final String SBOMDIRECTCOMPOSINFO = "/zte-plm-iproduct-sbom/sbomRelation/v1/getSbomDirectComposInfo";

    // 当前分页条数
    public static final int PAGESIZE = 100;

    // 当前页
    public static final int PAGE = 1;

    // 按PDM物料编号查询
    public static final String QUERY_SBOM_NO = "1";

    // 按PDM生产编号查询
    public static final String QUERY_PART_NO = "2";

    // 按PDM物料编号查询query_type
    public static final String QUERY_SBOM_TYPE = "Q0001";

    // 	销售件类别
    public static final String SBOM_TYPE = "基础报价项";


    // 查询类型：基础报价项获取直接下层
    public static final String QUERY_SALES_TYPE = "SG0001";
}
