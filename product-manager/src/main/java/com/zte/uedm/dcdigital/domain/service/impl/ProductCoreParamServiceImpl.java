package com.zte.uedm.dcdigital.domain.service.impl;

/* Started by AICoder, pid:j9676y0170zb9df14861095be050170ade6697f5 */
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCoreParamStatusCode;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCoreParamEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCoreParamRepository;
import com.alibaba.excel.EasyExcel;
import com.zte.uedm.dcdigital.common.util.FileDownloadUtil;
import com.zte.uedm.dcdigital.domain.service.ProductCoreParamService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProductCoreParamConvert;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamDeleteDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCoreParamDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCoreParamVo;
import com.zte.uedm.dcdigital.sdk.system.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamTemplateDto;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/* Started by AICoder, pid:q4ae9q751eu686d14e590a51612ebe16a240e014 */
/**
 * 产品核心参数服务实现类。
 */
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductCoreParamServiceImpl implements ProductCoreParamService {

    @Autowired
    private ProductCoreParamRepository productCoreParamRepository;

    @Autowired
    private AuthService authService;

    /**
     * 查询产品核心参数列表。
     *
     * @param queryDto 查询条件数据传输对象。
     * @return 包含查询结果的分页信息。
     */
    @Override
    public PageVO<ProductCoreParamVo> queryProductCoreParamList(ProductCoreParamQueryDto queryDto) {
        //使用了.trim()方法来去除前后多余的空白字符
        if (queryDto.getParamGroupL1() != null) {
            queryDto.setParamGroupL1(queryDto.getParamGroupL1().replaceAll("\\s+", " ").trim());
        }
        if (queryDto.getParamGroupL2() != null) {
            queryDto.setParamGroupL2(queryDto.getParamGroupL2().replaceAll("\\s+", " ").trim());
        }
        if (queryDto.getMainParam() != null) {
            queryDto.setMainParam(queryDto.getMainParam().replaceAll("\\s+", " ").trim());
        }
        return productCoreParamRepository.queryProductCoreParamList(queryDto);
    }

    /**
     * 添加一个新的产品核心参数。
     *
     * @param addDto 新的产品核心参数数据传输对象。
     * @return 操作是否成功。
     */
    @Override
    public Boolean addProductCoreParam(ProductCoreParamAddDto addDto) {
        ProductCoreParamEntity productCoreParamEntity = ProductCoreParamConvert.INSTANCE.productCoreParamDtoToEntity(addDto);
        createParam(productCoreParamEntity, true);
        return productCoreParamRepository.add(productCoreParamEntity);
    }

    /**
     * 根据ID获取产品核心参数详情。
     *
     * @param id 产品核心参数的ID。
     * @return 产品核心参数详细信息，如果未找到则抛出异常。
     */
    @Override
    public ProductCoreParamDetailVo getDetail(String id) {
        ProductCoreParamEntity detail = productCoreParamRepository.getDetail(id);
        if (detail == null) {
            log.error("ProductCoreParam does not exist.");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        return ProductCoreParamConvert.INSTANCE.productCoreParamEntityToDetailVo(detail);
    }

    /**
     * 编辑一个已有的产品核心参数。
     *
     * @param editDto 需要编辑的产品核心参数数据传输对象。
     * @return 操作是否成功。
     */
    @Override
    public Boolean editProductCoreParam(ProductCoreParamEditDto editDto) {
        ProductCoreParamEntity detail = productCoreParamRepository.getDetail(editDto.getId());
        if (detail == null) {
            log.error("ProductCoreParam does not exist.");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        createParam(detail, false);
        BeanUtils.copyProperties(editDto, detail);
        return productCoreParamRepository.update(detail);
    }

    /**
     * 批量删除产品核心参数。
     *
     * @param deleteDto 包含需要删除的产品核心参数ID列表的数据传输对象。
     * @return 操作是否成功。
     */
    @Override
    public Boolean deletByIds(ProductCoreParamDeleteDto deleteDto) {
        return productCoreParamRepository.updateByIds(deleteDto.getIds());
    }

    @Override
    public void export(ProductCoreParamTemplateDto templateDto, HttpServletResponse response) {
        // 设置响应参数
        setResponseHeader(response, "product-core-param.xlsx_");
        //查询符合筛选条件的数据合集
        ProductCoreParamQueryDto queryDto=new ProductCoreParamQueryDto();
        queryDto.setProductCategoryId(templateDto.getProductCategoryId());
        List<ProductCoreParamVo> dataList= productCoreParamRepository.selectProductCoreParamList(queryDto);
        if (CollectionUtils.isEmpty(dataList)){
            log.info("get productCoreParamList data is empty");
            return;
        }
        //若勾选了核心参数列表id,则需要导出指定列表数据
        if (CollectionUtils.isNotEmpty(templateDto.getIds())){
            dataList=dataList.stream().filter(item->templateDto.getIds().contains(item.getId())).collect(Collectors.toList());
        }
        try {
            // 获取模板文件输入流
            InputStream templateStream = new ClassPathResource(GlobalConstants.TEMPLATE_PATH + File.separator + GlobalConstants.EXPORT_TEMPLATE_CORE_PARAM_FILE_NAME).getInputStream();
            // 使用EasyExcel写入数据到HttpServletResponse
            EasyExcel.write(response.getOutputStream()).registerWriteHandler(setStyle()).withTemplate(templateStream).sheet().doFill(dataList);
        } catch (IOException e) {
            log.error("export productCoreParamList data is filed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    // 设置响应头通用方法
    private void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            String fileNameStr = fileName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
            String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        } catch (Exception e) {
            log.error("set response header error", e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
    }
    //设置导出excel文件部分内容样式
    private HorizontalCellStyleStrategy setStyle() {
        // 定义样式：自动换行
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 关键：开启自动换行
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Microsoft YaHei"); // 字体
        writeFont.setFontHeightInPoints((short) 12);// 字体大小
        contentWriteCellStyle.setWriteFont(writeFont);
        // 注册样式策略（全局生效）
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                null, // 头样式（默认）
                contentWriteCellStyle // 内容样式（自动换行）
        );
        return styleStrategy;
    }

    /**
     * 设置产品核心参数实体的创建和更新信息。
     *
     * @param productCoreParamEntity 产品核心参数实体。
     * @param isAdd 是否为添加操作。
     */
    private void createParam(ProductCoreParamEntity productCoreParamEntity, boolean isAdd) {
        // 获取当前用户ID和时间
        UacUserInfoDto uacUser = authService.getCurUacUser();
        String currentTime = DateTimeUtils.getCurrentTime();
        productCoreParamEntity.setUpdateBy(uacUser.getId());
        productCoreParamEntity.setUpdateTime(currentTime);
        if (isAdd) {
            productCoreParamEntity.setId(UUID.randomUUID().toString());
            productCoreParamEntity.setCreateBy(uacUser.getId());
            productCoreParamEntity.setCreateTime(currentTime);
            productCoreParamEntity.setDelFlag(GlobalConstants.ZERO);
        }
    }

    /* Started by AICoder, pid:v49b7u57f7rfb8b1432d09e4303287472fe8458d */
    /**
     * 导入产品核心参数。
     *
     * @param productCategoryId 产品小类id。
     * @param file 待导入的excel文件。
     */
    @Override
    @Transactional
    public int importTemplate(String productCategoryId,FormDataMultiPart file) {
        /* Started by AICoder, pid:x58b3wb485ud1d914ebd08cac0246e25178027a7 */
        // 1. 获取输入流（直接使用已提取的InputStream，避免重复获取）
        try (InputStream inputStream = getExcelInputStream(file)) {
            // 添加日志，确保InputStream有效
            log.info("InputStream obtained successfully.");
            // 2. 读取Excel数据（明确指定Sheet和关键配置）
            List<ProductCoreParamTemplateDto> templateDtoList = EasyExcel.read(inputStream)
                    .head(ProductCoreParamTemplateDto.class)
                    .sheet(GlobalConstants.ZERO) // 明确指定第一个Sheet
                    .headRowNumber(GlobalConstants.ONE)
                    .doReadSync();
            // 检查templateDtoList是否为空
            if (CollectionUtils.isEmpty(templateDtoList)) {
                log.warn("No data found in the Excel file or data read failed.");
                throw new BusinessException(ProductCoreParamStatusCode.DATA_NOT_FOUND);
            }
            // 记录读取的数据
            log.info("Data read from Excel: {}", templateDtoList);
            /* Ended by AICoder, pid:x58b3wb485ud1d914ebd08cac0246e25178027a7 */
            validateFields(templateDtoList);
            //为导入数据设置主键等属性
            setOtherParam(productCategoryId,templateDtoList);
            ProductCoreParamQueryDto queryDto=new ProductCoreParamQueryDto();
            queryDto.setProductCategoryId(productCategoryId);
            List<ProductCoreParamVo> productCoreParamList= productCoreParamRepository.selectProductCoreParamList(queryDto);
            if (CollectionUtils.isNotEmpty(productCoreParamList)){
                //如果当前产品小类有之前的核心参数就逻辑删除
                List<String> ids=productCoreParamList.stream().map(ProductCoreParamVo::getId).collect(Collectors.toList());
                //逻辑删除原产品小类所属核心参数
                productCoreParamRepository.batchUpdateDelFlag(ids);
            }
            // 插入数据并返回成功条数
            return insertProductCoreParam(templateDtoList);
        } catch (IOException e) {
            log.error("Failed to read Excel file", e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
    }


    // 获取输入流
    private InputStream getExcelInputStream(FormDataMultiPart file) {
        FormDataBodyPart part = file.getField("file");
        if (part == null) {
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        return part.getValueAs(InputStream.class);
    }

    /* Started by AICoder, pid:1a6dc68b89q77d6141da098a60d7011970578f18 */
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 定义模板文件路径
            String configFilepath = GlobalConstants.TEMPLATE_PATH + File.separator + GlobalConstants.TEMPLATE_CORE_PARAM_FILE_NAME;

            // 如果是类路径资源，使用ClassPathResource获取路径
            ClassPathResource resource = new ClassPathResource(configFilepath);
            String filePath = resource.getFile().getAbsolutePath();

            // 下载文件
            FileDownloadUtil.downloadFile(filePath, response, GlobalConstants.TEMPLATE_CORE_PARAM_FILE_NAME);
        } catch (Exception e) {
            log.error("Error exporting template:", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    /**
     * 下载导入部门成员模板
     * */
    @Override
    public void downloadDeptUserTemplate(HttpServletResponse response) {
        try {
            // 定义模板文件路径
            String configFilepath = GlobalConstants.TEMPLATE_PATH + File.separator + GlobalConstants.IMPORT_DEPT_USER;

            // 如果是类路径资源，使用ClassPathResource获取路径
            ClassPathResource resource = new ClassPathResource(configFilepath);
            String filePath = resource.getFile().getAbsolutePath();

            // 下载文件
            FileDownloadUtil.downloadFile(filePath, response, GlobalConstants.IMPORT_DEPT_USER);
        } catch (Exception e) {
            log.error("Error exporting template:", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    /* Ended by AICoder, pid:1a6dc68b89q77d6141da098a60d7011970578f18 */

    // 校验必填字段
    private void validateFields(List<ProductCoreParamTemplateDto> templateDtoList) {
        //因为easy-excel只能够指定第一行为标题行,然后从第二行(模板是提示行)开始读数据，所以要去掉第2行数据
        templateDtoList.remove(0);
        for (int i = 0; i < templateDtoList.size(); i++) {
            // 标题行占2行，数据从第3行开始
            int lineNumber = i + GlobalConstants.THREE;
            ProductCoreParamTemplateDto dto = templateDtoList.get(i);
            if (StringUtils.isBlank(dto.getMainParam())) {
                log.error("data in line:{} is incomplete", lineNumber);
                String msg = I18nUtil.getI18n(GlobalConstants.CHECK_FAILED_LINE) + lineNumber+","+I18nUtil.getI18n(GlobalConstants.CHECK_MAIN_PARAM_BLANK);
                throw new BusinessException(-1, msg);
            }
            //校验核心参数必填以及字符个数
            validateField(dto.getMainParam(), GlobalConstants.MAIN_PARAM_LENGTH, GlobalConstants.MAin_PARAM, lineNumber);
            validateField(dto.getParamGroupL1(), GlobalConstants.NAME_LENGTH, GlobalConstants.PARAM_GROUP_L1, lineNumber);
            validateField(dto.getParamGroupL2(), GlobalConstants.NAME_LENGTH, GlobalConstants.PARAM_GROUP_L2, lineNumber);
            validateField(dto.getParamRemark(), GlobalConstants.DESCRIPTION_LENGTH, GlobalConstants.PARAM_REMARK, lineNumber);
        }
    }
    /**
     * 辅助方法用于验证单个字段是否符合要求。
     */
    private void validateField(String fieldValue, int maxLength, String fieldName, int lineNumber) {
        if (StringUtils.isNotBlank(fieldValue) && fieldValue.length() > maxLength) {
            log.error("data in line:{} is {} length > {}", lineNumber, fieldName, maxLength);
            String msg = null;
            if (GlobalConstants.MAin_PARAM.equals(fieldName)){
                msg = I18nUtil.getI18n(GlobalConstants.CHECK_FAILED_LINE) + lineNumber+","+I18nUtil.getI18n(GlobalConstants.CHECK_MAIN_PARAM);
            }
            else if (GlobalConstants.PARAM_GROUP_L1.equals(fieldName)){
                msg = I18nUtil.getI18n(GlobalConstants.CHECK_FAILED_LINE) + lineNumber+","+I18nUtil.getI18n(GlobalConstants.CHECK_PARAM_L1);
            }
            else if (GlobalConstants.PARAM_GROUP_L2.equals(fieldName)){
                msg = I18nUtil.getI18n(GlobalConstants.CHECK_FAILED_LINE) + lineNumber+","+I18nUtil.getI18n(GlobalConstants.CHECK_PARAM_L2);
            }
            else if (GlobalConstants.PARAM_REMARK.equals(fieldName)){
                msg = I18nUtil.getI18n(GlobalConstants.CHECK_FAILED_LINE) + lineNumber+","+I18nUtil.getI18n(GlobalConstants.CHECK_PARAM_REMARK);
            }
            throw new BusinessException(-1, msg);
        }
    }

    /**
     * @param productCategoryId 产品小类id
     * @param templateDtoList 模板数据集合
     * 为导入模板数据补充属性
     * */
    private void setOtherParam(String productCategoryId,List<ProductCoreParamTemplateDto> templateDtoList){
        //反转列表顺序（因为数据默认倒序展示，为了保持与excel数据一致）
        //Collections.reverse(templateDtoList);
        String currentUserId=authService.getUserId();
        for (ProductCoreParamTemplateDto templateDto : templateDtoList) {
            String id= UUID.randomUUID().toString();
            templateDto.setId(id);
            //设置未删除标识0
            templateDto.setDelFlag(GlobalConstants.ZERO);
            templateDto.setProductCategoryId(productCategoryId);
            templateDto.setCreateBy(currentUserId);
            templateDto.setUpdateBy(currentUserId);
            templateDto.setCreateTime(DateTimeUtils.getCurrentTime());
            templateDto.setUpdateTime(DateTimeUtils.getCurrentTime());
        }
    }
    // 插入方法
    private int insertProductCoreParam(List<ProductCoreParamTemplateDto> addDtoList) {
        // 1. DTO转Entity
        List<ProductCoreParamEntity> productCoreParamEntityList = ProductCoreParamConvert.INSTANCE.convertToProductCoreParamListEntity(addDtoList);
        // 2. 保存到数据库
        return productCoreParamRepository.addProductCoreParamBatch(productCoreParamEntityList);
    }
    /* Ended by AICoder, pid:v49b7u57f7rfb8b1432d09e4303287472fe8458d */
}
/* Ended by AICoder, pid:q4ae9q751eu686d14e590a51612ebe16a240e014 */
/* Ended by AICoder, pid:j9676y0170zb9df14861095be050170ade6697f5 */
