/* Started by AICoder, pid:c429f44760n679e14bfc08a980ce3c54f6d15a62 */
package com.zte.uedm.dcdigital.domain.model.product.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProductGroupCustomSortEntity {

    /**
     * 分组父ID
     */
    private String parentId;

    /**
     * 主键
     */
    private String id;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 序号
     */
    private Integer serialNum;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建用户
     */
    private String createBy;

    /**
     * 更新用户
     */
    private String updateBy;
}

/* Ended by AICoder, pid:c429f44760n679e14bfc08a980ce3c54f6d15a62 */