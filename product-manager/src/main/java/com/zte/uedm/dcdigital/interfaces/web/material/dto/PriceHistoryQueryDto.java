/* Started by AICoder, pid:p216ab44760d858142570bdd50cf807efc372865 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.PriceCategoryEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;

/**
 * 采购成本添加数据传输对象 (DTO)。
 * 用于封装采购成本添加请求的参数，并提供参数校验功能。
 */
@Getter
@Setter
@ToString
@Slf4j
public class PriceHistoryQueryDto {

    /**
     * 选型单id，不能为空。
     */
    @NotBlank(message = "选型单id不能为空")
    private String lectotypeId;

    /**
     * 选型单类型，不能为空。1-招标 2-指定。
     */
    @NotBlank(message = "选型单类型不能为空")
    private String lectotypeType;

    /**
     * 价格分类。
     */
    @NotBlank(message = "价格分类不能为空")
    private String priceCategory;

    /**
     * 分页查询的页码，默认为1。
     */
    private Integer pageNum;

    /**
     * 每页显示的记录数，默认为10。
     */
    private Integer pageSize;

    /**
     * 参数校验方法。
     * 校验传入的参数是否合法，包括必填字段、价格分类的有效性以及分页参数的默认值设置。
     */
    public void parameterVerification() {
        // 校验bean字段NotEmpty
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[PriceHistoryQueryDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验价格分类是否在有效范围内
        if (!PriceCategoryEnums.isInRange(priceCategory)) {
            log.error("Invalid parameter priceCategory: {} Not within the agreed scope", priceCategory);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 设置分页参数的默认值
        if (pageNum == null || pageSize == null) {
            pageNum = GlobalConstants.ONE;
            pageSize = GlobalConstants.TEN;
        }
    }
}
/* Ended by AICoder, pid:p216ab44760d858142570bdd50cf807efc372865 */