/* Started by AICoder, pid:39375g4bfe528ee147f309946014f95ed980ecf0 */
package com.zte.uedm.dcdigital.application.material.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.config.ScmPropertiesConfig;
import com.zte.uedm.dcdigital.domain.common.enums.MaterialStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 物料维护查询服务实现类。
 * 该类实现了MaterialMaintenanceQueryService接口，提供了根据审批ID获取物料信息的功能。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MaterialMaintenanceQueryServiceImpl implements MaterialMaintenanceQueryService {

    /**
     * 物料领域服务，用于与领域层交互。
     */
    @Autowired
    private MaterialDomainService materialDomainService;

    @Autowired
    private ScmPropertiesConfig scmPropertiesConfig;

    private final static Integer PAGE_NUM = 1;
    private final static Integer PAGE_SIZE = 10;


    /* Started by AICoder, pid:997c6w146dmfc2214f720aef80c2241adc90b395 */
    @Override
    public List<MaterialVersionVo> queryMaterialVersionById(String id) {
        List<MaterialVersionVo> voList=materialDomainService.queryMaterialVersionById(id);
        // 按 version 从大到小排序
        voList.sort((v1, v2) -> {
            Integer ver1 = Integer.parseInt(v1.getVersion());
            Integer ver2 = Integer.parseInt(v2.getVersion());
            return ver2.compareTo(ver1); // 降序
        });
        return voList;
    }
    /* Started by AICoder, pid:8c3ccnd092ydc1d1458c0a59501f311d7c235df4 */
    @Override
    public MaterialDetailVo queryMaterialDetailsById(String id) {
        MaterialDetailVo detailVo = materialDomainService.queryMaterialDetails(id);
        return detailVo;
    }

    @Override
    public List<String> queryAllSalesStatus() {
        return materialDomainService.queryAllSalesStatus();
    }

    @Override
    public PageVO<MaterialVo> pagingQueryMaterialByCondition(MaterialConditionQueryDto dto) {
        return materialDomainService.pagingQueryByCondition(dto);
    }

    @Override
    public List<MaterialVo> queryMaterialByCondition(MaterialConditionQueryDto dto) {
        return materialDomainService.queryMaterialByCondition(dto);
    }

    /* Started by AICoder, pid:a4604b0af0qea6d14c480bf3107ad61f69b790d8 */
    @Override
    public List<IdNameBean> queryAllMaterialStatus() {
        return Arrays.stream(MaterialStatusEnums.values())
                .map(e -> new IdNameBean(e.getId(), I18nUtil.getI18nFromString(e.getName())))
                .collect(Collectors.toList());
    }

    /**
     * 查询所有采购模式。
     *
     * @return 包含所有采购模式的列表。
     */
    @Override
    public List<IdNameBean> queryAllPurchaseMode() {

        return Arrays.stream(PurchaseModeEnums.values())
                .map(e -> new IdNameBean(e.getId(), I18nUtil.getI18nFromString(e.getName())))
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:a4604b0af0qea6d14c480bf3107ad61f69b790d8 */
    /* Ended by AICoder, pid:8c3ccnd092ydc1d1458c0a59501f311d7c235df4 */
    /* Ended by AICoder, pid:997c6w146dmfc2214f720aef80c2241adc90b395 */

    /* Started by AICoder, pid:c16f0q2425u265e144b709ee50d0d23dddb69293 */
    /**
     * 物料模糊查询
     * @param materialFuzzyDto
     * @return
     */
    @Override
    public PageVO<MaterialFuzzyVo> queryMaterialFuzzy(MaterialFuzzyDto materialFuzzyDto) {
        if (ObjectUtils.isEmpty(materialFuzzyDto.getPageNum()) || ObjectUtils.isEmpty(materialFuzzyDto.getPageSize())){
            materialFuzzyDto.setPageSize(PAGE_SIZE);
            materialFuzzyDto.setPageNum(PAGE_NUM);
        }
        PageHelper.startPage(materialFuzzyDto.getPageNum(), materialFuzzyDto.getPageSize());
        List<MaterialFuzzyVo> materialFuzzyVos = materialDomainService.queryMaterialFuzzy(materialFuzzyDto);
        PageInfo<MaterialFuzzyVo> pageInfo = new PageInfo<>(materialFuzzyVos);
        List<MaterialFuzzyVo> infoList = pageInfo.getList();
        infoList.forEach(ma->{
            ma.setMaterialStatus(getMaterialStatusName(ma.getMaterialStatus()));
            ma.setPurchaseMode(getPurchaseModeName(ma.getPurchaseMode()));
        });
        return new PageVO<>(pageInfo.getTotal(), infoList);
    }

    /**
     * 物料精确查询
     * @param materialAccurateDto
     * @return
     */
    @Override
    public PageVO<MaterialFuzzyVo> queryMaterialAccurate(MaterialAccurateDto materialAccurateDto) {
        if (ObjectUtils.isEmpty(materialAccurateDto.getPageNum()) || ObjectUtils.isEmpty(materialAccurateDto.getPageSize())){
            materialAccurateDto.setPageSize(PAGE_SIZE);
            materialAccurateDto.setPageNum(PAGE_NUM);
        }
        PageHelper.startPage(materialAccurateDto.getPageNum(), materialAccurateDto.getPageSize());
        List<MaterialFuzzyVo> materialFuzzyVos = materialDomainService.queryMaterialAccurate(materialAccurateDto);
        PageInfo<MaterialFuzzyVo> pageInfo = new PageInfo<>(materialFuzzyVos);
        List<MaterialFuzzyVo> infoList = pageInfo.getList();
        infoList.forEach(ma->{
                    ma.setMaterialStatus(getMaterialStatusName(ma.getMaterialStatus()));
                    ma.setPurchaseMode(getPurchaseModeName(ma.getPurchaseMode()));
        });
        return new PageVO<>(pageInfo.getTotal(), infoList);
    }
    /* Ended by AICoder, pid:c16f0q2425u265e144b709ee50d0d23dddb69293 */

    private String getPurchaseModeName(String purchaseMode) {
        PurchaseModeEnums byId = PurchaseModeEnums.getById(purchaseMode);
        if (byId != null){
            return I18nUtil.getI18nFromString(byId.getName());
        }
        return purchaseMode;
    }

    private String getMaterialStatusName(String materialStatus) {
        MaterialStatusEnums byId = MaterialStatusEnums.getById(materialStatus);
        if (byId != null) {
            return I18nUtil.getI18nFromString(byId.getName());
        }
        return materialStatus;
    }

    @Override
    public List<MaterialWithExtendInfoVo> queryMaterialByIds(List<String> ids) {
        return materialDomainService.queryByIds(ids);
    }

    @Override
    public PageVO<MaterialVo> pagingFuzzyQuery(MaterialFuzzyQueryDto queryDto) {
        return materialDomainService.fuzzyQuery(queryDto);
    }

    /* Started by AICoder, pid:818667e030d17c31431d08ae31ecdd1f173990c1 */

    @Override
    public List<MaterialSpecificationVo> getSpecification(String productionCode) {
        // 初始化返回列表
        List<MaterialSpecificationVo> list = new ArrayList<>();

        // 获取与生产编码相关的销售状态信息列表
        List<ScmSpecificationResponseVo.BO> boList = getSalesStatusName(productionCode);

        // 检查获取到的列表是否为空或无效
        if (boList != null && !boList.isEmpty()) {
            for (ScmSpecificationResponseVo.BO bo : boList) {
                // 创建一个新的物料规格信息对象
                MaterialSpecificationVo materialSpecificationVo = new MaterialSpecificationVo();

                // 设置生产编码
                materialSpecificationVo.setProductionCode(bo.getItemNo());

                // 设置品牌名称
                materialSpecificationVo.setBrandName(bo.getBrandName());

                // 如果PDF规格文件路径不为空，则提取文件名
                if (StringUtils.isNotBlank(bo.getSpecpdfFile())) {
                    // 设置PDF规格文件路径
                    materialSpecificationVo.setSpecpdfFile(bo.getSpecpdfFile());

                    int lastIndex = bo.getSpecpdfFile().lastIndexOf('/');
                    if (lastIndex != -1 && lastIndex < bo.getSpecpdfFile().length() - 1) {
                        materialSpecificationVo.setSpecpdfFileName(bo.getSpecpdfFile().substring(lastIndex + 1));
                    }
                }

                // 如果Word规格文件路径不为空，则提取文件名
                if (StringUtils.isNotBlank(bo.getSpecwordFile())&&!bo.getSpecwordFile().equals(bo.getSpecpdfFile())) {
                    // 设置Word规格文件路径
                    materialSpecificationVo.setSpecwordFile(bo.getSpecwordFile());

                    int lastIndex = bo.getSpecwordFile().lastIndexOf('/');
                    if (lastIndex != -1 && lastIndex < bo.getSpecwordFile().length() - 1) {
                        materialSpecificationVo.setSpecwordFileName(bo.getSpecwordFile().substring(lastIndex + 1));
                    }
                }

                // 将物料规格信息对象添加到返回列表中
               setVo(materialSpecificationVo,list);
            }
        }

        // 返回包含物料规格信息的列表
        if(list.size()>0){
            return list
                    .stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(()
                                    -> new TreeSet<>(Comparator
                                    .comparing(MaterialSpecificationVo::getProductionCode)
                                    .thenComparing(MaterialSpecificationVo::getBrandName,Comparator.nullsFirst(String::compareTo))
                                    .thenComparing(MaterialSpecificationVo::getSpecwordFile,Comparator.nullsFirst(String::compareTo))
                                    .thenComparing(MaterialSpecificationVo::getSpecpdfFile,Comparator.nullsFirst(String::compareTo)))),
                            ArrayList::new));
        }
        return list;
    }

    private void setVo( MaterialSpecificationVo materialSpecificationVo, List<MaterialSpecificationVo> list ){
        if(StringUtils.isNotBlank(materialSpecificationVo.getBrandName())){
            if(StringUtils.isNotBlank(materialSpecificationVo.getSpecpdfFile())||StringUtils.isNotBlank(materialSpecificationVo.getSpecwordFile())){
                // 将物料规格信息对象添加到返回列表中
                list.add(materialSpecificationVo);
            }
        }
    }


    /* Started by AICoder, pid:ka3140b84e5151c149f20bee80ed296bc58455fa */
    private List<ScmSpecificationResponseVo.BO> getSalesStatusName(String productionCode) {
        // 检查输入的基本编号是否为空或空白
        if (StringUtils.isBlank(productionCode)) {
            return null;
        }

        // 初始化HTTP请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("appcode", scmPropertiesConfig.getAppcode());

        // 构建请求体
        Map<String, Object> request = new HashMap<>();
        request.put("itemNo", productionCode);
        request.put("pageNo", 1);
        request.put("pageSize", 100);

        // 使用ObjectMapper将请求体转换为JSON字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String parameters = null;
        try {
            parameters = objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            log.error("Failed to create parameters", e);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }

        // 创建HTTP请求实体
        HttpEntity<String> requestEntity = new HttpEntity<>(parameters, httpHeaders);

        // 构建请求URI
        String uri = UriComponentsBuilder.fromHttpUrl(scmPropertiesConfig.getDomain() + scmPropertiesConfig.getSpecificationSplit())
                .build()
                .toUriString();

        // 发送HTTP POST请求并获取响应
        ResponseEntity<ScmSpecificationResponseVo> responseEntity=null;
        try {
            RestTemplate restTemplate = new RestTemplate();
            responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ScmSpecificationResponseVo.class);
            log.debug("responseEntity: {}", JSON.toJSONString(responseEntity));
        } catch (RestClientException e) {
            log.error("Failed to request the SCM", e);
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        // 检查响应体是否为空
        if (responseEntity.getBody() == null) {
            log.error("SCM response body returned is empty");
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }

        // 获取响应体对象
        ScmSpecificationResponseVo responseVo = responseEntity.getBody();
        log.debug("ResponseVo: {}", responseVo);

        // 检查响应码是否成功
        if ("0000".equals(responseVo.getCode().getCode())) {
            return responseVo.getBoList();
        } else {
            log.warn("SCM response code is not success, code: {}", responseVo.getCode().getCode());
            throw new BusinessException(StatusCode.THIRD_PLATFORM_ERROR);
        }
    }
    /* Ended by AICoder, pid:ka3140b84e5151c149f20bee80ed296bc58455fa */

    /* Started by AICoder, pid:vd3589d00e35d79141e50b55c0d1606115e20df8 */
    @Override
    public void downloadSpecification(HttpServletRequest request, HttpServletResponse response,
                                      String fl, String fileName) {
        

        int lastIndex = fl.lastIndexOf('/');
        if (lastIndex != -1 && lastIndex < fl.length() - 1) {
            try {
                String fn = URLEncoder.encode(fl.substring(lastIndex + 1),"utf-8");
                fn = fn.replace("+", "%20");
                fl = fl.replace(fl.substring(lastIndex + 1),fn);
            } catch (UnsupportedEncodingException e) {
               log.error("url转义失败",e);
            }
        }


        OutputStream out = null;
        InputStream inputStream = null;
        log.debug("fileName: {}, fileUrl: {}", fileName, fl);
        try {
            // 创建URL对象并打开连接
            URL url = new URL(fl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(10 * 1000); // 设置连接超时时间
            log.debug("conn: {}", conn);

            // 获取输入流
            inputStream = conn.getInputStream();

            /**
             * 输出文件到浏览器
             */
            // 设置响应头，指定文件类型和编码
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=utf-8"); // 设置内容类型为Excel文件
            response.setHeader("Access-Control-Allow-Origin", "*"); // 允许跨域请求

            // 获取输出流
            out = response.getOutputStream();
            byte[] bytes = new byte[1024]; // 缓冲区大小
            int len;

            // 读取输入流中的数据并写入输出流
            while ((len = inputStream.read(bytes)) != -1) {
                out.write(bytes, 0, len);
            }

            // 刷新输出流
            out.flush();
            log.debug("response: {}", response);

        } catch (Exception e) {
            log.error("下载失败：", e);
        } finally {
            // 关闭输入流
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("关闭输入流失败：", e);
                }
            }

            // 关闭输出流
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    log.error("关闭输出流失败：", e);
                }
            }
        }
    }

    @Override
    public SelectionFormMaterialVo queryBySelectionForm(SelectionFormMaterialQueryDto queryDto) {
        return materialDomainService.queryBySelectionForm(queryDto);
    }

    @Override
    public PageVO<MaterialVo> selectionAssociationConditions(MaterialConditionQueryDto dto) {
        return materialDomainService.selectionAssociationConditions(dto);
    }

    @Override
    public PageVO<MaterialVo> fuzzyQuerySelection(MaterialFuzzyQueryDto queryDto) {
        return materialDomainService.fuzzyQuerySelection(queryDto);
    }
    /* Started by AICoder, pid:o1e3dd279bs2c2c14ff908cc30a43403d6d45856 */
    @Override
    public PageVO<PriceHistoryVo> getChangeMaterialCost(MaterialCostDto costDto) {
        return materialDomainService.getChangeMaterialCost(costDto);
    }

    /* Ended by AICoder, pid:o1e3dd279bs2c2c14ff908cc30a43403d6d45856 */
    /* Ended by AICoder, pid:vd3589d00e35d79141e50b55c0d1606115e20df8 */
}
/* Ended by AICoder, pid:39375g4bfe528ee147f309946014f95ed980ecf0 */
