/* Started by AICoder, pid:859c1a4c576d084143510baf706d355bfb294dce */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 采购查询数据传输对象 (DTO)。
 * 用于封装采购查询请求的参数，并提供参数校验功能。
 */
@Getter
@Setter
@ToString
public class SelectionFormQueryDto {

    /**
     * 商机ID或地区ID。
     * 用于筛选特定商机或地区的选型单。
     */
    private String conditionId;

    /**
     * 产品小类ID集。
     * 用于筛选特定产品小类的选型单。
     */
    private List<String> productCategoryIdList;

    /**
     * 选型名称。
     * 用于筛选特定名称的选型单。
     */
    private String selectionName;

    /**
     * 选型单类型。
     * 用于筛选特定类型的选型单。
     */
    private List<String> typeList;

    /**
     * 选型单状态。
     * 用于筛选特定状态的选型单。
     */
    private List<String> statusList;

    /**
     * 分页查询的页码，默认为1。
     * 用于分页查询时指定当前页码。
     */
    private Integer pageNum;

    /**
     * 每页显示的记录数，默认为10。
     * 用于分页查询时指定每页显示的记录数。
     */
    private Integer pageSize;
}
/* Ended by AICoder, pid:859c1a4c576d084143510baf706d355bfb294dce */