/* Started by AICoder, pid:m75be154b465ee214f4e0afe51f2ec27f3f19a29 */
package com.zte.uedm.dcdigital.domain.model.material.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物料历史实体类，用于表示历史物料的详细信息。
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class MaterialHistoryEntity {

    /**
     * 历史ID，唯一标识每条历史记录。
     */
    private String id;

    /**
     * 物料ID，关联主表的物料ID。
     */
    private String materialId;

    /**
     * 审批表ID，关联特定的审批流程。
     */
    private String approvalId;

    /**
     * 物料名称，描述物料的基本名称。
     */
    private String name;

    /**
     * 品牌，表示物料的品牌信息。
     */
    private String brand;

    /**
     * 供应商，表示提供物料的供应商信息。
     */
    private String supplier;

    /**
     * 采购模式，表示物料的采购方式。
     */
    private String purchaseMode;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    private String expirationDate;

    /**
     * 保质期，表示物料的保质期限。
     */
    private String warrantyPeriod;

    /**
     * 成本费用，表示物料的成本价格。
     */
    private String cost;

    /**
     * 交期，表示物料的交付时间。
     */
    private String deliveryDays;

    /**
     * 分组ID，用于将物料分组管理。
     */
    private String groupId;

    /**
     * PDM的ID，关联PDM系统中的物料信息。
     */
    private String pdmInfoId;

    /**
     * 版本，表示物料的版本信息。
     */
    private String version;

    /**
     * 发起人，记录发起变更的用户。
     */
    private String submitter;

    /**
     * 审批通过时间，记录物料变更审批通过的时间。
     */
    private String approvalTime;

    /**
     * 版本变更原因，记录物料版本变更的原因（例如：PDM同步或审批通过）。
     */
    private String changeReason;

    /* Started by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 规格型号：表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    private String specificationModel;

    /**
     * 服务：表示与产品相关的服务信息，例如售后服务、保修服务等。
     */
    private String service;
    /**
     * 推荐等级 从A、B、C三个等级中选择其一
     */
    private String recommendedLevel;


    /**
     * 描述：
     */
    private String description;

    /**
     * 单位：
     */
    private String unit;
    /* Ended by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 生产代码，表示物料的生产代码。
     */
    private String productionCode;

    /**
     * 销售状态，表示物料的销售状态。
     */
    private String salesStatus;
    /**
     * 创建者，记录创建该历史记录的用户。
     */
    private String createBy;

    /**
     * 更新者，记录更新该历史记录的用户。
     */
    private String updateBy;

    /**
     * 创建日期，记录历史记录的创建时间。
     */
    private String createTime;

    /**
     * 更新日期，记录历史记录的最后更新时间。
     */
    private String updateTime;
}
/* Ended by AICoder, pid:m75be154b465ee214f4e0afe51f2ec27f3f19a29 */