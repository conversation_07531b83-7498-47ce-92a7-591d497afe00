/* Started by AICoder, pid:7c19dm8b3cq12af14567082f3023fa62aac49da8 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.enums.LectotypeTypeEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * 采购查询数据传输对象 (DTO)。
 * 用于封装采购查询请求的参数，并提供参数校验功能。
 */
@Getter
@Setter
@ToString
@Slf4j
public class ProcurementQueryDto {

    /**
     * 选型单类型，必须为1或2。
     *
     * @NotBlank 确保该字段不能为空。
     */
    @NotBlank(message = "选型单类型不能为空")
    private String lectotypeType;

    /**
     * 选型单id，不能为空。
     *
     * @NotBlank 确保该字段不能为空。
     */
    @NotBlank(message = "选型单Id不能为空")
    private String lectotypeId;

    /**
     * 校验参数是否合法。
     *
     * 1. 使用ValidateUtils.validateObj方法校验对象字段是否为空。
     * 2. 如果校验失败，记录错误日志并抛出BusinessException。
     * 3. 校验lectotypeType字段是否为1或2，否则抛出BusinessException。
     */
    public void verification() {
        // 校验bean字段NotEmpty
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[ProcurementQueryDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        // 校验type字段是否为1或2
        if (!LectotypeTypeEnums.contains(lectotypeType)) {
            log.error("Invalid parameter:{}: lectotypeType must be 1 or 2", lectotypeType);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
    }
}
/* Ended by AICoder, pid:7c19dm8b3cq12af14567082f3023fa62aac49da8 */