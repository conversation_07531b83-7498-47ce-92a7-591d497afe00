package com.zte.uedm.dcdigital.application.coreparam;

import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamTemplateDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCoreParamVo;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;

import java.util.List;

import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamDeleteDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCoreParamEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCoreParamDetailVo;

/* Started by AICoder, pid:y45ae4698dj1acb149f20a8e400f1e0175722408 */
public interface ProductCoreParamCommandService {
    /* Started by AICoder, pid:x9511i67ffbf2fd14b6e0b6ef029dd266e339781 */
    /**
     * 添加一个新的产品核心参数。
     *
     * @param addDto 包含新添加的产品核心参数信息的数据传输对象。
     * @return 操作是否成功。如果添加成功，则返回true，否则返回false。
     */
    Boolean addProductCoreParam(ProductCoreParamAddDto addDto);

    /**
     * 编辑一个已有的产品核心参数。
     *
     * @param editDto 包含需要编辑的产品核心参数信息的数据传输对象。
     * @return 操作是否成功。如果编辑成功，则返回true，否则返回false。
     */
    Boolean editProductCoreParam(ProductCoreParamEditDto editDto);

    /**
     * 批量删除产品核心参数。
     *
     * @param deleteDto 包含需要删除的产品核心参数ID列表的数据传输对象。
     * @return 操作是否成功。如果删除成功，则返回true，否则返回false。
     */
    Boolean deleteByIds(ProductCoreParamDeleteDto deleteDto);
    /* Ended by AICoder, pid:x9511i67ffbf2fd14b6e0b6ef029dd266e339781 */
    int importTemplate(String productCategoryId,FormDataMultiPart file);
}
/* Ended by AICoder, pid:y45ae4698dj1acb149f20a8e400f1e0175722408 */
