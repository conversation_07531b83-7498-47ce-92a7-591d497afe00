/* Started by AICoder, pid:8d4a0eb9b9nd5ea144df0aed20463f11b319f648 */
package com.zte.uedm.dcdigital.application.category;

import com.zte.uedm.dcdigital.interfaces.web.product.dto.RequirementDashboardAddDto;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;

/**
 * RequirementDashboardCommandService 接口定义了用于编辑需求仪表板的命令服务。
 * 该接口提供了一个方法，用于更新需求仪表板的信息。
 *
 * <AUTHOR>
 */
public interface RequirementDashboardCommandService {

    /**
     * 编辑需求仪表板。
     *
     * @param dashboardAddDto 包含需求仪表板更新信息的数据传输对象
     */
    void editDashboard(RequirementDashboardAddDto dashboardAddDto);

    void sendKafkaMessage(OperationLogBean operationLogBean);

    void sendLog(String operation, OperationTypeOptional operationType, String detail );
}
/* Ended by AICoder, pid:8d4a0eb9b9nd5ea144df0aed20463f11b319f648 */