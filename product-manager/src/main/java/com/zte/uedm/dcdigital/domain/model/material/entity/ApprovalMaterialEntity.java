/* Started by AICoder, pid:p22c3n74e1u815714ec80a1b8039763b0c87a845 */
package com.zte.uedm.dcdigital.domain.model.material.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 审批和物料关联关系实体类。
 * 该类用于表示审批和物料之间的关联关系，包含相关的属性和方法。
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class ApprovalMaterialEntity {

    /**
     * ID，唯一标识每条记录。
     */
    private String id;

    /**
     * 审批表ID，关联特定的审批流程。
     */
    private String approvalId;

    /**
     * 物料ID，关联具体的物料信息。
     */
    private String materialId;

    /**
     * 物料版本，表示物料的具体版本信息。
     */
    private String materialVersion;
}
/* Ended by AICoder, pid:p22c3n74e1u815714ec80a1b8039763b0c87a845 */