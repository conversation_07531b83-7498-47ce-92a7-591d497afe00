package com.zte.uedm.dcdigital.interfaces.web.material.vo;

/* Started by AICoder, pid:cc291989dcfc7fa1483b09b50040d77cba672115 */
import com.zte.uedm.dcdigital.domain.common.enums.DemandTypeEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * DemandManagementPo 类表示需求管理的持久化对象。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DemandManagementVo {

    /**
     * id 属性表示记录的唯一标识符。
     */
    private String id;

    /**
     * projectId 属性表示项目的ID。
     */
    private String projectId;
    private String projectName;

    /**
     * billQuantitieId 属性表示账单数量的ID。
     */
    private String billQuantityId;

    /**
     * productCategoryId 属性表示产品类别的ID。
     */
    private String productCategoryId;
    private String productCategoryName;

    /**
     * createUser 属性表示创建用户。
     */
    private String createUser;

    /**
     * expTimeComplet 属性表示预计完成时间。
     */
    private String expTimeComplet;

    /**
     * actTimeComplet 属性表示实际完成时间。
     */
    private String actTimeComplet;

    /**
     * processor 属性表示处理人。
     */
    private String processor;
    private String processorName;

    /**
     * demandType 属性表示需求类型。
     */
    private String demandType;
    private String demandTypeName;

    /**
     * createBy 属性表示创建者。
     */
    private String createBy;

    /**
     * createTime 属性表示创建时间。
     */
    private String createTime;

    /**
     * updateBy 属性表示更新者。
     */
    private String updateBy;

    /**
     * updateTime 属性表示更新时间。
     */
    private String updateTime;

    /**
     * updateTime 属性表示需求单的工程量清单条目发生变化时间。
     */
    private String billQuantitieTime;
    public void setDemandType(String demandType) {
        this.demandType = demandType;
        if (demandType != null) {
            DemandTypeEnums demandTypeEnums = DemandTypeEnums.lookFor(demandType);
            this.demandTypeName = demandTypeEnums == null ? "" : demandTypeEnums.name;
        }
    }

}
/* Ended by AICoder, pid:cc291989dcfc7fa1483b09b50040d77cba672115 */
