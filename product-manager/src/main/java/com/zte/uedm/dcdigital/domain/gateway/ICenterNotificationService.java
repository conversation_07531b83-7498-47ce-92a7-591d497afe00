package com.zte.uedm.dcdigital.domain.gateway;

import com.zte.uedm.dcdigital.interfaces.web.material.vo.ICenterNotificationResponseVo;

import java.time.Instant;
import java.util.Map;

/**
 * iCenter通知服务接口
 * 提供调用iCenter通知API的功能
 *
 * <AUTHOR>
 */
public interface ICenterNotificationService {

    /**
     * 发送模板通知
     * 调用iCenter的模板发送接口
     *
     * @param templateId 模板ID
     * @param info 对应渠道的通用参数（发件人，收件人等）
     * @param data 业务数据（模板配置参数）
     * @param sendTime 发送时间（可选）
     * @return 通知发送结果
     */
    ICenterNotificationResponseVo sendTemplateNotification(String templateId, Map<String, Object> info, Map<String, Object> data, Instant sendTime);
}
