package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.GroupSortUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;

import java.util.List;

public interface ProductGroupService {

    /* Started by AICoder, pid:m9357q2d671315b1481208d2c00c5b0280a7b1b3 */
    /**
     * 查询符合条件的产品分组列表。
     *
     * @param queryDto 包含查询条件的数据传输对象，用于指定查询的参数，如分组名称、产品类别ID等。
     * @return 一个包含所有符合条件的产品分组视图对象的列表。该列表可以为空，但不会为null。
     */
    List<ProductGroupVo> queryProductGroups(ProductGroupQueryDto queryDto);
    /* Ended by AICoder, pid:m9357q2d671315b1481208d2c00c5b0280a7b1b3 */


    /* Started by AICoder, pid:9a3729e2c4ubbbd146900842606f97035e6675af */
    /**
     * 新增产品组
     * @param addDto
     * @return
     */
    ProductGroupVo add(ProductGroupAddDto addDto);
    /* Ended by AICoder, pid:9a3729e2c4ubbbd146900842606f97035e6675af */


    /* Started by AICoder, pid:70ae7ha507a527214fa909372051c0082c467dc1 */
    /**
     * 编辑产品组
     * @param editDto
     * @return
     */
    ProductGroupVo edit(ProductGroupEditDto editDto);
    /* Ended by AICoder, pid:70ae7ha507a527214fa909372051c0082c467dc1 */

    /* Started by AICoder, pid:m09bbmd4b8w01081486e0b4d300a6d012fb726ad */
    /**
     * 删除产品组
     * @param id 产品组的唯一标识符
     * @return 删除操作的结果，通常表示影响的行数
     * @throws BusinessException 如果删除过程中发生错误
     */
    int delete(String id);
    /* Ended by AICoder, pid:m09bbmd4b8w01081486e0b4d300a6d012fb726ad */

    ProductGroupDetailVo queryProductGroupDetail(String id);
    /**
     * 更新产品分组排序。
     *
     * @param sortUpdateDto 更新的排序数据集
     * @return 包含产品分组详细信息的视图对象
     * */
    void updateSort(GroupSortUpdateDto sortUpdateDto);
}
