package com.zte.uedm.dcdigital.application.category.impl;

import com.zte.uedm.dcdigital.application.category.RequirementDashboardQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.enums.CodeStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.ConfirmationModeEnums;
import com.zte.uedm.dcdigital.domain.service.RequirementDashboardDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardConditionQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.RequirementDashboardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RequirementDashboardQueryServiceImpl implements RequirementDashboardQueryService {

    @Autowired
    private RequirementDashboardDomainService dashboardDomainService;

    @Override
    public List<IdNameBean> getAllConfirmationMode() {
        return Arrays.stream(ConfirmationModeEnums.values())
                .map(e -> new IdNameBean(e.getCode(), I18nUtil.getI18nFromString(e.getName())))
                .collect(Collectors.toList());
    }
    @Override
    public RequirementDashboardVo queryByProductAndProject(DashboardQueryDto queryDto) {
        //查询
        return dashboardDomainService.queryByProductAndProject(queryDto);
    }
    @Override
    public PageVO<RequirementDashboardVo> queryByCondition(DashboardConditionQueryDto queryDto) {
        return dashboardDomainService.queryByCondition(queryDto);
    }

    @Override
    public List<IdNameBean> queryAllCodeStatus() {
        return Arrays.stream(CodeStatusEnums.values())
                .map(e -> new IdNameBean(e.getId(), I18nUtil.getI18nFromString(e.getName())))
                .collect(Collectors.toList());
    }

}
