package com.zte.uedm.dcdigital.application.category.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.application.configuration.converter.ProductCategoryVoConverter;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryAndBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.enums.ProductCategoryEnums;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.service.ProductCategoryDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductCategoryQueryServiceImpl implements ProductCategoryQueryService {

    @Autowired
    private ProductCategoryDomainService productCategoryDomainService;
    @Autowired
    private ProductCategoryRepository productCategoryRepository;

    @Override
    public ProductCategoryVo queryProductCategoryById(String id) throws BusinessException {
        ProductCategoryEntity categoryEntity = productCategoryDomainService.queryProductCategoryById(id);
        return ProductCategoryVoConverter.convertProductCategoryVo(categoryEntity);
    }

    @Override
    public PageInfo<ProductCategoryVo> queryProductCategory(ProductCategoryQueryDto queryDto) throws BusinessException {

        PageInfo<ProductCategoryEntity> entityPage = productCategoryDomainService.queryProductCategory(queryDto);

        List<ProductCategoryVo> voList = CollectionUtils.isEmpty(entityPage.getList())
                ? Collections.emptyList() : entityPage.getList().stream()
                .map(ProductCategoryVoConverter::convertProductCategoryVo).collect(Collectors.toList());
        PageInfo<ProductCategoryVo> categoryVoPageInfo = new PageInfo<>(voList);
        categoryVoPageInfo.setTotal(entityPage.getTotal());
        return categoryVoPageInfo;
    }

    /* Started by AICoder, pid:qa744pe738d838414e75094d8060ec221780eb2b */
    @Override
    public List<ProductSubcategoryVo> getUserProductSubcategory() {
        return productCategoryDomainService.getUserProductSubcategory();

    }

    @Override
    public List<ProductCategoryVo> batchQueryCategory(List<String> ids) {
        return productCategoryDomainService.batchQueryByIds(ids);
    }

    @Override
    public List<ProductCategoryInfoVo> queryByIds(List<String> ids) {
        return productCategoryDomainService.queryByIds(ids);
    }

    @Override
    public List<ProductTreeVo> queryTreeByIds(List<String> ids) {
        List<ProductSubcategoryVo> subcategoryVoList = productCategoryDomainService.getAllProductSubcategory();
        List<ProductSubcategoryVo> subcategoryVos = subcategoryVoList.stream().filter(cat -> ids.contains(cat.getId())).collect(Collectors.toList());
        return buildTree(subcategoryVos);
    }

    @Override
    public ProductCategoryManagerVo queryProductCategoryManagerById(String id) {
        return productCategoryDomainService.queryProductCategoryManagerById(id);
    }

    /* Started by AICoder, pid:ia99371f3fk403014ff90929405bb80475c5c123 */
    @Override
    public List<ProductSubcategoryVo> getAllProductSubcategory() {
        // 直接调用领域服务方法获取所有产品子类别
        return productCategoryDomainService.getAllProductSubcategory();
    }

    @Override
    /* Started by AICoder, pid:k9046c53c150e58140e90989a0357d1a1cb8909d */
    public List<ProductTreeVo> getAllProductSubcategoryTree() {
        List<ProductSubcategoryVo> subcategoryVoList = productCategoryDomainService.getAllProductSubcategory();
        return buildTree(subcategoryVoList);
    }

    /* Started by AICoder, pid:sad19v8e1fmfeed14d9a0ba7f00601398351998a */
    @Override
    public List<ProductTreeVo> getProjTree() {
        // 获取所有产品子分类
        List<ProductSubcategoryVo> subcategoryVoList = productCategoryDomainService.getAllProductSubcategory();

        // 构建项目树
        List<ProductTreeVo> list = buildTree(subcategoryVoList);

        // 如果项目树不为空
        if (list.size() > 0) {
            for (ProductTreeVo productTreeVo : list) {
                // 获取子节点列表
                List<Object> productTreeVos = productTreeVo.getChildren();

                // 创建一个表示“无产品小类”的节点
                ProductTreeVo p = new ProductTreeVo();
                p.setId("null");
                p.setName("无产品小类");
                p.setNodeType(2);
                p.setParentId("");
                p.setPathName("");
                p.setPathId("");

                // 将“无产品小类”节点添加到子节点列表中
                productTreeVos.add(p);
            }
        }

        // 返回构建好的项目树
        return list;
    }
    /* Ended by AICoder, pid:sad19v8e1fmfeed14d9a0ba7f00601398351998a */

    private List<ProductTreeVo> buildTree(List<ProductSubcategoryVo> subcategoryVoList) {
        // 检查是否有任何层级存在非0的sortOrder（支持产品线、产品大类、产品小类）
        boolean hasNonZeroSortOrder = subcategoryVoList.stream()
                .anyMatch(node -> node.getSortOrder() != null && node.getSortOrder() > 0);

        // 预处理：在排序前处理productName拼接产品编码
        subcategoryVoList.forEach(vo -> {
            if (StringUtils.isNotBlank(vo.getProductNo())) {
                vo.setProductName("[" + vo.getProductNo() + "]" + vo.getProductName());
            }
        });

        // 初始排序（如果没有sortOrder，按productNo排序）
        if (!hasNonZeroSortOrder) {
            subcategoryVoList.sort(Comparator.comparing(ProductSubcategoryVo::getProductNo,
                    Comparator.nullsLast(String::compareTo)));
        }

        // 获取所有产品线和产品大类的sortOrder信息
        Map<String, Integer> categoryOrderMap = getCategoryOrderMap();

        Map<String, ProductTreeVo> treeMap = new HashMap<>();

        // 首先确保所有产品线和产品大类都被创建，即使没有产品小类
        ensureAllCategoriesExist(treeMap, categoryOrderMap);

        // 然后基于产品小类构建树结构
        for (ProductSubcategoryVo subcategoryVo : subcategoryVoList) {
            createTree(subcategoryVo, treeMap, categoryOrderMap);
        }

        List<ProductTreeVo> result = new ArrayList<>();
        for (ProductTreeVo vo : treeMap.values()) {
            if (vo.getParentId() == null) {
                result.add(vo);
            }
        }

        // 检查是否有任何层级存在非0的sortOrder（包括产品线和产品大类）
        boolean hasAnyNonZeroSortOrder = hasNonZeroSortOrder ||
                categoryOrderMap.values().stream().anyMatch(order -> order != null && order > 0);

        log.debug("buildTree sorting info: hasNonZeroSortOrder={}, categoryOrderMap size={}, hasAnyNonZeroSortOrder={}",
                hasNonZeroSortOrder, categoryOrderMap.size(), hasAnyNonZeroSortOrder);

        // 对整个树结构进行排序，支持所有层级
        // 强制使用sortOrder排序，因为我们已经获取了所有层级的sortOrder信息
        sortAllLevelsInTree(result, true);

        return result;
    }

    /**
     * 获取所有产品线和产品大类的sortOrder信息
     *
     * @return ID到sortOrder的映射
     */
    private Map<String, Integer> getCategoryOrderMap() {
        Map<String, Integer> orderMap = new HashMap<>();

        try {
            // 查询所有产品线的sortOrder
            ProductCategoryQueryDto productLineQuery = new ProductCategoryQueryDto();
            productLineQuery.setNodeType(ProductCategoryEnums.PRODUCT_LINE.getId());
            productLineQuery.setPageNum(1);
            productLineQuery.setPageSize(Integer.MAX_VALUE);

            PageInfo<ProductCategoryEntity> productLinePageInfo = productCategoryDomainService.queryProductCategory(productLineQuery);
            for (ProductCategoryEntity entity : productLinePageInfo.getList()) {
                orderMap.put(entity.getId(), entity.getSortOrder());
                log.debug("Loaded product line: id={}, name={}, sortOrder={}",
                        entity.getId(), entity.getProductName(), entity.getSortOrder());
            }

            // 查询所有产品大类的sortOrder
            ProductCategoryQueryDto productCategoryQuery = new ProductCategoryQueryDto();
            productCategoryQuery.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
            productCategoryQuery.setPageNum(1);
            productCategoryQuery.setPageSize(Integer.MAX_VALUE);

            PageInfo<ProductCategoryEntity> productCategoryPageInfo = productCategoryDomainService.queryProductCategory(productCategoryQuery);
            for (ProductCategoryEntity entity : productCategoryPageInfo.getList()) {
                orderMap.put(entity.getId(), entity.getSortOrder());
                log.debug("Loaded product category: id={}, name={}, sortOrder={}",
                        entity.getId(), entity.getProductName(), entity.getSortOrder());
            }

            log.debug("Loaded sortOrder for {} product lines and categories", orderMap.size());
        } catch (Exception e) {
            log.warn("Failed to load category order map, using default ordering", e);
        }

        return orderMap;
    }

    /**
     * 确保所有产品线和产品大类都存在于树结构中，即使没有产品小类
     *
     * @param treeMap 树节点映射
     * @param categoryOrderMap 分类排序映射
     */
    private void ensureAllCategoriesExist(Map<String, ProductTreeVo> treeMap, Map<String, Integer> categoryOrderMap) {
        try {
            // 查询所有产品线
            ProductCategoryQueryDto productLineQuery = new ProductCategoryQueryDto();
            productLineQuery.setNodeType(ProductCategoryEnums.PRODUCT_LINE.getId());
            productLineQuery.setPageNum(1);
            productLineQuery.setPageSize(Integer.MAX_VALUE);

            PageInfo<ProductCategoryEntity> productLinePageInfo = productCategoryDomainService.queryProductCategory(productLineQuery);
            for (ProductCategoryEntity entity : productLinePageInfo.getList()) {
                if (!treeMap.containsKey(entity.getId())) {
                    ProductTreeVo productLineVo = new ProductTreeVo();
                    productLineVo.setId(entity.getId());
                    productLineVo.setName(entity.getProductName());
                    productLineVo.setPathId(entity.getId());
                    productLineVo.setPathName(entity.getProductName());
                    productLineVo.setNodeType(1); // 产品线
                    productLineVo.setParentId(null);
                    productLineVo.setSortOrder(entity.getSortOrder() != null ? entity.getSortOrder() : 0);
                    productLineVo.setChildren(new ArrayList<>());
                    treeMap.put(entity.getId(), productLineVo);
                    log.debug("Created product line node: id={}, name={}", entity.getId(), entity.getProductName());
                }
            }

            // 查询所有产品大类
            ProductCategoryQueryDto productCategoryQuery = new ProductCategoryQueryDto();
            productCategoryQuery.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
            productCategoryQuery.setPageNum(1);
            productCategoryQuery.setPageSize(Integer.MAX_VALUE);

            PageInfo<ProductCategoryEntity> productCategoryPageInfo = productCategoryDomainService.queryProductCategory(productCategoryQuery);
            for (ProductCategoryEntity entity : productCategoryPageInfo.getList()) {
                if (!treeMap.containsKey(entity.getId())) {
                    ProductTreeVo productCategoryVo = new ProductTreeVo();
                    productCategoryVo.setId(entity.getId());
                    productCategoryVo.setName(entity.getProductName());
                    productCategoryVo.setPathId(entity.getPathId());
                    productCategoryVo.setPathName(entity.getPathName());
                    productCategoryVo.setNodeType(2); // 产品大类
                    productCategoryVo.setParentId(entity.getParentId());
                    productCategoryVo.setSortOrder(entity.getSortOrder() != null ? entity.getSortOrder() : 0);
                    productCategoryVo.setChildren(new ArrayList<>());
                    treeMap.put(entity.getId(), productCategoryVo);

                    // 将产品大类添加到其父产品线的children中
                    ProductTreeVo parentProductLine = treeMap.get(entity.getParentId());
                    if (parentProductLine != null) {
                        parentProductLine.getChildren().add(productCategoryVo);
                    }
                    log.debug("Created product category node: id={}, name={}, parentId={}",
                            entity.getId(), entity.getProductName(), entity.getParentId());
                }
            }

            log.info("Ensured all categories exist: {} total nodes in tree", treeMap.size());
        } catch (Exception e) {
            log.warn("Failed to ensure all categories exist", e);
        }
    }

    private void createTree(ProductSubcategoryVo subcategoryVo, Map<String, ProductTreeVo> treeMap,
                           Map<String, Integer> categoryOrderMap) {
        String pathId = subcategoryVo.getPathId();
        String[] pathIdSplit = pathId.split("/");
        int pathLevel = pathIdSplit.length;
        String pathName = subcategoryVo.getPathName();
        String[] pathNameSplit = pathName.split("/", pathLevel);
        int len = Math.min(pathLevel, pathNameSplit.length);

        ProductTreeVo currentParent = null;
        for (int i = 0; i < len - 1; i++) {
            String pId = pathIdSplit[i];
            String pName = pathNameSplit[i];

            ProductTreeVo productTreeVo;
            if (treeMap.get(pId) != null) {
                productTreeVo = treeMap.get(pId);
            } else {
                productTreeVo = new ProductTreeVo();
                productTreeVo.setId(pId);
                productTreeVo.setPathId(pId);
                productTreeVo.setName(pName);
                productTreeVo.setPathName(pName);
                productTreeVo.setNodeType(i + 1);

                // 设置sortOrder（从categoryOrderMap中获取）
                Integer sortOrder = categoryOrderMap.get(pId);
                productTreeVo.setSortOrder(sortOrder != null ? sortOrder : 0);

                log.debug("createTree - Set sortOrder for node: id={}, name={}, sortOrder={}, nodeType={}",
                        pId, pName, productTreeVo.getSortOrder(), productTreeVo.getNodeType());

                if (currentParent != null) {
                    productTreeVo.setPathId(currentParent.getId() + "/" + pId);
                    productTreeVo.setPathName(currentParent.getName() + "/" + pName);
                    productTreeVo.setParentId(currentParent.getId());
                    currentParent.getChildren().add(productTreeVo);
                }
                treeMap.put(pId, productTreeVo);
            }

            currentParent = productTreeVo;
        }

        // Add the SubProductTreeVo at the end of the path
        SubProductTreeVo subProductTreeVo = new SubProductTreeVo();
        subProductTreeVo.setId(pathIdSplit[len - 1]);
        subProductTreeVo.setPathId(pathId);
        subProductTreeVo.setName(pathNameSplit[len - 1]);
        subProductTreeVo.setPathName(pathName);
        subProductTreeVo.setNodeType(len);
        subProductTreeVo.setProductLevel(subcategoryVo.getProductLevel());
        subProductTreeVo.setProductNo(subcategoryVo.getProductNo());
        subProductTreeVo.setDescription(subcategoryVo.getDescription());
        subProductTreeVo.setNonStandardItems(subcategoryVo.getNonStandardItems());
        subProductTreeVo.setProductComponent(subcategoryVo.getProductComponent());
        subProductTreeVo.setProductLineName(subcategoryVo.getProductLineName());
        subProductTreeVo.setProductLineNo(subcategoryVo.getProductLineNo());
        subProductTreeVo.setProductName(subcategoryVo.getProductName());
        subProductTreeVo.setParentId(currentParent == null ? "" : currentParent.getId());
        subProductTreeVo.setSortOrder(subcategoryVo.getSortOrder());
        if (currentParent != null) {
            currentParent.getChildren().add(subProductTreeVo);
        }
    }

    /* Started by AICoder, pid:wd25di20ce8823114160087d40fa87544823725e */
    /**
     * 对树形结构中的产品小类进行排序
     * @param result 根节点列表（产品线）
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void sortProductSubcategoriesInTree(List<ProductTreeVo> result, boolean hasNonZeroSortOrder) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        result.forEach(productLine -> processProductLine(productLine, hasNonZeroSortOrder));
    }

    /**
     * 处理单个产品线的排序
     * @param productLine 产品线节点
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void processProductLine(ProductTreeVo productLine, boolean hasNonZeroSortOrder) {
        if (!hasValidChildren(productLine)) {
            return;
        }

        List<ProductTreeVo> productCategories = filterProductCategories(productLine.getChildren());
        productCategories.forEach(category -> processProductCategory(category, hasNonZeroSortOrder));
    }

    /**
     * 处理单个产品大类的排序
     * @param productCategory 产品大类节点
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void processProductCategory(ProductTreeVo productCategory, boolean hasNonZeroSortOrder) {
        if (!hasValidChildren(productCategory)) {
            return;
        }

        List<SubProductTreeVo> subcategories = extractAndSortSubcategories(productCategory, hasNonZeroSortOrder);
        replaceChildrenWithSortedSubcategories(productCategory, subcategories);
    }

    /**
     * 检查节点是否有有效的子节点
     * @param node 要检查的节点
     * @return 是否有有效子节点
     */
    private boolean hasValidChildren(ProductTreeVo node) {
        return node != null &&
               node.getChildren() != null &&
               !node.getChildren().isEmpty();
    }

    /**
     * 从子节点列表中过滤出产品大类
     * @param children 子节点列表
     * @return 产品大类列表
     */
    private List<ProductTreeVo> filterProductCategories(List<Object> children) {
        return children.stream()
                .filter(ProductTreeVo.class::isInstance)
                .map(ProductTreeVo.class::cast)
                .collect(Collectors.toList());
    }

    /**
     * 提取并排序产品小类
     * @param productCategory 产品大类节点
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     * @return 排序后的产品小类列表
     */
    private List<SubProductTreeVo> extractAndSortSubcategories(ProductTreeVo productCategory, boolean hasNonZeroSortOrder) {
        List<SubProductTreeVo> subcategories = productCategory.getChildren().stream()
                .filter(SubProductTreeVo.class::isInstance)
                .map(SubProductTreeVo.class::cast)
                .collect(Collectors.toList());

        if (!subcategories.isEmpty()) {
            sortSubcategories(subcategories, hasNonZeroSortOrder);
        }

        return subcategories;
    }

    /**
     * 用排序后的产品小类替换原有子节点
     * @param productCategory 产品大类节点
     * @param sortedSubcategories 排序后的产品小类列表
     */
    private void replaceChildrenWithSortedSubcategories(ProductTreeVo productCategory, List<SubProductTreeVo> sortedSubcategories) {
        productCategory.getChildren().clear();
        productCategory.getChildren().addAll(sortedSubcategories);
    }

    /**
     * 对所有层级的树结构进行排序
     * 支持产品线、产品大类、产品小类的排序
     *
     * @param treeNodes 树节点列表
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void sortAllLevelsInTree(List<ProductTreeVo> treeNodes, boolean hasNonZeroSortOrder) {

        // 对当前层级进行排序
        sortTreeNodes(treeNodes, hasNonZeroSortOrder);

        // 递归对子节点进行排序
        for (ProductTreeVo node : treeNodes) {
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                log.debug("Processing children for node: id={}, name={}, children count={}",
                        node.getId(), node.getName(), node.getChildren().size());

                // 分离产品大类和产品小类
                List<ProductTreeVo> childNodes = new ArrayList<>();
                List<SubProductTreeVo> subcategories = new ArrayList<>();

                for (Object child : node.getChildren()) {
                    if (child instanceof ProductTreeVo) {
                        childNodes.add((ProductTreeVo) child);
                    } else if (child instanceof SubProductTreeVo) {
                        subcategories.add((SubProductTreeVo) child);
                    }
                }

                // 排序产品大类
                if (!childNodes.isEmpty()) {
                    log.debug("Sorting {} product categories under parent: {}", childNodes.size(), node.getName());
                    sortTreeNodes(childNodes, hasNonZeroSortOrder);
                    // 递归处理产品大类的子节点
                    sortAllLevelsInTree(childNodes, hasNonZeroSortOrder);
                }

                // 排序产品小类
                if (!subcategories.isEmpty()) {
                    log.debug("Sorting {} product subcategories under parent: {}", subcategories.size(), node.getName());
                    sortSubcategories(subcategories, hasNonZeroSortOrder);
                }

                // 重新构建children列表，保持排序后的顺序
                List<Object> sortedChildren = new ArrayList<>();
                sortedChildren.addAll(childNodes);  // 先添加排序后的产品大类
                sortedChildren.addAll(subcategories);  // 再添加排序后的产品小类

                // 更新父节点的children
                node.getChildren().clear();
                node.getChildren().addAll(sortedChildren);

                log.debug("Updated children for node: {}, new children count: {}",
                        node.getName(), node.getChildren().size());
            }
        }
    }

    /**
     * 对树节点列表进行排序（适用于产品线和产品大类）
     *
     * @param nodes 树节点列表
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void sortTreeNodes(List<ProductTreeVo> nodes, boolean hasNonZeroSortOrder) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        // 检查当前节点列表是否有非0的sortOrder
        boolean currentLevelHasSortOrder = nodes.stream()
                .anyMatch(node -> node.getSortOrder() != null && node.getSortOrder() > 0);

        log.debug("sortTreeNodes: nodes count={}, hasNonZeroSortOrder={}, currentLevelHasSortOrder={}",
                nodes.size(), hasNonZeroSortOrder, currentLevelHasSortOrder);

        // 打印排序前的状态
        if (log.isDebugEnabled()) {
            for (ProductTreeVo node : nodes) {
                log.debug("Before sort - Node: id={}, name={}, sortOrder={}, nodeType={}",
                        node.getId(), node.getName(), node.getSortOrder(), node.getNodeType());
            }
        }

        if (currentLevelHasSortOrder) {
            // 当前层级存在非0的sortOrder，按sortOrder升序排列，然后按name排序
            nodes.sort(Comparator.comparingInt((ProductTreeVo node) ->
                            node.getSortOrder() != null ? node.getSortOrder() : 0)
                    .thenComparing(ProductTreeVo::getName, Comparator.nullsLast(String::compareTo)));
            log.debug("Applied sortOrder-based sorting for {} nodes", nodes.size());
        } else {
            // 当前层级所有sortOrder都为0，使用原有排序逻辑（按name排序）
            nodes.sort(Comparator.comparing(ProductTreeVo::getName, Comparator.nullsLast(String::compareTo)));
            log.debug("Applied name-based sorting for {} nodes", nodes.size());
        }

        // 打印排序后的状态
        if (log.isDebugEnabled()) {
            for (ProductTreeVo node : nodes) {
                log.debug("After sort - Node: id={}, name={}, sortOrder={}, nodeType={}",
                        node.getId(), node.getName(), node.getSortOrder(), node.getNodeType());
            }
        }
    }

    /**
     * 对产品小类列表进行排序
     * @param subcategories 产品小类列表
     * @param hasNonZeroSortOrder 是否存在非0的sortOrder
     */
    private void sortSubcategories(List<SubProductTreeVo> subcategories, boolean hasNonZeroSortOrder) {
        if (hasNonZeroSortOrder) {
            // 存在非0的sortOrder，按sortOrder升序排列，然后按productNo排序
            subcategories.sort(Comparator.comparingInt((SubProductTreeVo node) ->
                            node.getSortOrder() != null ? node.getSortOrder() : 0)
                    .thenComparing(SubProductTreeVo::getProductNo, Comparator.nullsLast(String::compareTo)));
        } else {
            // 所有sortOrder都为0，使用原有排序逻辑（按productNo排序）
            subcategories.sort(Comparator.comparing(SubProductTreeVo::getProductNo, Comparator.nullsLast(String::compareTo)));
        }
    }
    /* Ended by AICoder, pid:wd25di20ce8823114160087d40fa87544823725e */
    /* Ended by AICoder, pid:19046d53c1c0e58140e90989a0357d4a1cb1909d */

    @Override
    public List<ProductCategoryAndBrandInfoVo> selectProductCategoryAndBrandList(List<String> ids) {
        return productCategoryDomainService.selectProductCategoryAndBrandList(ids);
    }

    @Override
    public List<ProductCategoryAndBrandInfoVo> selectALLProductCategoryAndBrandList() {
        List<String> ids=new ArrayList<>();
        return productCategoryDomainService.selectProductCategoryAndBrandList(ids);
    }

    @Override
    public List<ProductCategoryInfoVo> queryByPath(Set<String> pathList) {
        return productCategoryDomainService.queryByPathName(pathList);
    }

    @Override
    public List<ProductTreeVo> getOptionalSubcategoryTree(String projectId) {
        Set<String> ids = productCategoryDomainService.queryGuideSubcategory(projectId);
        List<ProductSubcategoryVo> subcategoryVoList = productCategoryDomainService.getAllProductSubcategory();
        if (CollectionUtils.isNotEmpty(ids)) {
            //过滤掉已有的
            subcategoryVoList = subcategoryVoList.stream().filter(cat -> !ids.contains(cat.getId())).collect(Collectors.toList());
        }
        return buildTree(subcategoryVoList);
    }

    @Override
    public List<ProductTreeVo> queryAuthorizedSubcategoryTree(String projectId) {
        List<ProductSubcategoryVo> productSubcategoryVos = productCategoryDomainService.queryAuthorizedSubcategory(projectId);
        if (CollectionUtils.isEmpty(productSubcategoryVos)) {
            return Collections.emptyList();
        }
        return buildTree(productSubcategoryVos);
    }

    @Override
    public List<String> selectProductSeByCategoryId(String productCategoryId) {
        return productCategoryDomainService.selectProductSeByCategoryId(productCategoryId);
    }
    /* Ended by AICoder, pid:ia99371f3fk403014ff90929405bb80475c5c123 */
    /* Ended by AICoder, pid:qa744pe738d838414e75094d8060ec221780eb2b */
    /* Started by AICoder, pid:x8d6ed6459sd8a514ea80a88d0f7241f8610d754 */
    @Override
    public List<String> selectByCategoryId(String productCategoryId) {
        /**
         * 根据产品类别ID查询所有子类别ID。
         *
         * @param productCategoryId 产品类别ID
         * @return 子类别ID列表
         */
        return productCategoryDomainService.selectByCategoryId(productCategoryId);
    }
    /* Ended by AICoder, pid:x8d6ed6459sd8a514ea80a88d0f7241f8610d754 */

    /* Started by AICoder, pid:r24d8nd693te6f114d620bcab0dc561dc5d05f08 */
    @Override
    public List<ProductCategoryInfoVo> selectByName(String categoryName) {
        /**
         * 根据类别名称查询产品类别信息。
         *
         * @param categoryName 类别名称
         * @return 包含产品类别信息的 ProductCategoryInfoVo 对象列表
         */
        return productCategoryDomainService.selectByName(categoryName);
    }
    /* Ended by AICoder, pid:r24d8nd693te6f114d620bcab0dc561dc5d05f08 */

    /* Started by AICoder, pid:211f934d97q9b41148ab0a2ed07e461715f00531 */
    @Override
    public List<ProductCategoryInfoVo> selectByNodeType(String nodeType) {
        /**
         * 根据节点类型查询产品类别信息。
         *
         * @param nodeType 节点类型
         * @return 包含产品类别信息的 ProductCategoryInfoVo 对象列表
         */
        return productCategoryDomainService.selectByNodeType(nodeType);
    }
    /* Ended by AICoder, pid:211f934d97q9b41148ab0a2ed07e461715f00531 */
    /* Started by AICoder, pid:oe55e0c71di30941470b0b7bf001642e8d8465cf */
    @Override
    public List<ProductCategoryInfoVo> getIdsByParentId(String parentId) {
        List<ProductCategoryInfoVo> list = productCategoryRepository.getIdsByParentId(parentId);
        return list;
    }

    @Override
    public List<String> getProductSubcategoryId() {
        List<String> productSubcategoryId = productCategoryRepository.getProductSubcategoryId();
        return productSubcategoryId;
    }
    /* Ended by AICoder, pid:oe55e0c71di30941470b0b7bf001642e8d8465cf */

    @Override
    public List<ProductSubcategoryWithCategoryVo> querySubcategoriesWithCategoryByParentId(String parentId) {
        log.info("querySubcategoriesWithCategoryByParentId parentId: {}", parentId);
        List<ProductSubcategoryWithCategoryVo> result = productCategoryDomainService.querySubcategoriesWithCategoryByParentId(parentId);
        return result;
    }

    @Override
    public List<MaterialStatisticsVo> queryMaterialStatisticsByTimeRange(MaterialStatisticsQueryDto queryDto) {
        return productCategoryDomainService.queryMaterialStatisticsByTimeRange(queryDto);
    }
}
