/* Started by AICoder, pid:eda60w5dc295ad514b040ac5f072bc674c562d3f */
package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.repository.MaterialRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.domain.service.MaterialOpenApiDomainService;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class MaterialOpenApiDomainServiceImpl implements MaterialOpenApiDomainService {

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private ProductGroupRepository productGroupRepository;

    @Override
    public List<MaterialVo> openApiFuzzyQuery(MaterialFuzzyQueryDto queryDto) {
        List<String> actualGroupIds = getGroupCondition();
        if (CollectionUtils.isEmpty(actualGroupIds)) {
            return Collections.emptyList();
        }
        queryDto.setGroupId(actualGroupIds);
        log.debug("actualGroupIds: {}", actualGroupIds);
        List<MaterialVo> list = materialRepository.openApiFuzzyQuery(queryDto);
        list.forEach(item -> {
            item.setPurchaseMode(getPurchaseModeName(item.getPurchaseMode()));
        });
        return list;
    }

    /**
     * 获取所有符合条件的分组ID列表。
     *
     * @return 包含所有符合条件的分组ID的列表。
     */
    private List<String> getGroupCondition() {
        // 查询当前小类下所有叶子节点分组
        return productGroupRepository.queryingLeafAllNodeGroups();
    }

    /**
     * 根据采购模式ID获取对应的名称。
     *
     * @param purchaseMode 采购模式ID
     * @return 采购模式名称
     */
    private String getPurchaseModeName(String purchaseMode) {
        PurchaseModeEnums byId = PurchaseModeEnums.getById(purchaseMode);
        if (byId != null) {
            return I18nUtil.getI18nFromString(byId.getName());
        }
        return purchaseMode;
    }
}

/* Ended by AICoder, pid:eda60w5dc295ad514b040ac5f072bc674c562d3f */