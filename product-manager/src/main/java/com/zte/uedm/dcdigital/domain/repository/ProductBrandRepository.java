/* Started by AICoder, pid:49c64p79e0fe9b714538095410b57a2ace00129e */
package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.common.bean.brand.ProductBrandInfoVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;

import java.util.List;

/**
 * 产品品牌仓库接口，定义了与品牌相关的数据访问方法。
 */
public interface ProductBrandRepository {

    /**
     * 根据产品小类ID和品牌名称查询品牌详情。
     *
     * 该方法通过RPC调用远程服务来获取品牌详情信息。
     *
     * @param productCategoryId 产品小类ID，用于标识具体的产品分类。
     * @param brandName 品牌名称，用于标识具体的品牌。
     * @return 品牌详情信息，包含品牌的各种详细信息。
     */
    ProductBrandInfoVo selectProductBrandInfo(String productCategoryId, String brandName);
    /**
     * 品牌高级查询 根据产品小类ID、品牌名称、到期时间、评分、采购模式等查询产品品牌信息。
     *
     * @param productBrandDto 产品小类ID、品牌名称、到期时间、评分、采购模式。
     * @return 包含产品品牌信息的VO对象，如果没有找到则返回null。
     */
    List<ProductBrandVo> advancedQueryProductBrands(ProductBrandQueryDto productBrandDto);

    /* Started by AICoder, pid:o00bck4303zf40214e4d0b5f90bf94099c580d3b */
    /**
     * 根据产品类别ID和品牌名称查询品牌信息。
     *
     * @param productCategoryId 产品类别ID
     * @param brandName         品牌名称
     * @return 包含品牌信息的 ProductBrandInfoVo 对象列表
     */
    List<ProductBrandInfoVo> selectBrandByCategoryIdName(String productCategoryId, String brandName);
    /* Ended by AICoder, pid:o00bck4303zf40214e4d0b5f90bf94099c580d3b */
}

/* Ended by AICoder, pid:49c64p79e0fe9b714538095410b57a2ace00129e */