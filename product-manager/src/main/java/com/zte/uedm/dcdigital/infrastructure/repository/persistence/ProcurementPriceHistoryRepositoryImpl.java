/* Started by AICoder, pid:o3731l3232ha67114ae50ba5b01322682fa21aaa */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.common.enums.PriceCategoryEnums;
import com.zte.uedm.dcdigital.domain.model.material.entity.ProcurementPriceHistoryEntity;
import com.zte.uedm.dcdigital.domain.repository.ProcurementPriceHistoryRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProcurementCostConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProcurementPriceHistoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProcurementPriceHistoryPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购价格历史记录仓库实现类。
 * 提供对采购价格历史记录的增删查改操作。
 */
@Slf4j
@Repository
public class ProcurementPriceHistoryRepositoryImpl extends ServiceImpl<ProcurementPriceHistoryMapper, ProcurementPriceHistoryPo> implements ProcurementPriceHistoryRepository {

    @Resource
    private ProcurementPriceHistoryMapper priceHistoryMapper;
    /**
     * 批量添加采购价格历史记录。
     *
     * @param historyEntityList 要添加的历史记录列表
     */
    @Override
    public void batchAddHistory(List<ProcurementPriceHistoryEntity> historyEntityList) {
        List<ProcurementPriceHistoryPo> procurementPriceHistoryPos = ProcurementCostConvert.INSTANCE.priceHistoryEntityListToPoList(historyEntityList);
        this.saveBatch(procurementPriceHistoryPos);
    }

    /**
     * 添加单条采购价格历史记录。
     *
     * @param historyEntity 要添加的历史记录实体
     */
    @Override
    public void addPriceHistory(ProcurementPriceHistoryEntity historyEntity) {
        if (historyEntity == null) {
            return;
        }
        ProcurementPriceHistoryPo priceHistoryPo = ProcurementCostConvert.INSTANCE.priceHistoryEntityToPo(historyEntity);
        this.save(priceHistoryPo);
    }

    /**
     * 根据选型单ID、选型类型和价格类别查询采购价格历史记录。
     *
     * @param lectotypeId    选型单ID
     * @param lectotypeType  选型类型
     * @param priceCategory  价格类别
     * @return 符合条件的采购价格历史记录列表
     */
    @Override
    public List<ProcurementPriceHistoryEntity> queryPriceHistoryByCategory(String lectotypeId, String lectotypeType, String priceCategory) {
        LambdaQueryWrapper<ProcurementPriceHistoryPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcurementPriceHistoryPo::getLectotypeId, lectotypeId)
                .eq(ProcurementPriceHistoryPo::getLectotypeType, lectotypeType)
                .eq(ProcurementPriceHistoryPo::getPriceCategory, priceCategory)
                .orderByDesc(ProcurementPriceHistoryPo::getUpdateTime);
        List<ProcurementPriceHistoryPo> procurementPriceHistoryPos = baseMapper.selectList(queryWrapper);
        return ProcurementCostConvert.INSTANCE.priceHistoryPoListToEntityList(procurementPriceHistoryPos);
    }

    @Override
    public void deleteByLectotypeId(String lectotypeId) {
        LambdaQueryWrapper<ProcurementPriceHistoryPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcurementPriceHistoryPo::getLectotypeId, lectotypeId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void batchDeleteByLectotypeIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaQueryWrapper<ProcurementPriceHistoryPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProcurementPriceHistoryPo::getLectotypeId, ids);
        baseMapper.delete(queryWrapper);
    }


    /* Started by AICoder, pid:w928bbe7afrb8c71435b094b401a9a01ec67ab33 */
    @Override
    public List<ProcurementPriceHistoryEntity> getChangeMaterialPriceHistoryList(String materialId,String priceCategory) {
        List<ProcurementPriceHistoryPo> poList=priceHistoryMapper.getChangeMaterialPriceHistoryList(materialId,priceCategory);
        return ProcurementCostConvert.INSTANCE.priceHistoryPoListToEntityList(poList);
    }

    /* Ended by AICoder, pid:w928bbe7afrb8c71435b094b401a9a01ec67ab33 */
}
/* Ended by AICoder, pid:o3731l3232ha67114ae50ba5b01322682fa21aaa */