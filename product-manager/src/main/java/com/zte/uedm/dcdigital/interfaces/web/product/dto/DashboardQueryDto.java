/* Started by AICoder, pid:t1881t5381d7254147e00b06e08d074cff05e69d */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * DashboardQueryDto 类用于表示仪表板查询的数据传输对象。
 * 该类包含项目ID和产品小类ID，并提供验证方法以确保数据的有效性。
 */
@Getter
@Setter
@ToString
public class DashboardQueryDto {

    /**
     * 项目ID，标识具体的项目。此字段是必需的。
     */
    @NotBlank(message = "项目ID是必须的")
    private String projectId;

    /**
     * 产品小类ID，标识具体的产品类别。此字段是必需的。
     */
    @NotBlank(message = "产品小类ID是必须的")
    private String productCategoryId;

    /**
     * 验证方法，确保所有必需字段都已正确填写。
     * 如果验证失败，将抛出业务异常。
     */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER.getCode(), validResult.getErrorMessage());
        }
    }
}
/* Ended by AICoder, pid:t1881t5381d7254147e00b06e08d074cff05e69d */