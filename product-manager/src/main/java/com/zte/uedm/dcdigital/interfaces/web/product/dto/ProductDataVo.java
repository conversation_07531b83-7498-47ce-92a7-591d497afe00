package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
public class ProductDataVo {

    private int totalVisits;         // 访问总数
    private int brandVisits;         // 品牌访问总数
    private int materialVisits;      // 物料访问总数
    private int documentVisits;      // 文档访问总数
    private int faqVisits;           // FAQ访问总数


    private List<PageStatisticData> pageStatisticData;   // 产品页面统计数据;

    private List<ProductStatisticTop10> productStatisticTop10;   // 产品统计数据(访问量Top10)

    private List<BrandStatisticTop20> brandStatisticTop20;   // 品牌统计数据(访问量Top20)

    private List<MaterialStatisticTop50> materialStatisticTop50;   // 物料统计数据(访问量Top50)

    private List<DocumentStatisticTop50> documentStatisticTop50;   // 文档统计数据(访问量Top50)

    private List<FAQStatisticTop50> faqStatisticTop50;   // FAQ统计数据(访问量Top50)

    private List<DetailStatisticData> detailStatistic;   //详情统计

    @Getter
    @Setter
    @ToString
    public static class DetailStatisticData {
        private Map<String, Integer>  valueByEveryTime;// 值
        private String productType; //产品类型
        private Integer total; //产品类型
    }

    @Getter
    @Setter
    @ToString
    public static class PageStatisticData {
        private Map<String, Integer>  valueByEveryTime;// 值
        private String productType; //产品类型
    }

    @Getter
    @Setter
    @ToString
    public static class ProductStatisticTop10 {

        private String name;
        private Integer value;
        public ProductStatisticTop10(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

    }

    @Getter
    @Setter
    @ToString
    public static class BrandStatisticTop20 {

        private String name;
        private Integer value;


        public BrandStatisticTop20(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

    }

    @Getter
    @Setter
    @ToString
    public static  class MaterialStatisticTop50 {

        private String name;
        private Integer value;

        public MaterialStatisticTop50(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

    }

    @Getter
    @Setter
    @ToString
    public static  class DocumentStatisticTop50 {

        private String name;
        private Integer value;

        public DocumentStatisticTop50(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

    }

    @Getter
    @Setter
    @ToString
    public static class FAQStatisticTop50 {

        private String name;
        private Integer value;

        public FAQStatisticTop50(String name, Integer value) {
            this.name = name;
            this.value = value;
        }
    }

}