/* Started by AICoder, pid:baa58o59e35a34d149e408968094956c609729d7 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryExtraInfoRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProductCategoryExtraInfoConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryExtraInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryExtraInfoPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class ProductCategoryExtraInfoRepositoryImpl extends ServiceImpl<ProductCategoryExtraInfoMapper,ProductCategoryExtraInfoPo> implements ProductCategoryExtraInfoRepository {


    @Autowired
    private ProductCategoryExtraInfoMapper productCategoryExtraInfoMapper;

    @Override
    public List<ProductCategoryExtraInfoEntity> queryExtraInfoByProductCategoryIds(List<String> productCategoryIds) {
        LambdaQueryWrapper<ProductCategoryExtraInfoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(productCategoryIds), ProductCategoryExtraInfoPo::getProductCategoryId, productCategoryIds);
        List<ProductCategoryExtraInfoPo> productCategoryExtraInfoPos = productCategoryExtraInfoMapper.selectList(queryWrapper);
        return ProductCategoryExtraInfoConvert.INSTANCE.categoryExtraInfoPoListToCategoryExtraInfoEntityList(productCategoryExtraInfoPos);
    }

    @Override
    public void updateProductCategoryExtraInfo(ProductCategoryExtraInfoEntity categoryEntity) {
        ProductCategoryExtraInfoPo extraInfoPo = ProductCategoryExtraInfoConvert.INSTANCE.categoryExtraInfoEntityToCategoryExtraInfoPo(categoryEntity);
        productCategoryExtraInfoMapper.updateById(extraInfoPo);
    }

    @Override
    public void addProductCategoryExtraInfo(ProductCategoryExtraInfoEntity categoryEntity) {
        ProductCategoryExtraInfoPo extraInfoPo = ProductCategoryExtraInfoConvert.INSTANCE.categoryExtraInfoEntityToCategoryExtraInfoPo(categoryEntity);
        productCategoryExtraInfoMapper.insert(extraInfoPo);
    }

    @Override
    public void deleteExtraInfoByProductCategoryId(String productCategoryId) {
        productCategoryExtraInfoMapper.deleteByProductCategoryId(productCategoryId);
    }

    @Override
    public ProductCategoryExtraInfoEntity queryExtraInfoByProductCategoryId(String productCategoryId) {
        LambdaQueryWrapper<ProductCategoryExtraInfoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductCategoryExtraInfoPo::getProductCategoryId, productCategoryId);
        ProductCategoryExtraInfoPo extraInfoPo = productCategoryExtraInfoMapper.selectOne(queryWrapper);
        return ProductCategoryExtraInfoConvert.INSTANCE.productCategoryExtraInfoPoToCategoryExtraInfoEntity(extraInfoPo);
    }

    /* Started by AICoder, pid:9c8ffdd46em865b148e70866e0df77030908da93 */
    @Override
    public void updateExtendedWarrantyFactorAndNonStandardItems(ProductCategoryExtraInfoEntity extraInfoEntity) {
        // 将实体对象转换为持久化对象
        ProductCategoryExtraInfoPo extraInfoPo = ProductCategoryExtraInfoConvert.INSTANCE.categoryExtraInfoEntityToCategoryExtraInfoPo(extraInfoEntity);

        // 调用数据访问层方法更新数据库记录
        productCategoryExtraInfoMapper.updateExtendedWarrantyFactorAndNonStandardItems(extraInfoPo);
    }
    /* Ended by AICoder, pid:9c8ffdd46em865b148e70866e0df77030908da93 */
}
/* Ended by AICoder, pid:baa58o59e35a34d149e408968094956c609729d7 */