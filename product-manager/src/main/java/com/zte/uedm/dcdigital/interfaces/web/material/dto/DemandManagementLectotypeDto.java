package com.zte.uedm.dcdigital.interfaces.web.material.dto;

/* Started by AICoder, pid:fdf861e297393b8147a4081aa058858a2962dec2 */
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * DemandManagementLectotypePo 类表示需求管理的定标类型持久化对象。
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class DemandManagementLectotypeDto {

    /**
     * lectotypeId 属性表示定标类型的唯一标识符。
     */
    private String lectotypeId;

    /**
     * demandId 属性表示需求的ID。
     */
    private String demandId;

    /**
     * lectotypeName 属性表示定标类型的名称。
     */
    private String lectotypeName;

    /**
     * lectotypeType 属性表示定标类型的类型。
     */
    private String lectotypeType;

    /**
     * lectotypeStatus 属性表示定标类型的状态。
     */
    private Integer lectotypeStatus;

    /**
     * 洽谈价格，保留两位小数。
     * 仅在选型单类型为“指定”时更新。
     */
    private BigDecimal negotiatedPrice;

    /**
     * 基准目标价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal datumTargetPrice;

    /**
     * 挑战目标价价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal challengeTargetPrice;

    /**
     * 开标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal openTenderPrice;

    /**
     * 定标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal setBidPrice;


    /**
     * sendBidTime 属性表示发送投标的时间。
     */
    private String sendBidTime;

    /**
     * endTime 属性表示结束时间。
     */
    private String endTime;

    /**
     * open_bidTime 属性表示开标时间。
     */
    private String openBidTime;


    /**
     * bidUrl 属性表示投标的URL。
     */
    private String bidUrl;

    /**
     * createBy 属性表示创建者。
     */
    private String createBy;

    /**
     * createTime 属性表示创建时间。
     */
    private String createTime;

    /**
     * updateBy 属性表示更新者。
     */
    private String updateBy;

    /**
     * updateTime 属性表示更新时间。
     */
    private String updateTime;

    /**
     * fileIdList 属性表示文件ID列表。
     */
    private List<String> fileIdList;

    /**
     * fileIds 属性表示文件ID的JSON字符串。
     */
    private String fileIds;
}
/* Ended by AICoder, pid:fdf861e297393b8147a4081aa058858a2962dec2 */
