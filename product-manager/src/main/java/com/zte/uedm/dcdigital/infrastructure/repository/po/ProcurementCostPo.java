/* Started by AICoder, pid:a12df3ff80c409614f490a0b7197d41c828677a5 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 采购成本持久化对象 (PO)。
 * 用于数据库表 `procurement_cost` 的映射，包含采购成本的相关信息。
 */
@Getter
@Setter
@ToString
@TableName("procurement_cost")
public class ProcurementCostPo {

    /**
     * id，必填。
     * 唯一标识。
     *
     * @LogMark 注解标记此字段在删除、添加和更新操作中需要记录日志。
     */
    @LogMark(range = {OperationMethodEnum.DELETE, OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String id;

    /**
     * 归属选型单id，必填。
     * 关联的选型单的唯一标识。
     */
    @TableField("lectotype_id")
    private String lectotypeId;

    /**
     * 归属选型类型，必填。
     * 选型单的类型，例如招标或指定。
     */
    @TableField("lectotype_type")
    private String lectotypeType;

    /**
     * 洽谈价。
     * 记录采购过程中的洽谈价格。
     */
    @TableField("negotiated_price")
    private String negotiatedPrice;

    /**
     * 基准目标价格。
     * 记录采购过程中的基准目标价格。
     */
    @TableField("datum_target_price")
    private String datumTargetPrice;

    /**
     * 挑战目标价。
     * 记录采购过程中的挑战目标价格。
     */
    @TableField("challenge_target_price")
    private String challengeTargetPrice;

    /**
     * 开标价。
     * 记录采购过程中的开标价格。
     */
    @TableField("open_tender_price")
    private String openTenderPrice;

    /**
     * 定标价。
     * 记录采购过程中的定标价格。
     */
    @TableField("set_bid_price")
    private String setBidPrice;

    /**
     * 发标时间。
     * 记录采购过程中发标的时间。
     */
    @TableField("bid_issuing_time")
    private String bidIssuingTime;

    /**
     * 开标时间。
     * 记录采购过程中开标的时间。
     */
    @TableField("bid_opening_time")
    private String bidOpeningTime;

    /**
     * 创建时间，必填。
     * 记录采购记录的创建时间。
     */
    private String createTime;

    /**
     * 更新时间，必填。
     * 记录采购记录的最后更新时间。
     */
    private String updateTime;

    /**
     * 创建用户，必填。
     * 记录创建该采购记录的用户。
     */
    private String createBy;

    /**
     * 更新用户，必填。
     * 记录最后更新该采购记录的用户。
     */
    private String updateBy;
}
/* Ended by AICoder, pid:a12df3ff80c409614f490a0b7197d41c828677a5 */