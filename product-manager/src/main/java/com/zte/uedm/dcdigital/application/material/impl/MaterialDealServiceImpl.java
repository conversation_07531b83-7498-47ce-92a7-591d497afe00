package com.zte.uedm.dcdigital.application.material.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.application.category.RequirementDashboardCommandService;
import com.zte.uedm.dcdigital.application.group.executor.ProductGroupQueryService;
import com.zte.uedm.dcdigital.application.material.MaterialDealService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceCommandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.PurchaseModeEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;
import com.zte.uedm.dcdigital.domain.common.utils.ExcelImportBuilder;
import com.zte.uedm.dcdigital.domain.common.utils.JtExcelFieldIn;
import com.zte.uedm.dcdigital.domain.common.utils.OperationLogUtils;
import com.zte.uedm.dcdigital.domain.common.utils.SalesCodeUtils;
import com.zte.uedm.dcdigital.domain.repository.MaterialRepository;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialEditDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialDealPageVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialDealVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupDetailVo;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationResultOptional;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class MaterialDealServiceImpl implements MaterialDealService {

    @Autowired
    private MaterialMaintenanceCommandService materialMaintenanceCommandService;

    @Autowired
    private MaterialMaintenanceQueryService materialMaintenanceQueryService;

    @Autowired
    private MaterialDomainService materialDomainService;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private ProductGroupQueryService productGroupQueryService;

    @Autowired
    private ProductGroupMapper productGroupMapper;

    @Autowired
    private RequirementDashboardCommandService commandService;

    @Autowired
    private AuthService authService;

    public List<JtExcelFieldIn> buildList() {
        List<JtExcelFieldIn> list = new ArrayList<>();
        JtExcelFieldIn excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("id");
        excelFieldIn.setColIndex('A');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("productPathName");
        excelFieldIn.setColIndex('B');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("groupName1");
        excelFieldIn.setColIndex('C');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("groupName2");
        excelFieldIn.setColIndex('D');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("name");
        excelFieldIn.setColIndex('E');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("nameEn");
        excelFieldIn.setColIndex('F');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("brand");
        excelFieldIn.setColIndex('G');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("specificationModel");
        excelFieldIn.setColIndex('H');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("purchaseMode");
        excelFieldIn.setColIndex('I');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("supplier");
        excelFieldIn.setColIndex('J');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("unit");
        excelFieldIn.setColIndex('K');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("description");
        excelFieldIn.setColIndex('L');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("salesCode");
        excelFieldIn.setColIndex('M');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("productionCode");
        excelFieldIn.setColIndex('N');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("salesStatus");
        excelFieldIn.setColIndex('O');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("expirationDate");
        excelFieldIn.setColIndex('P');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("service");
        excelFieldIn.setColIndex('Q');
        list.add(excelFieldIn);
        excelFieldIn = new JtExcelFieldIn();
        excelFieldIn.setColumn("recommendedLevel");
        excelFieldIn.setColIndex('R');
        list.add(excelFieldIn);
        String json = JSON.toJSONString(list);
        List<JtExcelFieldIn> fields = JSON.parseArray(json, JtExcelFieldIn.class);
        return fields;
    }

    @Override
    public MaterialDealPageVo importUpdMaterials(FormDataMultiPart file) {
        MaterialDealPageVo queryDto = new MaterialDealPageVo();
        // 构建 Excel 导入构建器
        ExcelImportBuilder builder = new ExcelImportBuilder.Builder(buildList(), MaterialDealVo.class).sheetName("物料清单").startLineNum(3).build();
        FormDataBodyPart part = file.getField("file");
        List<MaterialDealVo> list1 = builder.imports(part.getValueAs(InputStream.class));
        List<String> units = new ArrayList<>();
        units.add("套-KIT");
        units.add("块-PCS");
        units.add("个-PCS");
        units.add("根-PCS");
        units.add("台-SET");
        units.add("件-PCS");
        units.add("米-m");
        units.add("双-PR");
        units.add("组-GR");
        units.add("千米-km");
        units.add("平方米-m2");
        units.add("批-BAT");
        List<String> salesCodes = new ArrayList<>();
        salesCodes.add("正式销售");
        salesCodes.add("仅限报价");
        salesCodes.add("新品控制");
        salesCodes.add("退市控制");
        salesCodes.add("临时禁止");
        salesCodes.add("策略销售");
        if (list1 != null && list1.size() > 0) {
            for (MaterialDealVo t : list1) {
                MaterialVo me = materialRepository.multiTableQueryById(t.getId());
                if (me == null) {
                    t.setCheckResult("1");
                    t.setErrorReason("没有获取到物料");
                    continue;
                }

                check1(t, me);
                check1(t);
                check2(t,units);
                check3(t,salesCodes);
                check(t);

                if(StringUtils.isNotBlank(t.getErrorReason().trim())){
                    t.setCheckResult("1");
                }else {
                    t.setCheckResult("0");
                }

            }
        } else {
            throw new BusinessException(ProductCategoryStatusCode.READING_EXCEL_FAIL);
        }
        queryDto.setMaterial(list1);
        queryDto.setTotal(list1.size());
        return queryDto;
    }
    private void check1(MaterialDealVo t, MaterialVo me){
        String errorReason = "";
        String paId = null;
        if (StringUtils.isBlank(t.getGroupName1())) {
            errorReason = errorReason + "没有产品分组L1，";
        } else {
            ProductGroupPo po = productGroupMapper.queryProductGroup(me.getProductId(), t.getGroupName1(), null);
            if (null == po) {
                errorReason = errorReason + "产品分组L1填写错误，";
            } else {
                paId = po.getId();
            }
        }
        if (StringUtils.isBlank(t.getGroupName2())) {
            errorReason = errorReason + "没有产品分组L2，";
        } else {
            if (null != paId) {
                ProductGroupPo po = productGroupMapper.queryProductGroup(me.getProductId(), t.getGroupName2(), paId);
                if (null == po) {
                    errorReason = errorReason + "产品分组L2填写错误，";
                } else {
                    t.setGroupId(po.getId());
                }
            }
        }
        t.setErrorReason(errorReason);
    }
    private void check1(MaterialDealVo t){
        String errorReason = t.getErrorReason();
        if (StringUtils.isBlank(t.getName())) {
            errorReason = errorReason + "没有物料名称，";
        }
        if (StringUtils.isBlank(t.getNameEn())) {
            errorReason = errorReason + "没有物料名称（英文），";
        }
        if (StringUtils.isNotBlank(t.getPurchaseMode())) {
            PurchaseModeEnums byId = PurchaseModeEnums.getByCnName(t.getPurchaseMode());
            if (null == byId) {
                errorReason = errorReason + "采购模式填写不规范，";
            }
        } else {
            errorReason = errorReason + "没有采购模式，";
        }
        t.setErrorReason(errorReason);
    }
    private void check2(MaterialDealVo t, List<String> units){
        String errorReason = t.getErrorReason();
        if (StringUtils.isBlank(t.getUnit())) {
            errorReason = errorReason + "没有单位，";
        } else {
            if (!units.contains(t.getUnit())) {
                errorReason = errorReason + "单位填写不规范，";
            }
        }
        if (StringUtils.isBlank(t.getDescription())) {
            errorReason = errorReason + "没有描述，";
        }
        if (StringUtils.isNotBlank(t.getRecommendedLevel())) {
            if (!"A".equals(t.getRecommendedLevel()) && !"B".equals(t.getRecommendedLevel()) &&
                    !"C".equals(t.getRecommendedLevel())) {
                errorReason = errorReason + "推荐等级填写不规范，";
            }
        }
        t.setErrorReason(errorReason);
    }
    private void check3(MaterialDealVo t, List<String> salesCodes){
        String errorReason = t.getErrorReason();
        String salesCode = t.getSalesCode();
        if (StringUtils.isNotBlank(salesCode)) {
            if (salesCode.startsWith("18")) {
                if (!salesCodes.contains(t.getSalesStatus())) {
                    errorReason = errorReason + "销售状态填写不规范，";
                }
                if (!materialDomainService.uniqueCheckForPdm1(salesCode, t.getBrand(), t.getId())) {
                    errorReason = errorReason + "销售代码品牌校验重复，";
                }
            } else if (salesCode.startsWith("OTH")) {
                if (!"仅限报价".equals(t.getSalesStatus())) {
                    errorReason = errorReason + "OTH销售状态只能填仅限报价，";
                }
                if (!materialDomainService.nonPdmNameUniqueCheck1(t.getName(), salesCode, t.getId())) {
                    errorReason = errorReason + "销售代码名称校验重复，";
                }
            } else {
                errorReason = errorReason + "销售代码填写不规范，";
            }
        }
        t.setErrorReason(errorReason);
    }
    private void check(MaterialDealVo t) {

        String errorReason = t.getErrorReason();
        if (StringUtils.isBlank(t.getSalesStatus())) {
            errorReason = errorReason + "没有销售状态，";
        }
        if (StringUtils.isNotBlank(t.getProductionCode())) {
            if (!t.getProductionCode().startsWith("05") && !t.getProductionCode().startsWith("06") &&
                    !t.getProductionCode().startsWith("12")) {
                errorReason = errorReason + "生产代码填写不规范，";
            }
        }

        if (StringUtils.isNotBlank(t.getExpirationDate())) {
            errorReason = errorReason + getFormatDateStr(t.getExpirationDate());
        }
        t.setErrorReason(errorReason);
    }

    private String getFormatDateStr(String expirationDate) {
        LocalDate now = LocalDate.now();
        LocalDate localDate = LocalDate.parse(expirationDate, DateTimeUtils.DATE_FORMATTER);
        if (localDate.isBefore(now)) {
            return "失效日期已过期";
        }
        return "";
    }

    private List<MaterialDealVo> getMaterialDealVos(String materialIds) {
        String[] materialIdArr = materialIds.split(",");
        List<String> materialIdList = Arrays.asList(materialIdArr);
        if (CollectionUtils.isEmpty(materialIdList)) {
            return null;
        }
        List<MaterialDealVo> list = new ArrayList<>();
        for (String id : materialIdList) {
            MaterialDetailVo result = materialMaintenanceQueryService.queryMaterialDetailsById(id);
            MaterialDealVo materialDealVo = new MaterialDealVo();
            materialDealVo.setId(result.getMaterial().getId());
            materialDealVo.setProductPathName(result.getMaterial().getProductPathName());
            String groupName1 = "";
            String groupName2 = "";
            ProductGroupDetailVo productGroupDetail = productGroupQueryService.queryProductGroupDetail(result.getMaterial().getGroupId());
            if (null != productGroupDetail) {
                String[] groupName = productGroupDetail.getPathName().split("/");
                if (groupName.length == 1) {
                    groupName1 = groupName[0];
                }
                if (groupName.length == 2) {
                    groupName1 = groupName[0];
                    groupName2 = groupName[1];
                }
            }
            materialDealVo.setGroupName1(groupName1);
            materialDealVo.setGroupName2(groupName2);
            materialDealVo.setName(result.getMaterial().getName());
            materialDealVo.setNameEn(result.getMaterial().getNameEn());
            materialDealVo.setBrand(result.getMaterial().getBrand());
            materialDealVo.setSpecificationModel(result.getMaterial().getSpecificationModel());
            materialDealVo.setSupplier(result.getMaterial().getSupplier());
            materialDealVo.setPurchaseMode(PurchaseModeEnums.EXCEL_PURCHASE_MODE_ENUMS_MAP2.get(result.getMaterial().getPurchaseModeId()));
            String unit = result.getMaterial().getUnit();
            if (StringUtils.isNotBlank(result.getMaterial().getUnitEn())) {
                unit = result.getMaterial().getUnit() + "-" + result.getMaterial().getUnitEn();
            }
            materialDealVo.setUnit(unit);
            materialDealVo.setDescription(result.getMaterial().getDescription());
            materialDealVo.setSalesCode(result.getMaterial().getSalesCode());
            materialDealVo.setProductionCode(result.getMaterial().getProductionCode());
            materialDealVo.setSalesStatus(result.getMaterial().getSalesStatus());
            materialDealVo.setExpirationDate(result.getMaterial().getExpirationDate());
            materialDealVo.setService(result.getMaterial().getService());
            materialDealVo.setRecommendedLevel(result.getMaterial().getRecommendedLevel());
            list.add(materialDealVo);
        }

        return list;
    }

    @Override
    public void exportUpdMaterials(String materialIds, HttpServletResponse response) {
        try {
            // 获取模板文件输入流
            ClassPathResource resource = new ClassPathResource("template" + File.separator + "material-imp-template.xlsx");
            log.info("templateStream:{}", JSON.toJSONString(resource));
            InputStream templateStream = resource.getInputStream();

            try {
                String fileNameStr = "Material" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
                String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
            } catch (Exception e) {
                log.error("set response header error", e);
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateStream)
                    .registerWriteHandler(setStyle())
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE)
                    .sheet()
                    .doFill(getMaterialDealVos(materialIds));

        } catch (IOException e) {
            log.error("export productCoreParamList data is filed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    private HorizontalCellStyleStrategy setStyle() {
        // 定义样式：自动换行
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 关键：开启自动换行

        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Microsoft YaHei"); // 字体
        writeFont.setFontHeightInPoints((short) 12); // 字体大小
        contentWriteCellStyle.setWriteFont(writeFont);

        // 注册样式策略（全局生效）
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                null, // 头样式（默认）
                contentWriteCellStyle // 内容样式（自动换行）
        );

        return styleStrategy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void UpdMaterials(List<MaterialDealVo> list) {
        for (MaterialDealVo t : list) {
            String id = t.getId();
            MaterialEditDto editDto = new MaterialEditDto();
            editDto.setId(id);
            editDto.setGroupId(t.getGroupId());
            editDto.setName(t.getName());
            editDto.setNameEn(t.getNameEn());
            editDto.setBrand(t.getBrand());
            editDto.setSpecificationModel(t.getSpecificationModel());
            editDto.setSupplier(t.getSupplier());
            editDto.setPurchaseMode(PurchaseModeEnums.getByCnName(t.getPurchaseMode()).getId());
            String[] units = t.getUnit().split("-");
            editDto.setUnit(units[0]);
            editDto.setUnitEn(units[1]);
            editDto.setDescription(t.getDescription());
            editDto.setSalesCode(t.getSalesCode());
            editDto.setProductionCode(t.getProductionCode());
            editDto.setSalesStatus(t.getSalesStatus());
            editDto.setExpirationDate(t.getExpirationDate());
            editDto.setService(t.getService());
            editDto.setRecommendedLevel(t.getRecommendedLevel());

            String salesCode = editDto.getSalesCode();
            String othInfoId = null;
            if (SalesCodeUtils.isPdmSalesCode(salesCode)) {
                materialDomainService.uniqueCheckForPdm(salesCode, editDto.getBrand(), id);
            } else {
                //非pdm的
                //名称唯一性校验
                materialDomainService.nonPdmNameUniqueCheck(editDto.getName(), salesCode, id);
                //销售代码唯一性校验
                othInfoId = materialDomainService.addOrUpdateOthInfo(editDto);
            }
            materialDomainService.editMaterial1(editDto, othInfoId);
        }

    }

    @Override
    public void delMaterials(String materialIds) {
        String[] materialIdArr = materialIds.split(",");
        List<String> materialIdList = Arrays.asList(materialIdArr);
        if (CollectionUtils.isNotEmpty(materialIdList)) {
            materialMaintenanceCommandService.batchDeleteMaterial(materialIdList);
        }
        try{
            String userId = authService.getUserId();
            String detail = materialIds;
            OperationLogBean operationLogBean = OperationLogUtils.buildOperationLogBean(GlobalConstants.MATERIAL_DELETES,
                    OperationTypeOptional.OPERATION_TYPE_DEL,
                    userId, OperationResultOptional.OPERATION_RESULT_SUCCESS.getId(), detail, OperationLogRankEnum.IMPORTANT.getId());
            log.info("operationLogBean:{}",JSON.toJSONString(operationLogBean));
            commandService.sendKafkaMessage(operationLogBean);
        } catch (Exception e) {
            log.error("send log message failed", e);
        }
    }
}
