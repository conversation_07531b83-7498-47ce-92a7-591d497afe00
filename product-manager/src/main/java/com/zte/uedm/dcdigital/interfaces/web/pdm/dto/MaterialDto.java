package com.zte.uedm.dcdigital.interfaces.web.pdm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
public class MaterialDto {


    /**
     * 查询方式：1 按物料编号查询；2 按生产编码，该参必传
     */
    private String queryType;


    /**
     * PDM物料编号，queryType为1时传此参数
     */
    private String sbomNo;

    /**
     * PDM物料名称，queryType为1时传此参数
     */
    private String sbomName;

    /**
     * PDM生产编码，queryType为2时传此参数
     */
    private String partNo;

    /**
     * 页码，必填。
     */
    @NotNull(message = "pageNum cannot be null")
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 每页数量，必填。
     */
    @NotNull(message = "pageSize cannot be null")
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
}
