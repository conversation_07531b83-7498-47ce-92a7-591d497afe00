/* Started by AICoder, pid:i97f2bb571ne190143ce085540e6c62775440197 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/12/24
 * @Description:  物料模糊查询请求体
 */

@Getter
@Setter
@ToString
@Slf4j
public class SelectionFormMaterialQueryDto {

    /**
     * 对物料的名称、品牌、采购模式、生产代码进行模糊搜索
     */
    private String fuzzyName;
    /**
     * 归属选型单
     */
    @NotBlank(message = "选型单id不能为空")
    private String lectotypeId;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     *  排序顺序
     */
    private String sortOrder;


    public void validate() {
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[check param] Parameter is blank. {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

    }
}
/* Ended by AICoder, pid:i97f2bb571ne190143ce085540e6c62775440197 */