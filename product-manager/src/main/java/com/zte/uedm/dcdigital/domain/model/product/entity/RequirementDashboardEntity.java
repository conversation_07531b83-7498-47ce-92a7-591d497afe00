/* Started by AICoder, pid:39163ha61ecb23514af3099dd1e63a146044980e */
package com.zte.uedm.dcdigital.domain.model.product.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * RequirementDashboardEntity 表示用于跟踪需求的仪表板实体。
 * 该类包含各种字段，用于跟踪项目中产品需求的状态和详细信息。
 */
@Getter
@Setter
@ToString
public class RequirementDashboardEntity {
 /**
  * 主表的主键ID。
  */
 private String id;

 /**
  * 产品类别的ID。
  */
 private String productCategoryId;

 /**
  * 项目的ID。
  */
 private String projectId;

 /**
  * 资源确认模式，可以包含多个选项，如招标、自研、战采、框标、独家、生态或取消。
  */
 private String[] resourceConfirmationMode;

 /**
  * 需求确认时间
  */
 private String demandConfirmationTime;

 /**
  * 期望的招标开标时间。
  */
 private String expectBidOpenTime;

 /**
  * 需求的状态（0 - 正常，1 - 取消）。默认为正常。
  */
 private Integer status;

 /**
  * 招标TS发起时间，表示实际的招标TS发起时间。
  */
 private String tenderLaunchTime;

 /**
  * 招标TS关闭时间，表示实际的招标TS关闭时间。
  */
 private String biddingCloseTime;


 /**
  * 实际的开标时间。
  */
 private String actualBidOpeningTime;

 /**
  * 投标品牌。
  */
 private String biddingBrand;

 /**
  * 代码状态，默认最后一个选型单据中的最后一次物料上架审批状态，待提交审批（选型单无物料或者物料无上架审批单）、上架审批中、已上架。
  */
 private String codeStatus;

 /**
  * 备注或附加信息。
  */
 private String remarks;

 /**
  * 创建记录的用户。
  */
 private String createBy;

 /**
  * 记录的创建时间。
  */
 private String createTime;

 /**
  * 最后更新记录的用户。
  */
 private String updateBy;

 /**
  * 记录的最后更新时间。
  */
 private String updateTime;
}
/* Ended by AICoder, pid:39163ha61ecb23514af3099dd1e63a146044980e */