<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialMapper">

    <resultMap id="queryMaterialMap" type="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo" autoMapping="true">
        <result column="documentIds" property="documentIds" typeHandler="org.apache.ibatis.type.ArrayTypeHandler"/>
    </resultMap>
    <insert id="batchInsert">
        INSERT INTO material
            (id, approval_id,name,brand,supplier,purchase_mode,expiration_date,warranty_period,cost,
            delivery_days,group_id, pdm_info_id,material_status,version,specification_model,service,oth_info_id,
        create_by,update_by,create_time,update_time,specification_remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.approvalId}, #{item.name}, #{item.brand}, #{item.supplier}, #{item.purchaseMode},
            #{item.expirationDate}, #{item.warrantyPeriod}, #{item.cost}, #{item.deliveryDays}, #{item.groupId},
            #{item.pdmInfoId}, #{item.materialStatus}, #{item.version},#{item.specificationModel}, #{item.service},#{item.othInfoId},
            #{item.createBy}, #{item.updateBy},
            #{item.createTime},#{item.updateTime},#{item.specificationRemark})
        </foreach>
    </insert>
    <update id="batchUpdateMaterialStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE material set
            <if test="item.name!=null and item.name!=''">
                "name" = #{item.name},
            </if>
            <if test="item.approvalId!=null and item.approvalId!=''">
                approval_id = #{item.approvalId},
            </if>
            <if test="item.brand!=null and item.brand!=''">
                brand = #{item.brand},
            </if>
            <if test="item.supplier!=null and item.supplier!=''">
                supplier = #{item.supplier},
            </if>
            <if test="item.purchaseMode!=null and item.purchaseMode!=''">
                purchase_mode = #{item.purchaseMode},
            </if>
            <if test="item.expirationDate!=null and item.expirationDate!=''">
                expiration_date = #{item.expirationDate},
            </if>
            <if test="item.warrantyPeriod!=null and item.warrantyPeriod!=''">
                warranty_period = #{item.warrantyPeriod},
            </if>
            <if test="item.cost!=null and item.cost!=''">
                cost = #{item.cost},
            </if>
            <if test="item.deliveryDays!=null and item.deliveryDays!=''">
                delivery_days = #{item.deliveryDays},
            </if>
            <if test="item.groupId!=null and item.groupId!=''">
                group_id = #{item.groupId},
            </if>
            <if test="item.pdmInfoId!=null and item.pdmInfoId!=''">
                pdm_info_id = #{item.pdmInfoId},
            </if>
            <if test="item.materialStatus!=null and item.materialStatus!=''">
                material_status = #{item.materialStatus},
            </if>
            <if test="item.version!=null and item.version!=''">
                version = #{item.version},
            </if>
            <if test="item.createBy!=null and item.createBy!=''">
                create_by = #{item.createBy},
            </if>
            <if test="item.updateBy!=null and item.updateBy!=''">
                update_by = #{item.updateBy},
            </if>
            <if test="item.createTime!=null and item.createTime!=''">
                create_time = #{item.createTime},
            </if>
            <if test="item.updateTime!=null and item.updateTime!=''">
                update_time = #{item.updateTime}
            </if>
            <if test="item.specificationRemark!=null and item.specificationRemark!=''">
                specification_remark = #{item.specificationRemark}
            </if>
            where id = #{item.id}
        </foreach>
    </update>
    <update id="updateBatchGroupIdById">
        UPDATE material
        SET group_id = CASE
        <foreach collection="materialPos" item="material" separator=" ">
            WHEN id = #{material.id} THEN #{material.groupId}
        </foreach>
        ELSE group_id
        END
        WHERE id IN
        <foreach collection="materialPos" item="material" open="(" close=")" separator=",">
            #{material.id}
        </foreach>
    </update>
    <update id="updateDraftMaterialRelation">
        UPDATE material
        SET pdm_info_id = #{pdmInfoId}, oth_info_id = #{othInfoId}
        WHERE id = #{id}
    </update>
    <select id="unionQueryByApprovalId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean">
        SELECT
        a.id AS id,
        a.name AS name,
        a.approval_id AS approvalId,
        a.brand AS brand,
        a.supplier AS supplier,
        a.purchase_mode AS purchaseMode,
        a.expiration_date AS expirationDate,
        a.warranty_period AS warrantyPeriod,
        a.cost AS cost,
        a.delivery_days AS deliveryDays,
        a.group_id AS groupId,
        d.path_name AS groupPathName,
        e.id AS productId,
        e.path_name AS productPathName,
        a.material_status AS materialStatus,
        a.version,
        c.sales_code AS salesCode,
        c.production_code AS productionCode,
        c.sales_status AS salesStatus,
        c.unit,
        c.description
        FROM material a
        LEFT JOIN pdm_info c ON a.pdm_info_id = c.id
        WHERE a.approval_id = #{approvalId}
        ORDER BY a.name
    </select>
    <select id="queryLatestMaterialById"
            resultMap="queryMaterialMap">
        WITH material_data AS (
        SELECT
        COALESCE(b.material_id, a.id) AS id,
        COALESCE(b.approval_id, a.approval_id) AS approvalId,
        COALESCE(b.name, a.name) AS name,
        COALESCE(b.name_en, a.name_en) AS name_en,
        COALESCE(b.brand, a.brand) AS brand,
        COALESCE(b.supplier, a.supplier) AS supplier,
        COALESCE(b.purchase_mode, a.purchase_mode) AS purchaseMode,
        COALESCE(b.expiration_date, a.expiration_date) AS expirationDate,
        COALESCE(b.warranty_period, a.warranty_period) AS warrantyPeriod,
        COALESCE(b.cost, a.cost) AS cost,
        COALESCE(b.delivery_days, a.delivery_days) AS deliveryDays,
        COALESCE(b.group_id, a.group_id) AS groupId,
        a.material_status AS materialStatus,
        a.version,
        b.document_ids AS documentIds,
        COALESCE(b.specification_model, a.specification_model) AS specificationModel,
        COALESCE(b.recommended_level, a.recommended_level) AS recommendedLevel,
        COALESCE(b.description, a.description) AS description,
        COALESCE(b.unit, a.unit) AS unit,
        COALESCE(b.unit_en, a.unit_en) AS unitEn,
        COALESCE(b.service, a.service) AS service,
        COALESCE(b.pdm_info_id, a.pdm_info_id) AS pdmInfoId,
        a.oth_info_id,
        a.specification_remark
        FROM material a
        LEFT JOIN material_temporary b ON a.id = b.material_id
        WHERE a.id = #{id}
        )
        SELECT
        md.*,
        COALESCE(c.sales_code, oth.sales_code) AS salesCode,
        COALESCE(c.production_code, oth.production_code) AS productionCode,
        COALESCE(c.sales_status, oth.sales_status) AS salesStatus,
        d.path_name AS groupPathName,
        e.id AS productId,
        e.path_name AS productPathName
        FROM material_data md
        LEFT JOIN pdm_info c ON md.pdmInfoId = c.id
        LEFT JOIN oth_info oth ON oth.id = md.oth_info_id
        LEFT JOIN product_group d ON md.groupId = d.id
        LEFT JOIN product_category e ON d.product_category_id = e.id
        ORDER BY md.name
    </select>
    <select id="queryMaterialByCondition"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo">
        WITH material_data AS (
        SELECT
        COALESCE(b.material_id, a.id) AS id,
        COALESCE(b.name, a.name) AS name,
        COALESCE(b.approval_id, a.approval_id) AS approvalId,
        COALESCE(b.purchase_mode, a.purchase_mode) AS purchaseMode,
        COALESCE(b.expiration_date, a.expiration_date) AS expirationDate,
        COALESCE(b.group_id, a.group_id) AS groupId,
        COALESCE(b.brand, a.brand) AS brand,
        COALESCE(b.specification_model, a.specification_model) AS specification_model,
        COALESCE(b.description, a.description) AS description,
        COALESCE(b.recommended_level, a.recommended_level) AS recommended_level,
        COALESCE(b.supplier, a.supplier) AS supplier,
        a.material_status AS materialStatus,
        COALESCE(b.pdm_info_id, a.pdm_info_id) AS pdmInfoId,
        COALESCE(b.update_time, a.update_time) AS updateTime,
        a.oth_info_id AS othInfoId
        FROM material a
        LEFT JOIN material_temporary b ON a.id = b.material_id
        WHERE COALESCE(b.group_id, a.group_id) in
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        )
        SELECT
        md.*,
        COALESCE(c.sales_code, f.sales_code) AS salesCode,
        COALESCE(c.production_code, f.production_code) AS productionCode,
        COALESCE(c.sales_status, f.sales_status) AS salesStatus,
        d.path_name AS groupPathName,
        pc.path_name AS productPathName
        FROM material_data md
        LEFT JOIN pdm_info c ON md.pdmInfoId = c.id
        LEFT JOIN oth_info f ON md.othInfoId = f.id 
        LEFT JOIN product_group d ON md.groupId = d.id
        LEFT JOIN product_category pc ON d.product_category_id = pc.id
        <where>
            <if test="materialStatus != null and materialStatus.size() > 0">
                AND md.materialStatus IN
                <foreach collection="materialStatus" item="material" open="(" separator="," close=")">
                    #{material}
                </foreach>
            </if>
            <if test="purchaseModes != null and purchaseModes.size() > 0">
                AND md.purchaseMode IN
                <foreach collection="purchaseModes" item="purchase" open="(" separator="," close=")">
                    #{purchase}
                </foreach>
            </if>
            <if test="salesStatus != null and salesStatus.size() > 0">
                AND (
                c.sales_status IN
                <foreach collection="salesStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
                OR f.sales_status IN
                <foreach collection="salesStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
                )
            </if>
            <if test="dto.materialName != null and dto.materialName != ''">
                AND md.name ILIKE CONCAT('%', #{dto.materialName}, '%')
            </if>
            <if test="dto.salesCode != null and dto.salesCode != ''">
                AND (c.sales_code ILIKE CONCAT('%', #{dto.salesCode}, '%') or f.sales_code ILIKE CONCAT('%', #{dto.salesCode}, '%'))
            </if>
            <if test="dto.productionCode != null and dto.productionCode != ''">
                AND (c.production_code ILIKE CONCAT('%', #{dto.productionCode}, '%') or f.production_code ILIKE CONCAT('%', #{dto.productionCode}, '%'))
            </if>
            <if test="dto.brand != null and dto.brand != ''">
                AND md.brand ILIKE CONCAT('%', #{dto.brand}, '%')
            </if>
            <if test="dto.specificationModel != null and dto.specificationModel != ''">
                AND md.specification_model ILIKE CONCAT('%', #{dto.specificationModel}, '%')
            </if>
            <if test="dto.description != null and dto.description != ''">
                AND md.description ILIKE CONCAT('%', #{dto.description}, '%')
            </if>
            <if test="dto.startDate != null">
                AND md.expirationDate >= #{dto.startDate}
            </if>
            <if test="dto.endDate != null">
                AND md.expirationDate &lt;= #{dto.endDate}
            </if>
        </where>
        ORDER BY md.updateTime DESC
    </select>

    <select id="selectByIds" resultType="com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo">
        select id,name from material where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- Started by AICoder, pid:ia73d14584l28d7148e10b421172d21e7681032b -->
    <select id="selectFuzzyMaterial" resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialFuzzyVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyDto">
        SELECT
        COALESCE(mt.material_id, m.id) AS id,
        COALESCE(mt.name, m.name) AS "name",
        m.material_status,
        COALESCE(mt.expiration_date, m.expiration_date) AS expiration_date,
        COALESCE(mt.purchase_mode, m.purchase_mode) AS purchase_mode,
        COALESCE(mt.brand, m.brand) AS brand,
        COALESCE(mt.supplier, m.supplier) AS supplier,
        COALESCE(mt.create_time, m.create_time) AS create_time,
        COALESCE(p.sales_code, oth.sales_code) AS sales_code,
        COALESCE(p.production_code, oth.production_code) AS production_code,
        COALESCE(p.sales_status, oth.sales_status) AS sales_status,
        pg.path_name AS group_path_name,
        pc.path_name AS category_path_name,
        pc.description
        FROM material m
        LEFT JOIN material_temporary mt ON mt.material_id = m.id
        LEFT JOIN pdm_info p ON p.id = m.pdm_info_id
        LEFT JOIN oth_info oth ON oth.id = m.oth_info_id
        LEFT JOIN product_group pg ON pg.id = m.group_id
        LEFT JOIN product_category pc ON pg.product_category_id = pc.id
        <where>
            1=1
            <if test="fuzzyName != null and fuzzyName != ''">
                AND (
                mt.name ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR m.name ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR p.sales_code ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR p.production_code ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR oth.sales_code ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR oth.production_code ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR pc.description ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR mt.brand ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR m.brand ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR mt.supplier ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                OR m.supplier ILIKE concat('%', #{fuzzyName}, '%') ESCAPE '/'
                )
            </if>
        </where>
        ORDER BY COALESCE(mt.name, m.name) ASC, COALESCE(mt.create_time, m.create_time) DESC
    </select>

    <select id="selectAccurateMaterial" resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialFuzzyVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialAccurateDto">
        SELECT
        COALESCE(mt.material_id, m.id) AS id,
        COALESCE(mt.name, m.name) AS "name",
        m.material_status,
        COALESCE(mt.expiration_date, m.expiration_date) AS expiration_date,
        COALESCE(mt.purchase_mode, m.purchase_mode) AS purchase_mode,
        COALESCE(mt.brand, m.brand) AS brand,
        COALESCE(mt.supplier, m.supplier) AS supplier,
        COALESCE(mt.create_time, m.create_time) AS create_time,
        COALESCE(p.sales_code, oth.sales_code) AS sales_code,
        COALESCE(p.production_code, oth.production_code) AS production_code,
        COALESCE(p.sales_status, oth.sales_status) AS sales_status,
        pg.path_name AS group_path_name,
        pc.path_name AS category_path_name,
        pc.description
        FROM material m
        LEFT JOIN material_temporary mt ON mt.material_id = m.id
        LEFT JOIN pdm_info p ON p.id = m.pdm_info_id
        LEFT JOIN oth_info oth ON oth.id = m.oth_info_id
        LEFT JOIN product_group pg ON pg.id = m.group_id
        LEFT JOIN product_category pc ON pg.product_category_id = pc.id
        <where>
            1=1
            <if test="categoryId != null and categoryId != ''">
                AND pc.id = #{categoryId}
            </if>
            <if test="groupId != null and groupId != '' and groupId.size() > 0">
                AND m.group_id IN
                <foreach item="item" index="index" collection="groupId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialName != null and materialName != ''">
                AND(
                mt.name LIKE CONCAT('%', #{materialName}, '%')
                OR m.name LIKE CONCAT('%', #{materialName}, '%')
                )
            </if>
            <if test="materialStatus != null and materialStatus != ''">
                AND m.material_status = #{materialStatus}
            </if>
            <if test="salesCode != null and salesCode != ''">
                AND (p.sales_code = #{salesCode} OR oth.sales_code = #{salesCode})
            </if>
            <if test="productionCode != null and productionCode != ''">
                AND (p.production_code LIKE CONCAT('%', #{productionCode}, '%') OR oth.production_code LIKE CONCAT('%', #{productionCode}, '%'))
            </if>
            <if test="salesStatus != null and salesStatus != ''">
                AND (p.sales_status = #{salesStatus} OR oth.sales_status = #{salesStatus})
            </if>
            <if test="purchaseMode != null and purchaseMode != ''">
                AND( mt.purchase_mode = #{purchaseMode}
                OR m.purchase_mode = #{purchaseMode}
                )
            </if>
            <if test="brand != null and brand != ''">
                AND (mt.brand LIKE CONCAT('%', #{brand}, '%')
                OR m.brand LIKE CONCAT('%', #{brand}, '%')
                )
            </if>
            <if test="supplier != null and supplier != ''">
                AND (mt.supplier LIKE CONCAT('%', #{supplier}, '%')
                OR m.supplier LIKE CONCAT('%', #{supplier}, '%')
                )
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND COALESCE(mt.expiration_date, m.expiration_date) BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="startDate != null and startDate != '' and (endDate == null or endDate == '')">
                AND COALESCE(mt.expiration_date, m.expiration_date) &gt;= #{startDate}
            </if>
            <if test="(startDate == null or startDate == '') and endDate != null and endDate != ''">
                AND COALESCE(mt.expiration_date, m.expiration_date) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY COALESCE(mt.create_time, m.create_time) DESC
    </select>
    <!-- Started by AICoder, pid:o7adfa34d8ycd2f141ee0bfc804c0d1496d56d3b -->
    <select id="selectByIdAndName" resultType="java.lang.Integer">
        select count(*) as count from material where name = #{name}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <resultMap id="ResultMap_MaterialWithExtendInfoEntity" type="com.zte.uedm.dcdigital.domain.model.material.entity.MaterialWithExtendInfoEntity"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="materialName" column="material_name"/>
        <result property="brand" column="brand"/>
        <result property="supplier" column="supplier"/>
        <result property="groupId" column="group_id"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="purchaseMode" column="purchase_mode"/>
        <result property="materialStatus" column="material_status"/>
        <result property="materialNameEn" column="name_en"/>
        <result property="unit" column="munit"/>
        <result property="unitEn" column="unit_en"/>
        <result property="tecParam" column="description"/>
        <result property="cost" column="cost"/>

        <association property="pdmInfoEntity" javaType="com.zte.uedm.dcdigital.domain.model.material.entity.PdmInfoEntity">
            <id property="id" column="p_id"/>
            <result property="name" column="p_name"/>
            <result property="salesCode" column="p_sales_code"/>
            <result property="productionCode" column="p_production_code"/>
            <result property="unit" column="p_unit"/>
        </association>

        <association property="othInfoEntity" javaType="com.zte.uedm.dcdigital.domain.model.material.entity.OthInfoEntity">
            <id property="id" column="o_id"/>
            <result property="name" column="o_name"/>
            <result property="salesCode" column="o_sales_code"/>
            <result property="productionCode" column="o_production_code"/>
            <result property="unit" column="o_unit"/>
        </association>
    </resultMap>

    <select id="queryMaterialWithExtendInfoByIds" resultMap="ResultMap_MaterialWithExtendInfoEntity">
        SELECT
            m.id, m.name AS material_name, m.brand, m.supplier, m.group_id, m.expiration_date, m.purchase_mode, m.material_status,
            m.unit as munit,m.cost,m.unit_en,m.description,m.name_en,
            p.id AS p_id, p.name AS p_name, p.sales_code AS p_sales_code, p.production_code AS p_production_code, p.unit AS p_unit,
            o.id AS o_id, o.name AS o_name, o.sales_code AS o_sales_code, o.production_code AS o_production_code, o.unit AS o_unit
        FROM material m
        LEFT JOIN pdm_info p ON p.id = m.pdm_info_id
        LEFT JOIN oth_info o ON o.id = m.oth_info_id
        <where>
            <if test="ids != null and ids.size() > 0">
                m.id IN
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>

    <!-- Ended by AICoder, pid:o7adfa34d8ycd2f141ee0bfc804c0d1496d56d3b -->
    <!-- Ended by AICoder, pid:ia73d14584l28d7148e10b421172d21e7681032b -->

    <select id="fuzzyQuery" resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo">
        WITH material_data AS ( SELECT
            m.id,
            COALESCE(mt.approval_id, m.approval_id) AS approval_id,
            COALESCE(mt.name, m.name) AS "name",
            m.group_id,
            m.material_status,
            COALESCE(mt.expiration_date, m.expiration_date) AS expiration_date,
            COALESCE(mt.purchase_mode, m.purchase_mode) AS purchase_mode,
            COALESCE(mt.brand, m.brand) AS brand,
            COALESCE(mt.supplier, m.supplier) AS supplier,
            COALESCE(mt.recommended_level, m.recommended_level) AS recommended_level,
            COALESCE(mt.specification_model, m.specification_model) AS specification_model,
            COALESCE(mt.description, m.description) AS description,
            COALESCE(mt.create_time, m.create_time) AS create_time,
            COALESCE(p.sales_code, oth.sales_code) AS sales_code,
            COALESCE(p.production_code, oth.production_code) AS production_code,
            COALESCE(p.sales_status, oth.sales_status) AS sales_status,
            COALESCE(mt.update_time, m.update_time) AS updateTime,
            pg.path_name AS group_path_name,
            pc.path_name AS productPathName,
            pc.description,
            pc.id as productId

        FROM material m
        LEFT JOIN material_temporary mt ON mt.material_id = m.id
        LEFT JOIN pdm_info p ON p.id = m.pdm_info_id
        LEFT JOIN oth_info oth ON oth.id = m.oth_info_id
        LEFT JOIN product_group pg ON pg.id = m.group_id
        LEFT JOIN product_category pc ON pg.product_category_id = pc.id
        <where>
            <if test="keywords != null and keywords.size() > 0">
                <foreach item="keyword" collection="keywords" separator=" AND ">
                    (
                    mt.name ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR m.name ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR p.sales_code ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR p.production_code ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR oth.sales_code ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR oth.production_code ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR pc.description ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR mt.brand ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR m.brand ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR mt.supplier ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR m.supplier ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR mt.specification_model ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR m.specification_model ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR mt.description ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    OR m.description ILIKE concat('%', #{keyword}, '%') ESCAPE '/'
                    )
                </foreach>
            </if>
            <if test="groupId != null and groupId.size() > 0">
                AND m.group_id IN
                <foreach collection="groupId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="materialIdList != null and !materialIdList.isEmpty()">
                AND m.id IN
                <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        )
        SELECT *
        FROM material_data md
        ORDER BY
        <choose>
            <when test="sortField != null and sortField != ''">
                ${sortField} ${sortOrder}
            </when>
            <otherwise>
                md.updateTime DESC
            </otherwise>
        </choose>
    </select>
    <select id="queryAssociatedMaterial"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo">

        WITH material_data AS (
        SELECT
        m.id,
        m.approval_id,
        m.name AS "name",
        m.group_id,
        m.material_status,
        m.expiration_date,
        m.cost,
        m.purchase_mode,
        m.brand AS brand,
        m.supplier AS supplier,
        m.recommended_level,
        m.specification_model,
        m.description,
        m.create_time,
        COALESCE(p.sales_code, oth.sales_code) AS sales_code,
        COALESCE(p.production_code, oth.production_code) AS production_code,
        COALESCE(p.sales_status, oth.sales_status) AS sales_status
        FROM material m
        LEFT JOIN pdm_info p ON p.id = m.pdm_info_id
        LEFT JOIN oth_info oth ON oth.id = m.oth_info_id
        <where>
            <if test="fuzzyName != null and fuzzyName != ''">
                AND (
                m.name LIKE CONCAT('%', #{fuzzyName}, '%')
                OR m.brand LIKE CONCAT('%', #{fuzzyName}, '%')
                OR m.description LIKE CONCAT('%', #{fuzzyName}, '%')
                OR (m.pdm_info_id IS NULL AND oth.production_code LIKE CONCAT('%', #{fuzzyName}, '%'))
                OR p.production_code LIKE CONCAT('%', #{fuzzyName}, '%')
                )
            </if>
            <if test="materialIdList != null and !materialIdList.isEmpty()">
                AND m.id IN
                <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        )
        SELECT *
        FROM material_data
        ORDER BY
        <choose>
            <when test="sortField != null and sortField != ''">
                ${sortField} ${sortOrder}
            </when>
            <otherwise>
                name ASC
            </otherwise>
        </choose>
    </select>
    <select id="selectionAssociationConditions"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo">
        WITH material_data AS (
        SELECT
        a.id AS id,
        COALESCE(b.name, a.name) AS name,
        COALESCE(b.approval_id, a.approval_id) AS approval_id,
        COALESCE(b.purchase_mode, a.purchase_mode) AS purchase_mode,
        COALESCE(b.expiration_date, a.expiration_date) AS expiration_date,
        COALESCE(b.group_id, a.group_id) AS group_id,
        COALESCE(b.brand, a.brand) AS brand,
        COALESCE(b.specification_model, a.specification_model) AS specification_model,
        COALESCE(b.description, a.description) AS description,
        COALESCE(b.recommended_level, a.recommended_level) AS recommended_level,
        COALESCE(b.supplier, a.supplier) AS supplier,
        a.material_status,
        COALESCE(b.pdm_info_id, a.pdm_info_id) AS pdm_info_id,
        a.oth_info_id
        FROM material a
        LEFT JOIN material_temporary b ON a.id = b.material_id
        WHERE a.id IN
        <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        material_result_data AS (
        SELECT
        md.*,
        COALESCE(c.sales_code, f.sales_code) AS sales_code,
        COALESCE(c.production_code, f.production_code) AS production_code,
        COALESCE(c.sales_status, f.sales_status) AS sales_status
        FROM material_data md
        LEFT JOIN pdm_info c ON md.pdm_info_id = c.id
        LEFT JOIN oth_info f ON md.oth_info_id = f.id AND md.pdm_info_id IS NULL
        WHERE 1=1 /* 添加这一行以避免AND/OR逻辑错误 */
        <if test="materialStatus != null and materialStatus.size() > 0">
            AND md.material_status IN
            <foreach collection="materialStatus" item="material" open="(" separator="," close=")">
                #{material}
            </foreach>
        </if>
        <if test="purchaseModes != null and purchaseModes.size() > 0">
            AND md.purchase_mode IN
            <foreach collection="purchaseModes" item="purchase" open="(" separator="," close=")">
                #{purchase}
            </foreach>
        </if>
        <if test="salesStatus != null and salesStatus.size() > 0">
            AND (
            c.sales_status IN
            <foreach collection="salesStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            OR f.sales_status IN
            <foreach collection="salesStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            )
        </if>
        <if test="dto.materialName != null and dto.materialName != ''">
            AND md.name ILIKE CONCAT('%', #{dto.materialName}, '%')
        </if>
        <if test="dto.salesCode != null and dto.salesCode != ''">
            AND c.sales_code ILIKE CONCAT('%', #{dto.salesCode}, '%')
        </if>
        <if test="dto.productionCode != null and dto.productionCode != ''">
            AND c.production_code ILIKE CONCAT('%', #{dto.productionCode}, '%')
        </if>
        <if test="dto.brand != null and dto.brand != ''">
            AND md.brand ILIKE CONCAT('%', #{dto.brand}, '%')
        </if>
        <if test="dto.specificationModel != null and dto.specificationModel != ''">
            AND md.specification_model ILIKE CONCAT('%', #{dto.specificationModel}, '%')
        </if>
        <if test="dto.description != null and dto.description != ''">
            AND md.description ILIKE CONCAT('%', #{dto.description}, '%')
        </if>
        <if test="dto.startDate != null">
            AND md.expirationDate >= #{dto.startDate}
        </if>
        <if test="dto.endDate != null">
            AND md.expirationDate &lt;= #{dto.endDate}
        </if>
        )
        SELECT mrd.*
        FROM material_result_data mrd
        ORDER BY
        <choose>
            <when test="dto.sortField != null and dto.sortField != ''">
                ${dto.sortField} ${dto.sortOrder}
            </when>
            <otherwise>
                name ASC
            </otherwise>
        </choose>
    </select>
</mapper>