{"module-product-manager": "产品分类管理", "ProductCategoryAddDto-productName": "产品类名称", "ProductCategoryAddDto-nodeType": "产品分类节点类型", "ProductCateGoryAddDescription": "新增产品分类", "ProductCategoryEditDto-productName": "产品分类名称", "ProductCategoryEditDescription": "编辑产品分类", "ProductCategoryPo-productName": "产品分类名称", "ProductCategoryPoDescription": "删除产品分类", "MaterialAddDto-operate": "物料操作类型", "MaterialAddDto-salesCode": "物料销售代码", "MaterialAddDto-name": "物料名称", "MaterialAddDto-groupId": "物料所属分组", "MaterialAddDtoDescription": "新增物料", "MaterialPo-name": "物料名称", "MaterialPo-materialStatus": "物料状态", "MaterialPoDeleteDescription": "删除物料", "MaterialPoUpdateDescription": "更新物料状态", "MaterialPoBatchUpdateDescription": "批量更新物料状态", "MaterialBatchAddDto-salesCode": "物料销售状态", "MaterialBatchAddDto-purchaseMode": "物料采购模式", "MaterialPoUpdateCostDescription": "物料成本变更", "ExportTemplate": "模版下载", "PdmInfoPo-name": "pdm产品名称", "PdmInfoPo-salesCode": "pdm产品销售代码", "PdmInfoPoDescription": "更新pdm信息", "module-product-group-manager": "产品分组管理", "ProductGroupAddDto-name": "产品分组名称", "ProductGroupAddDto-productCategoryId": "产品分类ID", "ProductGroupAddDescription": "新增产品分组", "ProductGroupEditDto-name": "产品分组名称", "ProductGroupEditDto-pdmModelSpec": "规格型号", "ProductGroupEditDescription": "编辑产品分组", "ProductGroupPo-name": "产品分组名称", "ProductGroupPo-productCategoryId": "产品分类ID", "ProductGroupDeleteDescription": "删除产品分组", "ProductBrandAdd": "新增品牌", "ProductBrandDelete": "删除品牌", "ProductBrandEditDescription": "编辑品牌", "ProductBrandAddDto-productCategoryId": "产品小类主键", "ProductBrandAddDto-brandName": "品牌名称", "ProductBrandAddDto-tagName": "标签名称", "ProductBrandAddDto-selectionAttribute": "选型属性", "ProductBrandAddDto-selectionScore": "选型评分", "ProductCategoryExtraInfoEditDescription": "产品小类拓展属性编辑", "BatchConfigPersonInfoDescription": "批量配置产品分类人员信息", "UpdateUserDto-id": "产品小类拓展表id", "UpdateUserDto-extendedWarrantyFactor": "延保系数", "module-procurement-cost-manager": "招采成本管理", "ProcurementCostEditDescription": "编辑招采成本", "ProcurementCostEditDto-lectotypeId": "选型单id", "ProcurementCostEditDto-lectotypeType": "选型单类型", "module-requirement-dashboard-manager": "产品需求看板", "RequirementDashboardEditDescription": "编辑产品需求", "RequirementDashboardAddDto-productCategoryId": "产品小类id", "RequirementDashboardAddDto-projectId": "项目id"}