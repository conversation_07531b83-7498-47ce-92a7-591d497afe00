package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:6da8arc51e4dcef1450d08dab0524572c9109633 */
/* Started by AICoder, pid:23017i0dca1b8c91420708e0c1440e0c13566c3b */
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialTemporaryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialTemporaryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialTemporaryPo;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MaterialTemporaryRepositoryImplTest {

    @InjectMocks
    private MaterialTemporaryRepositoryImpl materialTemporaryRepository;

    @Mock
    private MaterialTemporaryMapper materialTemporaryMapper;

    private static final String MATERIAL_ID = "material123";

    @Before
    public void setUp() {
        // Setup code if needed
    }


    @Test
    public void testQueryMaterialByIds() {
         List<String> materialIds = new ArrayList<>();
         materialIds.add("ad");
         List<MaterialTemporaryPo> temporaryPos =  new ArrayList<>();
         MaterialTemporaryPo materialTemporaryPo = new MaterialTemporaryPo();
         materialTemporaryPo.setId("12");
         temporaryPos.add(materialTemporaryPo);
         when(materialTemporaryMapper.selectList(Mockito.any())).thenReturn(temporaryPos);

         materialTemporaryRepository.queryMaterialByIds(materialIds);
         verify(materialTemporaryMapper).selectList(Mockito.any());
    }

    @Test
    public void testQueryMaterialByIdsWithEmptyList() {
        List<MaterialTemporaryEntity> result = materialTemporaryRepository.queryMaterialByIds(Collections.emptyList());
        assertEquals(Collections.emptyList(), result);
    }

    @Test(expected = BusinessException.class)
    public void testQueryMaterialByIdsWithException() {
        when(materialTemporaryMapper.selectList(Mockito.any())).thenThrow(new RuntimeException("Database error"));
        List<String> materialIds = new ArrayList<>();
        materialIds.add("ad");
        materialTemporaryRepository.queryMaterialByIds(materialIds);
    }

    @Test
    public void testDeleteByIds() {
        List<String> materialIds = new ArrayList<>();
        materialIds.add("ad");
        doReturn(1).when(materialTemporaryMapper).delete(Mockito.any());

        materialTemporaryRepository.deleteByMaterialIds(materialIds);

        verify(materialTemporaryMapper).delete(Mockito.any());
    }

    @Test
    public void testDeleteByIdsWithEmptyList() {
        materialTemporaryRepository.deleteByMaterialIds(Collections.emptyList());

        verify(materialTemporaryMapper, never()).delete(any());
    }

    @Test(expected = BusinessException.class)
    public void testDeleteByIdsWithException() {
       doThrow(new RuntimeException("Database error")).when(materialTemporaryMapper).delete(Mockito.any());
        List<String> materialIds = new ArrayList<>();
        materialIds.add("ad");
        materialTemporaryRepository.deleteByMaterialIds(materialIds);
    }

    @Test(expected = BusinessException.class)
    public void queryMaterialTemporaryByApprovalIdTest() {
        doReturn(new ArrayList<>()).when(materialTemporaryMapper).unionQueryByApprovalId(Mockito.any());
        List<MaterialQueryBean> materialQueryBeans = materialTemporaryRepository.queryTempMaterialByApprovalId("ad");
        Assert.assertTrue(CollectionUtils.isEmpty(materialQueryBeans));
        doThrow(new RuntimeException("Database error")).when(materialTemporaryMapper).unionQueryByApprovalId(Mockito.any());
        materialTemporaryRepository.queryTempMaterialByApprovalId("ad");
    }

    @Test
    public void queryTempMaterialByApprovalIdTest() {
        List<MaterialTemporaryEntity> temporaryEntities = materialTemporaryRepository.queryMaterialTemporaryByApprovalId("ad");
        Assert.assertTrue(CollectionUtils.isEmpty(temporaryEntities));
        List<MaterialTemporaryEntity> temporaryEntities1 = materialTemporaryRepository.queryMaterialTemporaryByApprovalId("ad");
        Assert.assertTrue(CollectionUtils.isEmpty(temporaryEntities1));
    }


    @Test
    public void deleteByIdsTest() {
        materialTemporaryRepository.deleteByIds(Collections.emptyList());
        verify(materialTemporaryMapper, never()).deleteBatchIds(anyList());

        try {
            doThrow(new RuntimeException("Database error")).when(materialTemporaryMapper).deleteBatchIds(anyList());
            materialTemporaryRepository.deleteByIds(Collections.singletonList(MATERIAL_ID));
        } catch (BusinessException e) {
            assertEquals("1005", String.valueOf(e.getCode()));
        }
    }
    /* Started by AICoder, pid:n1dbc2a8c61842114f8709d0a00e2b053d826cf5 */

    /* Started by AICoder, pid:v4f1f909ddr92011445908dea0791f81c3e96069 */
    @Test
    public void testQueryTempMaterialByMaterialId() {
        MaterialTemporaryPo po = new MaterialTemporaryPo();
        po.setId("1");
        po.setMaterialId("material1");
        when(materialTemporaryMapper.selectOne(any())).thenReturn(po);

        MaterialTemporaryEntity result = materialTemporaryRepository.queryTempMaterialByMaterialId("material1");

        assertNotNull(result);
        assertEquals("material1", result.getMaterialId());
        verify(materialTemporaryMapper, times(1)).selectOne(any());
    }

    @Test
    public void testAddTempMaterial() {
        MaterialTemporaryEntity entity = new MaterialTemporaryEntity();
        entity.setId("1");
        entity.setMaterialId("material1");
        materialTemporaryRepository.addTempMaterial(entity);
        verify(materialTemporaryMapper, times(1)).insert(Mockito.any());
    }

    @Test
    public void testUpdateTempMaterial() {
        MaterialTemporaryEntity entity = new MaterialTemporaryEntity();
        entity.setId("1");
        entity.setMaterialId("material1");

        materialTemporaryRepository.updateTempMaterial(entity);
        verify(materialTemporaryMapper, times(1)).updateById(Mockito.any());
    }

    @Test
    public void testBatchUpdateApprovalIdById() {
        List<String> ids = Arrays.asList("1", "2", "3");
        String approvalId = "newApprovalId";

        materialTemporaryRepository.batchUpdateApprovalIdById(ids, approvalId);

        verify(materialTemporaryMapper, times(1)).batchUpdateApprovalIdById(ids, approvalId);
    }

    @Test(expected = MybatisPlusException.class)
    public void testBatchAddMaterialTemporary() {
        MaterialTemporaryEntity entity = new MaterialTemporaryEntity();
        entity.setId("1");
        entity.setMaterialId("material1");
        List<MaterialTemporaryEntity> entities = Collections.singletonList(entity);
        materialTemporaryRepository.batchAddMaterialTemporary(entities);
    }
    /* Ended by AICoder, pid:v4f1f909ddr92011445908dea0791f81c3e96069 */
    /* Ended by AICoder, pid:n1dbc2a8c61842114f8709d0a00e2b053d826cf5 */
    /* Ended by AICoder, pid:23017i0dca1b8c91420708e0c1440e0c13566c3b */

    @Test
    public void addTempMaterialsTest()
    {
        materialTemporaryRepository.addTempMaterials(new MaterialTemporaryEntity());
        verify(materialTemporaryMapper).insert(any());
    }

    @Test
    public void editTempMaterialsTest()
    {
        materialTemporaryRepository.editTempMaterials(new MaterialTemporaryEntity());
        verify(materialTemporaryMapper).updateById(any());
    }

    @Test
    public void selectByMaterialIdTest()
    {
        MaterialTemporaryEntity entity = materialTemporaryRepository.selectByMaterialId("id");
        verify(materialTemporaryMapper).selectByMaterialId(any());
        assertEquals(entity,null);
    }
    /* Ended by AICoder, pid:4cf6134181w6bf314fbd0affa02218018216dbee */
    @Test
    public void queryByPdmInfoIdTest() {
        materialTemporaryRepository.queryByPdmInfoId("materialIds");
        verify(materialTemporaryMapper).selectOne(Mockito.any());
    }
    @Test
    public void queryByGroupIdTest()
    {
        materialTemporaryRepository.queryByGroupId("id");
        verify(materialTemporaryMapper).selectList(any());
    }
    @Test
    public void updateBatchMaterialsTest()
    {
        materialTemporaryRepository.updateBatchMaterials(new ArrayList<>());
        verify(materialTemporaryMapper).updateBatchBy(any());
    }

    /* Started by AICoder, pid:f5aa99892dl1b34147280baa30972705b9c80ad9 */
    @Test
    public void batchQueryByPdmInfoIdsTest() {
        // 使用空列表调用批量查询方法
        materialTemporaryRepository.batchQueryByPdmInfoIds(new ArrayList<>());

        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(materialTemporaryMapper).selectList(any());
    }
    /* Ended by AICoder, pid:f5aa99892dl1b34147280baa30972705b9c80ad9 */
    /* Ended by AICoder, pid:qcf619418196bf314fbd0affa02218018216dbee */
    /* Started by AICoder, pid:d7c0bnb73e715ba141430ba3f042d22e9da58cb6 */
    @Test
    public void testQueryByPdmInfoIdAndBrand_AllFields() {
        String id = "1";
        String brand = "BrandA";
        String materialId = "Material1";

        MaterialTemporaryPo mockPo = mock(MaterialTemporaryPo.class);
        when(mockPo.getPdmInfoId()).thenReturn(id);
        when(mockPo.getBrand()).thenReturn(brand);
        when(mockPo.getMaterialId()).thenReturn(materialId);

        when(materialTemporaryMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.singletonList(mockPo));

        List<MaterialTemporaryEntity> result = materialTemporaryRepository.queryByPdmInfoIdAndBrand(id, brand, materialId);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(materialTemporaryMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }
    /* Ended by AICoder, pid:d7c0bnb73e715ba141430ba3f042d22e9da58cb6 */
}
/* Ended by AICoder, pid:6da8arc51e4dcef1450d08dab0524572c9109633 */
