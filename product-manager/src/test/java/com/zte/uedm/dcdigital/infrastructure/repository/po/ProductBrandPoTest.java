package com.zte.uedm.dcdigital.infrastructure.repository.po;/* Started by AICoder, pid:te5a7u198f0896914e4e08fe9091de647dd84d68 */
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandPo;
import static org.junit.Assert.*;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.junit.Before;
import org.junit.Test;

public class ProductBrandPoTest {

    private ProductBrandPo productBrandPo;

    @Before
    public void setUp() {
        productBrandPo = new ProductBrandPo();
    }

    @Test
    public void testProductBrandPoFields() {
        // 测试所有字段的设置和获取
        productBrandPo.setId("1");
        assertEquals("1", productBrandPo.getId());

        productBrandPo.setProductCategoryId("PC123");
        assertEquals("PC123", productBrandPo.getProductCategoryId());

        productBrandPo.setBrandName("Brand A");
        assertEquals("Brand A", productBrandPo.getBrandName());

        productBrandPo.setSelectionAttribute(0);
        assertEquals(Integer.valueOf(0), productBrandPo.getSelectionAttribute());

        productBrandPo.setSelectionScore(new BigDecimal("8.5"));
        assertEquals(new BigDecimal("8.5"), productBrandPo.getSelectionScore());

        productBrandPo.setProcurementMode("1");
        assertEquals("1", productBrandPo.getProcurementMode());

        productBrandPo.setExpiryDate("2025-12-31");
        assertEquals("2025-12-31", productBrandPo.getExpiryDate());

        productBrandPo.setQuoteId("Q123");
        assertEquals("Q123", productBrandPo.getQuoteId());

        productBrandPo.setCreateTime("2023-01-01 12:00:00");
        assertEquals("2023-01-01 12:00:00", productBrandPo.getCreateTime());

        productBrandPo.setUpdateTime("2023-01-02 12:00:00");
        assertEquals("2023-01-02 12:00:00", productBrandPo.getUpdateTime());

        productBrandPo.setCreateBy("user1");
        assertEquals("user1", productBrandPo.getCreateBy());

        productBrandPo.setUpdateBy("user2");
        assertEquals("user2", productBrandPo.getUpdateBy());
    }

    @Test
    public void testToStringMethod() {
        // 设置一些字段以测试toString方法
        productBrandPo.setId("1");
        productBrandPo.setBrandName("Brand A");

        String expectedString = "ProductBrandPo(id=1, productCategoryId=null, brandName=Brand A, selectionAttribute=null, selectionScore=null, procurementMode=null, expiryDate=null, quoteId=null, createTime=null, updateTime=null, createBy=null, updateBy=null)";
        assertEquals(expectedString, productBrandPo.toString());
    }
}

/* Ended by AICoder, pid:te5a7u198f0896914e4e08fe9091de647dd84d68 */