/* Started by AICoder, pid:b329fe0f29oeea5149b609bb30a09a7e689706c7 */
package com.zte.uedm.dcdigital.interfaces.web.pdm.controller;

import com.zte.uedm.dcdigital.application.pdm.PdmApiQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.CategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.MaterialDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.SpecModelDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.CategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.SpecModelVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PdmUportalControllerTest {

    @InjectMocks
    private PdmUportalController pdmUportalController;

    @Mock
    private PdmApiQueryService pdmApiQueryService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSelectProductLargeCategory(){
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setProductLineNo("1");
        CategoryVo categoryVo = new CategoryVo();
        categoryVo.setCategoryNo("1");
        categoryVo.setCategoryCnName("test");
        List<CategoryVo> list = new ArrayList<>();
        list.add(categoryVo);
        Mockito.when(pdmApiQueryService.getLargeCategoryList(categoryDto)).thenReturn(list);
        BaseResult<List<CategoryVo>> listBaseResult = pdmUportalController.selectProductLargeCategory(categoryDto);
        Assert.assertEquals(0, listBaseResult.getCode().intValue());
    }

    @Test
    public void testSelectSpecificationModel(){
        SpecModelDto modelDto = new SpecModelDto();
        modelDto.setProductLineNo("1");
        PageVO<SpecModelVo> pageVO = new PageVO<>();
        SpecModelVo specModelVo = new SpecModelVo();
        specModelVo.setLargeCategoryNo("123");
        specModelVo.setProductModel("test");
        pageVO.setList(Arrays.asList(specModelVo));
        Mockito.when(pdmApiQueryService.getSpecificationModel(modelDto)).thenReturn(pageVO);
        BaseResult<PageVO<SpecModelVo>> listBaseResult = pdmUportalController.selectSpecificationModel(modelDto);
        Assert.assertEquals(0, listBaseResult.getCode().intValue());
    }

    @Test
    public void testSelectMaterialList(){
        MaterialDto materialDto = new MaterialDto();
        materialDto.setQueryType("1");
        PageVO<MaterialVo> pageVO = new PageVO<>();
        MaterialVo materialVo = new MaterialVo();
        materialVo.setPartNo("123");
        pageVO.setList(Arrays.asList(materialVo));
        Mockito.when(pdmApiQueryService.getMaterialList(materialDto)).thenReturn(pageVO);
        BaseResult<PageVO<MaterialVo>> listBaseResult = pdmUportalController.selectMaterialList(materialDto);
        Assert.assertEquals(0, listBaseResult.getCode().intValue());
    }
}
/* Ended by AICoder, pid:b329fe0f29oeea5149b609bb30a09a7e689706c7 */
