package com.zte.uedm.dcdigital.interfaces.web.product.vo;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class ProductBrandTagVoTest {

    private ProductBrandTagVo productBrandTagVo;

    @Before
    public void setUp() {
        productBrandTagVo = new ProductBrandTagVo();
    }

    @Test
    public void testGettersAndSetters() {
        // Arrange
        String id = "123";
        String tagName = "Electronics";
        String brandId = "456";

        // Act
        productBrandTagVo.setId(id);
        productBrandTagVo.setTagName(tagName);
        productBrandTagVo.setBrandId(brandId);

        // Assert
        assertEquals("The ID should match", id, productBrandTagVo.getId());
        assertEquals("The Tag Name should match", tagName, productBrandTagVo.getTagName());
        assertEquals("The Brand ID should match", brandId, productBrandTagVo.getBrandId());
    }

    @Test
    public void testToString() {
        // Arrange
        productBrandTagVo.setId("123");
        productBrandTagVo.setTagName("Electronics");
        productBrandTagVo.setBrandId("456");

        // Act
        String result = productBrandTagVo.toString();

        // Assert
        assertTrue("toString should contain the id", result.contains("id=123"));
        assertTrue("toString should contain the Tag Name", result.contains("tagName=Electronics"));
        assertTrue("toString should contain the Brand ID", result.contains("brandId=456"));
    }
}