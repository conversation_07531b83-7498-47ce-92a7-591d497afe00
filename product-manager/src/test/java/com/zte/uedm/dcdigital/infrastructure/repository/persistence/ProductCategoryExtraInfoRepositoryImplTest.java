/* Started by AICoder, pid:305f4oee0159ba0145a10839319d7d0cfdc1029e */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryExtraInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryExtraInfoPo;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryExtraInfoRepositoryImplTest {

    @InjectMocks
    private ProductCategoryExtraInfoRepositoryImpl extraInfoRepository;

    @Mock
    private ProductCategoryExtraInfoMapper categoryExtraInfoMapper;

    private ProductCategoryExtraInfoEntity extraInfoEntity;
    private ProductCategoryExtraInfoPo extraInfoPo;

    @Before
    public void setUp() {
        extraInfoEntity = new ProductCategoryExtraInfoEntity();
        extraInfoEntity.setProductCategoryId("1");
        extraInfoEntity.setProductLevel("Level 1");
        extraInfoEntity.setProductComponent("Component A");

        extraInfoPo = new ProductCategoryExtraInfoPo();
        extraInfoPo.setProductCategoryId("1");
        extraInfoPo.setProductLevel("Level 1");
        extraInfoPo.setProductComponent("Component A");
    }
    @Test
    public void testQueryExtraInfoByProductCategoryId() throws BusinessException {
        when(categoryExtraInfoMapper.selectOne(any())).thenReturn(extraInfoPo);


        ProductCategoryExtraInfoEntity result = extraInfoRepository.queryExtraInfoByProductCategoryId("1");

        assertNotNull(result);
        assertEquals("1", result.getProductCategoryId());
        assertEquals("Level 1", result.getProductLevel());
        assertEquals("Component A", result.getProductComponent());
    }


    @Test
    public void testDeleteExtraInfoByProductCategoryId() throws BusinessException {
        doNothing().when(categoryExtraInfoMapper).deleteByProductCategoryId(anyString());

        extraInfoRepository.deleteExtraInfoByProductCategoryId("1");

        verify(categoryExtraInfoMapper, times(1)).deleteByProductCategoryId("1");
    }


    @Test
    public void testAddProductCategoryExtraInfo() throws BusinessException {

        doReturn(1).when(categoryExtraInfoMapper).insert(Mockito.any());
        extraInfoRepository.addProductCategoryExtraInfo(extraInfoEntity);

    }



    @Test
    public void testUpdateProductCategoryExtraInfo() throws BusinessException {
        extraInfoEntity.setProductCategoryId("123");
        extraInfoRepository.updateProductCategoryExtraInfo(extraInfoEntity);
    }


    @Test
    public void testQueryExtraInfoByProductCategoryIds() throws BusinessException {
        List<ProductCategoryExtraInfoPo> pos = Collections.singletonList(extraInfoPo);
        when(categoryExtraInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pos);


        List<ProductCategoryExtraInfoEntity> result = extraInfoRepository.queryExtraInfoByProductCategoryIds(Collections.singletonList("1"));

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("1", result.get(0).getProductCategoryId());
        assertEquals("Level 1", result.get(0).getProductLevel());
        assertEquals("Component A", result.get(0).getProductComponent());
    }
    /* Started by AICoder, pid:5130aw07f3d99ff14e5909a8a047a40669787c14 */
    @Test
    public void testUpdateExtendedWarrantyFactorAndNonStandardItems() {

        ProductCategoryExtraInfoEntity entity = new ProductCategoryExtraInfoEntity();
        entity.setId("555");
        // 调用方法
        extraInfoRepository.updateExtendedWarrantyFactorAndNonStandardItems(entity);

        // 验证mapper是否被调用
        verify(categoryExtraInfoMapper, times(1)).updateExtendedWarrantyFactorAndNonStandardItems(Mockito.any());
    }
    /* Ended by AICoder, pid:5130aw07f3d99ff14e5909a8a047a40669787c14 */
}
/* Ended by AICoder, pid:305f4oee0159ba0145a10839319d7d0cfdc1029e */