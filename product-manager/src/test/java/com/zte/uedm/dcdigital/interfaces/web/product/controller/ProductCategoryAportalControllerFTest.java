package com.zte.uedm.dcdigital.interfaces.web.product.controller;

import com.zte.udem.ft.FtMockitoAnnotations;
import com.zte.udem.ft.util.FakeBranchFlag;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;
import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryExtraInfoRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryExtraInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCategoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.persistence.ProductCategoryExtraInfoRepositoryImpl;
import com.zte.uedm.dcdigital.infrastructure.repository.persistence.ProductCategoryRepositoryImpl;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ManagerDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryQueryDto;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import com.zte.uedm.dcdigital.uft.db.ProductCategoryExtraInfoMapperFake;
import com.zte.uedm.dcdigital.uft.db.ProductCategoryMapperFake;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ProductCategoryAportalControllerFTest {

    @InjectMocks
    private ProductCategoryAportalController productCategoryAportalController;

    @Resource
    private ProductCategoryRepository productCategoryRepository = new ProductCategoryRepositoryImpl();
    @Resource
    private ProductCategoryExtraInfoRepository extraInfoRepository = new ProductCategoryExtraInfoRepositoryImpl();
    @Mock
    private ProductCategoryMapper productCategoryMapper = new ProductCategoryMapperFake();

    @Mock
    private ProductCategoryExtraInfoMapper productCategoryExtraInfoMapper = new ProductCategoryExtraInfoMapperFake();

    @Mock
    private ProductGroupRepository productGroupRepository;

    @Mock
    private SystemService systemService;
    @Mock
    private DocumentService documentService;

    @Mock
    private AuthService authService;

    @Before
    public void setUp() throws Exception{
        FtMockitoAnnotations.initMocks(this);
        Mockito.when(authService.getUserId()).thenReturn("admin");
    }
    @Test
    public void EPM_37891_given_产品线为空_when_创建产品线_then_产品线创建失败(){
        //
        //given
        //产品线名称为空
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(1);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1006, result.getCode().intValue());

    }

    @Test
    public void EPM_37903_given_产品大类为空_when_创建产品大类_then_产品大类创建失败(){
        //
        //given
        //产品大类名称为空
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(2);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1006, result.getCode().intValue());
    }

    @Test
    public void EPM_37895_given_产品小类为空_when_创建产品小类_then_产品小类创建失败(){
        //
        //given
        //产品小类名称为空
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(3);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1006, result.getCode().intValue());
        //then

    }

    @Test
    public void EPM_37909_given_产品线已存在_when_创建产品线_then_产品线创建失败(){
        //
        //given
        //产品线名称已存在
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(1);
        addDto.setProductName("productLine");
        FakeBranchFlag.setFLAG("nameIsExists");
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1201, result.getCode().intValue());
    }

    @Test
    public void EPM_37910_given_产品大类已存在_when_创建产品大类_then_产品大类创建失败(){
        //
        //given
        //产品大类名称已存在
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(2);
        addDto.setParentId("line");
        addDto.setProductName("productCategory");
        FakeBranchFlag.setFLAG("nameIsExists");
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1201, result.getCode().intValue());
    }

    @Test
    public void EPM_37905_given_产品小类已存在_when_创建产品小类_then_产品小类创建失败(){
        //
        //given
        //产品小类名称已存在
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(3);
        addDto.setParentId("product");
        addDto.setProductName("productCategory");
        addDto.setProductLevel("productCategory");
        addDto.setProductComponent("productCategory");
        addDto.setProductManager(new ManagerDto());
        FakeBranchFlag.setFLAG("nameIsExists");
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1201, result.getCode().intValue());
    }

    @Test
    public void EPM_37900_given_产品线不存在_when_创建产品大类_then_产品大类创建失败(){
        //
        //given
        //产品大类所属产品线不存在
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(2);
        addDto.setParentId("line");
        addDto.setProductName("productCategory");
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1202, result.getCode().intValue());

    }

    @Test
    public void EPM_37893_given_产品大类不存在_when_创建产品小类_then_产品小类创建失败(){
        //
        //given
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(3);
        addDto.setParentId("product");
        addDto.setProductName("productCategory");
        addDto.setProductLevel("productCategory");
        addDto.setProductComponent("productCategory");
        addDto.setProductManager(new ManagerDto());
        //产品小类所属产品大类不存在
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(1202, result.getCode().intValue());
    }

    @Test
    public void EPM_37899_given_产品线名称合法_when_创建产品线_then_产品线创建成功(){
        //
        //given
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(1);
        addDto.setProductName("line");
        //产品线名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(0, result.getCode().intValue());

    }

    @Test
    public void EPM_37897_given_产品大类名称合法_when_创建产品大类_then_产品大类创建成功(){
        //
        //given
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(2);
        addDto.setParentId("line01");
        addDto.setProductName("productCategory");
        //产品大类名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(0, result.getCode().intValue());

    }

    @Test
    public void EPM_37907_given_产品小类名称合法_when_创建产品小类_then_产品小类创建成功() throws IOException {
        //
        //given
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(3);
        addDto.setParentId("productCategory01");
        addDto.setProductName("subProductCategory");
        addDto.setProductLevel("productLevel");
        addDto.setProductComponent("productComponent");
        addDto.setProductManager(new ManagerDto());
        //产品小类名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.addProductCategory(addDto);
        //then
        assertEquals(0, result.getCode().intValue());

    }
    @Test
    public void EPM_given_小类分页查询成功() throws IOException {
        //
        //given
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        queryDto.setNodeType(3);
        queryDto.setCategoryName("sub");
        queryDto.setParentId("productCategory01");
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        //产品小类名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        List<ResourceVo> list = new ArrayList<>();
        ResourceVo resourceVo = new ResourceVo();
        resourceVo.setEntityId("12");
        UserVo userVo = new UserVo();
        userVo.setId("123");
        List<UserVo> userVos = new ArrayList<>();
        userVos.add(userVo);
        Map<String, List<UserVo>> userRoleMap = new HashMap<>();
        userRoleMap.put("10001", userVos);
        resourceVo.setUserRoleMap(userRoleMap);
        list.add(resourceVo);



        //when
        BaseResult result = productCategoryAportalController.queryProductCategory(queryDto);
        //then
        assertEquals(0, result.getCode().intValue());

    }
    @Test
    public void EPM_given_小类修改成功() throws IOException {
        //
        //given
        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("subProductCategory01");
        editDto.setProductName("subProductCategory");
        editDto.setProductLevel("productLevel");
        editDto.setProductComponent("productComponent");
        editDto.setProductManager(new ManagerDto());
        //产品小类名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        Call<ResponseBean> value = mock(Call.class);
        ResponseBean resResponseBean = new ResponseBean();
        resResponseBean.setData(new HashMap<>());
        resResponseBean.setCode(0);
        Response<ResponseBean> response = Response.success(resResponseBean);
        when(value.execute()).thenReturn(response);

        //when
        BaseResult result = productCategoryAportalController.editProductCategory(editDto);
        //then
        assertEquals(0, result.getCode().intValue());

    }
    @Test
    public void EPM_given_小类删除成功() throws IOException {
        //
        //given
        //产品小类删除
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        Call<ResponseBean> value = mock(Call.class);
        ResponseBean resResponseBean = new ResponseBean();
        resResponseBean.setData(new HashMap<>());
        resResponseBean.setCode(0);
        Response<ResponseBean> response = Response.success(resResponseBean);
        when(value.execute()).thenReturn(response);

        //when
        BaseResult result = productCategoryAportalController.deleteProductCategoryById("subProductCategory01");
        //then
        assertEquals(0, result.getCode().intValue());

    }

    @Test
    public void EPM_given_大类删除成功(){
        //
        //given
        //产品线/大类删除
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        //when
        BaseResult result = productCategoryAportalController.deleteProductCategoryById("productCategory03");
        //then
        assertEquals(0, result.getCode().intValue());

    }
    @Test
    public void EPM_given_根据id查询小类() throws IOException {
        //
        //given
        //产品小类名称合法
        FakeBranchFlag.setFLAG(FakeBranchFlag.DATA_NORMAL);
        List<ResourceVo> list = new ArrayList<>();
        ResourceVo resourceVo = new ResourceVo();
        resourceVo.setEntityId("123");
        list.add(resourceVo);


        //when
        BaseResult result = productCategoryAportalController.queryProductCategoryById("subProductCategory01");
        //then
        assertEquals(0, result.getCode().intValue());

    }
}
