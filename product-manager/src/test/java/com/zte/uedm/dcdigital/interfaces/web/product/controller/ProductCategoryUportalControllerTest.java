package com.zte.uedm.dcdigital.interfaces.web.product.controller;


/* Started by AICoder, pid:76a4b80a2bv06f3140850acb01c16425c254a718 */
import com.zte.uedm.dcdigital.application.category.ProductCategoryCommandService;
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.UpdateUserDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.UserDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryUportalControllerTest {

    @Mock
    private ProductCategoryQueryService productCategoryQueryService;

    @Mock
    private ProductCategoryCommandService productCategoryCommandService;

    @InjectMocks
    private ProductCategoryUportalController controller;

    @Before
    public void setUp() {
        // 初始化任何需要的设置
    }

    @Test
    public void testQueryAllProductSubcategory() {
        controller.queryAllProductSubcategory();
        verify(productCategoryQueryService, times(1)).getAllProductSubcategory();
    }
    @Test
    public void testQueryUserProductSubcategory() {
        List<ProductSubcategoryVo> expectedList = Arrays.asList(new ProductSubcategoryVo(), new ProductSubcategoryVo());
        when(productCategoryQueryService.getUserProductSubcategory()).thenReturn(expectedList);

        BaseResult<List<ProductSubcategoryVo>> result = controller.queryUserProductSubcategory();

        assertEquals(expectedList, result.getData());
        verify(productCategoryQueryService, times(1)).getUserProductSubcategory();
    }

    @Test
    public void testUpdateAssociatedUser_EmptyUsers() {
        UpdateUserDto userDto = new UpdateUserDto();
        userDto.setUsers(Collections.emptyList());

        try {
            controller.updateAssociatedUser(userDto);
        } catch (BusinessException e) {
            assertEquals("1006", String.valueOf(e.getCode()));
            verify(productCategoryCommandService, never()).updateRelatedUser(anyString(), anyList(), anyString(),any(),  anyString());
        }
    }

    @Test
    public void testUpdateAssociatedUser_ValidUsers() {
        UpdateUserDto userDto = new UpdateUserDto();
        userDto.setId("1");
        userDto.setUsers(Collections.singletonList(new UserDto()));
        userDto.setNonStandardItems("some items");
        userDto.setExtendedWarrantyFactor(BigDecimal.TEN);

        doNothing().when(productCategoryCommandService).updateRelatedUser(anyString(), anyList(), anyString(),any(),  anyString());

        BaseResult result = controller.updateAssociatedUser(userDto);

        assertEquals("0", String.valueOf(result.getCode()));
        verify(productCategoryCommandService, times(1)).updateRelatedUser(Mockito.anyString(),Mockito.anyList(),Mockito.anyString(),Mockito.any(),Mockito.anyString());
    }

    @Test
    public void testQueryProductCategoryById_EmptyId() {
        try {
            controller.queryProductCategoryById("");
        } catch (BusinessException e) {
            assertEquals("1006", String.valueOf(e.getCode()));
            verify(productCategoryQueryService, never()).queryProductCategoryById(anyString());
        }
    }

    @Test
    public void testQueryProductCategoryById_ValidId() {
        String id = "1";
        ProductCategoryManagerVo expectedVo = new ProductCategoryManagerVo();
        when(productCategoryQueryService.queryProductCategoryManagerById(id)).thenReturn(expectedVo);

        BaseResult<ProductCategoryManagerVo> result = controller.queryProductCategoryById(id);

        verify(productCategoryQueryService, times(1)).queryProductCategoryManagerById(id);
    }

    @Test
    public void testBatchQueryProductCategory_EmptyIds() {
        try {
            controller.batchQueryProductCategory(Collections.emptyList());
        } catch (BusinessException e) {
            assertEquals("1006", String.valueOf(e.getCode()));
            verify(productCategoryQueryService, never()).batchQueryCategory(anyList());
        }
    }

    @Test
    public void testBatchQueryProductCategory_ValidIds() {
        List<String> ids = Arrays.asList("1", "2");
        List<ProductCategoryVo> expectedList = Arrays.asList(new ProductCategoryVo(), new ProductCategoryVo());
        when(productCategoryQueryService.batchQueryCategory(ids)).thenReturn(expectedList);

        BaseResult<List<ProductCategoryVo>> result = controller.batchQueryProductCategory(ids);

        assertEquals(expectedList, result.getData());
        verify(productCategoryQueryService, times(1)).batchQueryCategory(ids);
    }
}
/* Ended by AICoder, pid:76a4b80a2bv06f3140850acb01c16425c254a718 */