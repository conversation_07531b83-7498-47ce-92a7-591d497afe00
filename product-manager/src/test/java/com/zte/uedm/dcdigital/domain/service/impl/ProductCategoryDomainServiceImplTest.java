package com.zte.uedm.dcdigital.domain.service.impl;

/* Started by AICoder, pid:93ce29801eg36d114cec092093713319d3950261 */
import com.github.pagehelper.PageInfo;

import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.ResourceDto;
import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryExtraInfoRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.common.enums.ProductCategoryEnums;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandMapper;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;


import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryDomainServiceImplTest {

    @Mock
    private ProductCategoryRepository productCategoryRepository;

    @Mock
    private ProductCategoryExtraInfoRepository extraInfoRepository;

    @Mock
    private ProductGroupRepository productGroupRepository;

    @Mock
    private AuthService authService;
    @Mock
    private SystemService systemService;

    @Mock
    private DocumentService documentService;

    @Mock
    private ProductBrandMapper brandMapper;

    @InjectMocks
    private ProductCategoryDomainServiceImpl productCategoryDomainService;

    @Before
    public void setUp() {
        // Setup code if needed
        Mockito.when(authService.getUserId()).thenReturn("admin");
    }

    @Test
    public void testQueryProductCategory() throws BusinessException {
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setNodeType(3);
        ProductCategoryEntity entity1 = new ProductCategoryEntity();
        entity1.setId("1");
        entity1.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());

        ProductCategoryEntity entity2 = new ProductCategoryEntity();
        entity2.setId("2");
        entity2.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());

        List<ProductCategoryEntity> entities = Arrays.asList(entity1, entity2);
        PageInfo<ProductCategoryEntity> pageInfo2 = new PageInfo<>(entities);

        when(productCategoryRepository.queryByCondition(any())).thenReturn(pageInfo2);

        PageInfo<ProductCategoryEntity> pageInfo = productCategoryDomainService.queryProductCategory(queryDto);

        assertNotNull(pageInfo);
        assertEquals(2, pageInfo.getList().size());

        verify(productCategoryRepository, times(1)).queryByCondition(any());
    }

    @Test
    public void testQueryProductCategoryWithExtraInfo() throws BusinessException {
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());

        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());

        List<ProductCategoryEntity> entities = Collections.singletonList(entity);

        ProductCategoryExtraInfoEntity extraInfo = new ProductCategoryExtraInfoEntity();
        extraInfo.setProductCategoryId("1");
        extraInfo.setProductLevel("Level1");
        extraInfo.setProductComponent("Component1");
        List<ResourceVo> list = new ArrayList<>();
        ResourceVo resourceVo = new ResourceVo();
        Map<String, List<UserVo>> userRoleMap = new HashMap<>();
        for (RoleCodeEnum role : RoleCodeEnum.values()) {
            List<UserVo> users = new ArrayList<>();
            UserVo userVo = new UserVo();
            userVo.setId("as");
            users.add(userVo);
            userRoleMap.put(role.getCode(), users);
        }
        resourceVo.setUserRoleMap(userRoleMap);
        resourceVo.setEntityId("1");
        list.add(resourceVo);
        when(systemService.getResourceByIdAndRoleCode(Mockito.anyList(),Mockito.anyString())).thenReturn(list);
        List<ProductCategoryExtraInfoEntity> extraInfos = Collections.singletonList(extraInfo);
        PageInfo<ProductCategoryEntity> pageInfo2 = new PageInfo<>(entities);
        when(productCategoryRepository.queryByCondition(any())).thenReturn(pageInfo2);
        when(extraInfoRepository.queryExtraInfoByProductCategoryIds(anyList())).thenReturn(extraInfos);

        PageInfo<ProductCategoryEntity> pageInfo = productCategoryDomainService.queryProductCategory(queryDto);

        assertNotNull(pageInfo);
        assertEquals(1, pageInfo.getList().size());

        verify(productCategoryRepository, times(1)).queryByCondition(any());
        verify(extraInfoRepository, times(1)).queryExtraInfoByProductCategoryIds(anyList());
    }


    @Test
    public void testDeleteByIdSubcategory_success() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);

        productCategoryDomainService.deleteById("1");

        verify(productCategoryRepository, times(1)).deleteById("1");
        verify(extraInfoRepository, times(1)).deleteExtraInfoByProductCategoryId("1");
    }

    @Test(expected = BusinessException.class)
    public void testDeleteById_subcategory_with_group() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        List<ProductGroupEntity> productGroupEntities = new ArrayList<>();
        ProductGroupEntity entity1 = new ProductGroupEntity();
        entity1.setId("123");
        productGroupEntities.add(entity1);
        when(productGroupRepository.queryPorductGroups(Mockito.any())).thenReturn(productGroupEntities);
        productCategoryDomainService.deleteById("1");
    }

    /* Started by AICoder, pid:n89eaf84c6le43d143b8099910101f6e4c275697 */
    @Test(expected = BusinessException.class)
    public void testDeleteById_subcategory_with_brand() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        List<String> ids = Collections.singletonList("asa");
        when(brandMapper.advancedDetailedSearchBrandIds(Mockito.any())).thenReturn(ids);
        productCategoryDomainService.deleteById("1");

    }
    @Test(expected = BusinessException.class)
    public void testDeleteById_subcategory_with_faq() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        when(systemService.existRelatedFaq(Mockito.anyString())).thenReturn(true);
        productCategoryDomainService.deleteById("1");

    }
    @Test(expected = BusinessException.class)
    public void testDeleteById_subcategory_with_document() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        when(documentService.existRelatedDocument(Mockito.anyString())).thenReturn(true);
        productCategoryDomainService.deleteById("1");
    }
    /* Ended by AICoder, pid:n89eaf84c6le43d143b8099910101f6e4c275697 */
    @Test(expected = BusinessException.class)
    public void testDeleteByIdCategoryWithSubcategories() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        when(productCategoryRepository.checkCorrelationRelationship("1")).thenReturn(1L);

        productCategoryDomainService.deleteById("1");

        verify(productCategoryRepository, never()).deleteById(anyString());
        verify(extraInfoRepository, never()).deleteExtraInfoByProductCategoryId(anyString());
    }

    @Test
    public void testDeleteByIdCategoryWithoutSubcategories() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        when(productCategoryRepository.checkCorrelationRelationship("1")).thenReturn(0L);

        productCategoryDomainService.deleteById("1");
        verify(productCategoryRepository, times(1)).deleteById("1");
    }

    @Test
    public void testEditProductCategoryNonExistent() {
        try {
            ProductCategoryEditDto editDto = new ProductCategoryEditDto();
            editDto.setId("1");
            when(productCategoryRepository.queryById("1")).thenReturn(null);

            productCategoryDomainService.editProductCategory(editDto);
        } catch (Exception e) {
            Assert.assertNull(e);
        }
    }
    @Test
    public void testEditProductCategoryNameNotChange() {
        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductName("1");
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_LINE.getId());
        entity.setId("1");
        entity.setProductName("1");
        when(productCategoryRepository.queryById("1")).thenReturn(entity);

        productCategoryDomainService.editProductCategory(editDto);
        verify(productCategoryRepository, times(1)).updateProductCategory(any());
    }

    @Test(expected = BusinessException.class)
    public void testEditProductCategoryInvalidSubcategory() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity.setId("1");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("");
        editDto.setProductComponent("");
        editDto.setProductManager(null);

        when(productCategoryRepository.queryById("1")).thenReturn(entity);

        productCategoryDomainService.editProductCategory(editDto);

        verify(productCategoryRepository, never()).updateProductCategory(any());
        verify(extraInfoRepository, never()).updateProductCategoryExtraInfo(any());
    }

    @Test
    public void testEditProductCategoryValidSubcategory() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity.setId("1");
        entity.setProductName("old");
        entity.setPathName("line/old");
        entity.setParentId("parent");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("Level1");
        editDto.setProductComponent("Component1");
        editDto.setProductManager(new ManagerDto());
        editDto.setProductName("newName");
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        ProductCategoryExtraInfoEntity extraInfoEntity = new ProductCategoryExtraInfoEntity();
        when(extraInfoRepository.queryExtraInfoByProductCategoryId("1")).thenReturn(extraInfoEntity);
        List<ProductCategoryEntity> allNodes = new ArrayList<>();
        ProductCategoryEntity entity2 = new ProductCategoryEntity();
        entity2.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity2.setId("2");
        entity2.setProductName("two");
        entity2.setParentId("1");
        entity2.setPathName("line/old/two");
        allNodes.add(entity);
        allNodes.add(entity2);
        when(productCategoryRepository.selectCurentNodeAllChildNode(Mockito.any())).thenReturn(allNodes);
        ProductCategoryEntity secondaryCategory = new ProductCategoryEntity();
        secondaryCategory.setId("2");
        secondaryCategory.setNodeType(2);
        secondaryCategory.setPathName("ac/ad");
        secondaryCategory.setParentId("1");
        when(productCategoryRepository.queryById("parent")).thenReturn(secondaryCategory);

        productCategoryDomainService.editProductCategory(editDto);
        verify(productCategoryRepository, times(1)).updateProductCategory(any());
    }
    /* Started by AICoder, pid:gfbf1g39207b3de1453a0945e0104a914377d9c3 */

    @Test
    public void testMaintenancePathNameTopLevel() {

        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_LINE.getId());
        entity.setId("1");
        entity.setProductName("line");
        entity.setPathName("line");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("Level1");
        editDto.setProductComponent("Component1");
        editDto.setProductManager(new ManagerDto());
        editDto.setProductName("newName");
        when(productCategoryRepository.queryById("1")).thenReturn(entity);

        List<ProductCategoryEntity> allNodes = new ArrayList<>();
        ProductCategoryEntity entity2 = new ProductCategoryEntity();
        entity2.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity2.setId("2");
        entity2.setProductName("two");
        entity2.setParentId("33");
        entity2.setPathName("line/old/two");
        ProductCategoryEntity entity1 = new ProductCategoryEntity();
        entity1.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
        entity1.setId("33");
        entity1.setProductName("old");
        entity1.setParentId("1");
        entity1.setPathName("line/old/");
        allNodes.add(entity);
        allNodes.add(entity1);
        allNodes.add(entity2);
        when(productCategoryRepository.selectCurentNodeAllChildNode(Mockito.any())).thenReturn(allNodes);


        productCategoryDomainService.editProductCategory(editDto);

        verify(productCategoryRepository).updateBatch(anyList());
    }

    @Test
    public void testMaintenancePathNameSecondaryLevel() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_CATEGORIES.getId());
        entity.setId("1");
        entity.setProductName("old");
        entity.setPathName("line");
        entity.setParentId("parent2");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("Level1");
        editDto.setProductComponent("Component1");
        editDto.setProductManager(new ManagerDto());
        editDto.setProductName("newName");
        when(productCategoryRepository.queryById("1")).thenReturn(entity);

        List<ProductCategoryEntity> allNodes = new ArrayList<>();
        ProductCategoryEntity entity2 = new ProductCategoryEntity();
        entity2.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity2.setId("2");
        entity2.setProductName("two");
        entity2.setParentId("1");
        entity2.setPathName("line/old/two");
        allNodes.add(entity);
        allNodes.add(entity2);
        when(productCategoryRepository.selectCurentNodeAllChildNode(Mockito.any())).thenReturn(allNodes);
        ProductCategoryEntity topCategory = new ProductCategoryEntity();
        topCategory.setId("2");
        topCategory.setNodeType(2);
        topCategory.setPathName("ac/ad");
        topCategory.setParentId("1");
        when(productCategoryRepository.queryById("parent2")).thenReturn(topCategory);

        productCategoryDomainService.editProductCategory(editDto);


        verify(productCategoryRepository).updateBatch(anyList());
    }


    @Test(expected = BusinessException.class)
    public void testMaintenancePathNameInvalidNodeType() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(7);
        entity.setId("1");
        entity.setProductName("old");
        entity.setPathName("line/old");
        entity.setParentId("parent");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("Level1");
        editDto.setProductComponent("Component1");
        editDto.setProductManager(new ManagerDto());
        editDto.setProductName("newName");
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        List<ProductCategoryEntity> allNodes = new ArrayList<>();
        ProductCategoryEntity entity2 = new ProductCategoryEntity();
        entity2.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity2.setId("2");
        entity2.setProductName("two");
        entity2.setParentId("1");
        entity2.setPathName("line/old/two");
        allNodes.add(entity);
        allNodes.add(entity2);
        when(productCategoryRepository.selectCurentNodeAllChildNode(Mockito.any())).thenReturn(allNodes);
        productCategoryDomainService.editProductCategory(editDto);
        // The method should throw a BusinessException for an invalid node type
    }
    /* Ended by AICoder, pid:gfbf1g39207b3de1453a0945e0104a914377d9c3 */
    @Test(expected = BusinessException.class)
    public void testEditProductCategory_Subcategory_not_exit() {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        entity.setId("1");

        ProductCategoryEditDto editDto = new ProductCategoryEditDto();
        editDto.setId("1");
        editDto.setProductLevel("Level1");
        editDto.setProductComponent("Component1");
        editDto.setProductManager(new ManagerDto());
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        productCategoryDomainService.editProductCategory(editDto);

    }
    @Test
    public void testAddProductCategoryInvalidParent() {
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(ProductCategoryEnums.PRODUCT_LINE.getId());
        doNothing().when(productCategoryRepository).addProductCategory(Mockito.any());
        productCategoryDomainService.addProductCategory(addDto);
        verify(productCategoryRepository,  times(1)).addProductCategory(any());
    }

    @Test
    public void testAddProductCategoryValid() {
        ProductCategoryAddDto addDto = new ProductCategoryAddDto();
        addDto.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());
        addDto.setParentId("3");
        addDto.setProductName("3");
        addDto.setBrandManagers(Collections.emptyList());
        addDto.setProductManager(new ManagerDto());
        ProductCategoryEntity parentEntity = new ProductCategoryEntity();
        parentEntity.setId("1");
        parentEntity.setPathId("1");
        parentEntity.setPathName("Path1");
        when(productCategoryRepository.queryById("3")).thenReturn(null);
        assertThrows(BusinessException.class, () -> {
            productCategoryDomainService.addProductCategory(addDto);
        });


        when(productCategoryRepository.queryById("3")).thenReturn(parentEntity);
        productCategoryDomainService.addProductCategory(addDto);
        verify(productCategoryRepository, times(1)).addProductCategory(any());
        verify(extraInfoRepository, times(1)).addProductCategoryExtraInfo(any());

    }

    @Test
    public void testQueryProductCategoryById() throws BusinessException {
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setNodeType(ProductCategoryEnums.PRODUCT_SUBCATEGORY.getId());

        ProductCategoryExtraInfoEntity extraInfo = new ProductCategoryExtraInfoEntity();
        extraInfo.setProductCategoryId("1");
        extraInfo.setProductLevel("Level1");
        extraInfo.setProductComponent("Component1");
        ResourceVo resourceVo = new ResourceVo();
        Map<String, List<UserVo>> userRoleMap = new HashMap<>();
        for (RoleCodeEnum role : RoleCodeEnum.values()) {
            List<UserVo> users = Arrays.asList(new UserVo(), new UserVo());
            userRoleMap.put(role.getCode(), users);
        }
        resourceVo.setUserRoleMap(userRoleMap);
        when(productCategoryRepository.queryById("1")).thenReturn(entity);
        when(extraInfoRepository.queryExtraInfoByProductCategoryId("1")).thenReturn(extraInfo);
        when(systemService.getResourceById(Mockito.anyString())).thenReturn(resourceVo);

        ProductCategoryEntity vo = productCategoryDomainService.queryProductCategoryById("1");

        assertNotNull(vo);
        assertEquals("Level1", vo.getProductLevel());
        assertEquals("Component1", vo.getProductComponent());
    }

    @Test
    public void testQueryProductCategoryByIdNoExtraInfo() throws BusinessException {

        when(productCategoryRepository.queryById(Mockito.anyString())).thenReturn(null);
        ProductCategoryEntity vo = productCategoryDomainService.queryProductCategoryById("1");
        assertNull(vo.getId());
    }

    @Test
    public void testIsProductCategoryNameUnique() throws BusinessException {
        when(productCategoryRepository.countByNameAndIdNot(Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        productCategoryDomainService.isProductCategoryNameUnique("1", "name",null,"");

        verify(productCategoryRepository, times(1)).countByNameAndIdNot(eq("name"), eq("1"));
    }

    @Test(expected = BusinessException.class)
    public void testIsProductCategoryNameNotUnique() throws BusinessException {
        when(productCategoryRepository.nameValidation("name", 2,"parentId")).thenReturn(true);

        productCategoryDomainService.isProductCategoryNameUnique(null, "name",2,"parentId");

        verify(productCategoryRepository, times(1)).nameValidation(eq("name"), eq(2), eq("parentId"));
    }
    /* Started by AICoder, pid:135cb36b4f3e7a714e650aa711c2033e62886c66 */
    @Test
    public void testGetUserProductSubcategory() {
        List<ProductSubcategoryVo> userProductSubcategory = productCategoryDomainService.getUserProductSubcategory();
        assertNotNull(userProductSubcategory);
        assertTrue(userProductSubcategory.isEmpty());
    }

    @Test
    public void testBatchQueryByIds() {
        List<String> ids = Arrays.asList("1", "2");
        when(productCategoryRepository.batchQueryProductSubclasses(ids)).thenReturn(Collections.emptyList());

        List<ProductCategoryVo> result = productCategoryDomainService.batchQueryByIds(ids);

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(productCategoryRepository).batchQueryProductSubclasses(ids);
    }

    @Test
    public void testUpdateRelatedUser() {
        String id = "1";
        Arrays.asList("user1", "user2");
        List<ManagerDto> userIds = new ArrayList<>();
        ManagerDto managerDto = new ManagerDto();
        managerDto.setId("123");
        managerDto.setRoleCode("manager");
        ManagerDto managerDto2 = new ManagerDto();
        managerDto2.setId("111");
        managerDto2.setRoleCode("manager");
        userIds.add(managerDto);
        userIds.add(managerDto2);
        UserDto userDto1 = new UserDto();
        userDto1.setRoleCode("admin");
        userDto1.setUserIds(userIds);
        UserDto userDto2 = new UserDto();
        userDto2.setRoleCode("product-manager");
        userDto2.setUserIds(userIds);
        List<UserDto> users = Arrays.asList(userDto1,userDto2);
        String nonStandardItems = "non-standard-items";
        String materialType = "材料类别测试字符";
        ProductCategoryExtraInfoEntity extraInfoEntity = new ProductCategoryExtraInfoEntity();
        when(extraInfoRepository.queryExtraInfoByProductCategoryId(id)).thenReturn(extraInfoEntity);

        doNothing().when(systemService).updateResource(any(ResourceDto.class));
        doNothing().when(extraInfoRepository).updateProductCategoryExtraInfo(any(ProductCategoryExtraInfoEntity.class));

        productCategoryDomainService.updateRelatedUser(id, users, nonStandardItems, BigDecimal.TEN, materialType);

        verify(extraInfoRepository).queryExtraInfoByProductCategoryId(id);
        verify(systemService).updateResource(any(ResourceDto.class));
        assertEquals(nonStandardItems, extraInfoEntity.getNonStandardItems());
    }

    @Test(expected = BusinessException.class)
    public void testUpdateRelatedUser_ExtraInfoNotFound() {
        String id = "1";
        List<ManagerDto> userIds = new ArrayList<>();
        ManagerDto managerDto = new ManagerDto();
        managerDto.setId("123");
        managerDto.setRoleCode("manager");
        ManagerDto managerDto2 = new ManagerDto();
        managerDto2.setId("111");
        managerDto2.setRoleCode("manager");
        userIds.add(managerDto);
        userIds.add(managerDto2);
        UserDto userDto = new UserDto();
        userDto.setRoleCode("admin");
        userDto.setUserIds( userIds);
        List<UserDto> users = Collections.singletonList(userDto);
        String nonStandardItems = "non-standard-items";
        String materialType = "材料类别测试字符";
        when(extraInfoRepository.queryExtraInfoByProductCategoryId(id)).thenReturn(null);

        try {
            productCategoryDomainService.updateRelatedUser(id, users, nonStandardItems,BigDecimal.TEN, materialType);
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATA_NOT_FOUND.getCode(), e.getCode());
            throw e;
        }
    }

    @Test(expected = BusinessException.class)
    public void testUpdateRelatedUser_EmptyUsers() {
        String id = "1";
        List<UserDto> users = Collections.emptyList();
        String nonStandardItems = "non-standard-items";
        String materialType = "材料类别测试字符";
        try {
            productCategoryDomainService.updateRelatedUser(id, users, nonStandardItems,BigDecimal.TEN, materialType);
        } catch (BusinessException e) {
            assertEquals(StatusCode.INVALID_PARAMETER.getCode(), e.getCode());
            throw e;
        }
    }


    @Test
    public void testUpdateRelatedUser_extended_empty() {
        String id = "1";
        UserDto userDto = new UserDto();
        userDto.setRoleCode("admin");
        List<UserDto> users = Collections.singletonList(userDto);
        String nonStandardItems = "non-standard-items";
        String materialType = "材料类别测试字符";
        ProductCategoryExtraInfoEntity extraInfoEntity = new ProductCategoryExtraInfoEntity();
        when(extraInfoRepository.queryExtraInfoByProductCategoryId(id)).thenReturn(extraInfoEntity);
        productCategoryDomainService.updateRelatedUser(id, users, nonStandardItems,null, materialType);
        verify(extraInfoRepository).queryExtraInfoByProductCategoryId(id);
    }
    /* Ended by AICoder, pid:135cb36b4f3e7a714e650aa711c2033e62886c66 */
    @Test
    public void testQueryByIds() {
        List<String> ids = Arrays.asList("1", "2", "3");

        // 模拟返回的ProductCategoryVo列表
        List<ProductCategoryVo> mockVos = new ArrayList<>();
        ProductCategoryVo v1 = new ProductCategoryVo();
        v1.setId("1");
        mockVos.add(v1);
        when(productCategoryRepository.batchQueryProductSubclasses(ids)).thenReturn(mockVos);

        List<ProductCategoryInfoVo> result = productCategoryDomainService.queryByIds(ids);

        // 验证结果是否符合预期
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getId());

        verify(productCategoryRepository, times(1)).batchQueryProductSubclasses(ids);
    }

    @Test(expected = BusinessException.class)
    public void testQueryProductCategoryManagerById_CategoryNotFound() {
        when(productCategoryRepository.queryById("1")).thenReturn(null);
        productCategoryDomainService.queryProductCategoryManagerById("1");
    }

    @Test(expected = BusinessException.class)
    public void testQueryProductCategoryManagerById_ExtraInfoNotFound() {
        when(productCategoryRepository.queryById("1")).thenReturn(new ProductCategoryEntity());
        productCategoryDomainService.queryProductCategoryManagerById("1");
    }

    @Test
    public void testQueryProductCategoryManagerById_ValidResource() {
        ResourceVo resourceVo = new ResourceVo();
        Map<String, List<UserVo>> userRoleMap = new HashMap<>();
        for (RoleCodeEnum role : RoleCodeEnum.values()) {
            List<UserVo> users = Arrays.asList(new UserVo(), new UserVo());
            userRoleMap.put(role.getCode(), users);
        }
        resourceVo.setUserRoleMap(userRoleMap);

        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setId("1");
        productCategoryEntity.setProductName("Test Product");

        ProductCategoryExtraInfoEntity extraInfoEntity = new ProductCategoryExtraInfoEntity();
        extraInfoEntity.setId("1");
        extraInfoEntity.setProductLevel("1");
        extraInfoEntity.setProductComponent("Component");
        extraInfoEntity.setNonStandardItems("Non Standard Items");

        when(productCategoryRepository.queryById("1")).thenReturn(productCategoryEntity);
        when(extraInfoRepository.queryExtraInfoByProductCategoryId("1")).thenReturn(extraInfoEntity);
        when(systemService.getResourceById("1")).thenReturn(resourceVo);

        ProductCategoryManagerVo result = productCategoryDomainService.queryProductCategoryManagerById("1");

        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("Test Product", result.getProductName());

        assertNotNull(result.getProductManager());
        assertNotNull(result.getProductSe());
        assertNotNull(result.getCostDirector());
        assertNotNull(result.getBusinessDirector());
        assertNotNull(result.getConfigurationManager());
        assertNotNull(result.getLogisticsDirector());
        assertNotNull(result.getMarketRepresentative());
        assertNotNull(result.getAfterSalesRepresentative());
        assertNotNull(result.getEngineeringDirector());
    }

    /* Started by AICoder, pid:1f03bq4990aea6c148220bec0035c910fee1a126 */
    @Test
    public void testGetAllProductSubcategory() {
        productCategoryDomainService.getAllProductSubcategory();
        // 验证结果是否符合预期
        verify(productCategoryRepository, times(1)).queryAllProductSubcategory();
    }
    /* Ended by AICoder, pid:1f03bq4990aea6c148220bec0035c910fee1a126 */


    @Test
    public void testQueryByPathName_empty() {
        Set<String> pathList = new HashSet<>();
        productCategoryDomainService.queryByPathName(pathList);
        // 验证结果是否符合预期
        verify(productCategoryRepository, times(0)).queryAllProductSubcategory();
    }

    @Test
    public void testQueryByPathName() {
        Set<String> pathList = new HashSet<>();
        pathList.add("path1");
        List<ProductSubcategoryVo> list = new ArrayList<>();
        ProductSubcategoryVo productSubcategoryVo = new ProductSubcategoryVo();
        productSubcategoryVo.setId("1");
        productSubcategoryVo.setPathName("path1");
        list.add(productSubcategoryVo);
        when(productCategoryRepository.queryAllProductSubcategory()).thenReturn(list);
        productCategoryDomainService.queryByPathName(pathList);
        // 验证结果是否符合预期
        verify(productCategoryRepository, times(1)).queryAllProductSubcategory();
    }
}
/* Ended by AICoder, pid:93ce29801eg36d114cec092093713319d3950261 */
