package com.zte.uedm.dcdigital.application.material.impl;

/* Started by AICoder, pid:wabfbh4ccfo338214fd7080ad0f0dc478504c243 */
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.dto.ApprovalInformationDto;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialAddDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialBatchAddDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialEditDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.TemplateImportVo;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataMultiPart;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MaterialMaintenanceCommandServiceImplTest {

    @InjectMocks
    private MaterialMaintenanceCommandServiceImpl materialMaintenanceCommandService;

    @Mock
    private MaterialDomainService materialDomainService;

    /* Started by AICoder, pid:kccbek3975i1d3414d760a1aa035840a7f229877 */
    @Mock
    private ProductBrandQueryService productBrandQueryService;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FormDataMultiPart file;

    @Mock
    private FormDataBodyPart part;

    private ApprovalInformationDto informationDto;

    private static final String VALID_ID = "1";
    private static final Integer MATERIAL_TYPE = DocumentRelateResourceTypeEnum.MATERIAL.getCode();
    private static final Integer BRAND_TYPE = DocumentRelateResourceTypeEnum.BRAND.getCode();
    private static final Integer INVALID_TYPE = -1;

    @Before
    public void setUp() {
        informationDto = new ApprovalInformationDto();
        informationDto.setApprovalId("1");
        informationDto.setApprovalResult(1);
        informationDto.setApprovalStatus(1);
    }

    @Test
    public void testWithdrawByApprovalId() {
        String approvalId = "1";
        materialMaintenanceCommandService.withdrawByApprovalId(approvalId);
        verify(materialDomainService).withdrawByApprovalId(approvalId);
    }

    @Test
    public void testApprovalInformationUpdate() {
        materialMaintenanceCommandService.approvalInformationUpdate(informationDto);
        verify(materialDomainService).updateByApprovalInformation(informationDto);
    }
    @Test
    public void testPdmManualSync() {
        materialMaintenanceCommandService.pdmManualSync("approvalId","1");
        verify(materialDomainService, atLeastOnce()).pdmSync(anyString(),"1");
    }

    // 测试 addMaterial 方法
    @Test
    public void given_addMaterial_when_called_then_addsMaterialAndRelatesDocuments() {
        // Given
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setDocumentIds(Arrays.asList("doc1", "doc2"));
        String pdmId = "pdm1";
        String materialId = "material1";
        addDto.setOperate("2");
        addDto.setSalesCode("180000484506");
        when(materialDomainService.addPdmInfo(addDto)).thenReturn(pdmId);
        when(materialDomainService.addMaterial(addDto, pdmId, null)).thenReturn(materialId);

        // When
        materialMaintenanceCommandService.addMaterial(addDto);

        // Then
        verify(materialDomainService).addPdmInfo(addDto);
        verify(materialDomainService).addMaterial(addDto, pdmId, null);
        verify(materialDomainService).relateDocuments(materialId, addDto.getDocumentIds());
    }
    /* Started by AICoder, pid:m1164y5464874a214ee30afe000e5c1bc6642690 */
    @Test
    public void testAddMaterial_NonPdmSalesCode() {
        MaterialAddDto addDto = new MaterialAddDto();
        addDto.setSalesCode("181234567890aa");
        addDto.setBrand("BrandA");
        addDto.setName("NameA");
        addDto.setDocumentIds(Collections.singletonList("doc1"));
        doNothing().when(materialDomainService).nonPdmNameUniqueCheck(anyString(), anyString(),any());
        when(materialDomainService.addOrUpdateOthInfo(addDto)).thenReturn("othInfoId");

        materialMaintenanceCommandService.addMaterial(addDto);

        verify(materialDomainService, times(1)).nonPdmNameUniqueCheck(addDto.getName(), addDto.getSalesCode(),null);
        verify(materialDomainService, times(1)).addOrUpdateOthInfo(addDto);
        verify(materialDomainService, times(1)).addMaterial(addDto, null, "othInfoId");

    }
    @Test
    public void given_editMaterial_when_oth_sales_code() {
        // Given
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setOperate("2");

        editDto.setSalesCode("180000484506");
        materialMaintenanceCommandService.editMaterial(editDto);
        // Then
        verify(materialDomainService).editMaterial(editDto, null);
    }
    /* Ended by AICoder, pid:m1164y5464874a214ee30afe000e5c1bc6642690 */
    // 测试 editMaterial 方法
    @Test
    public void given_editMaterial_when_called_then_editsMaterial() {
        // Given
        MaterialEditDto editDto = new MaterialEditDto();
        editDto.setOperate("2");

        // When
        materialMaintenanceCommandService.editMaterial(editDto);
        // Then
        verify(materialDomainService).editMaterial(editDto, null);
    }

    // 测试 deleteMaterial 方法
    @Test
    public void given_deleteMaterial_when_called_then_deletesMaterial() {
        // Given
        String id = "material1";

        // When
        materialMaintenanceCommandService.deleteMaterial(id);

        // Then
        verify(materialDomainService).deleteMaterial(id);
    }


    // 测试 exportTemplate 方法 - 异常处理
    @Test(expected = BusinessException.class)
    public void given_exportTemplate_when_exception_then_throwsBusinessException() throws Exception {
        // Given
        materialMaintenanceCommandService.exportTemplate(response);
    }

    /* Started by AICoder, pid:39b53ba628c4383146ec0b0fd00cce4e86b5f9dd */
    @Test
    public void testHandlingMaterialOperation() {
        String id = "123";
        String operation = "add";

        materialMaintenanceCommandService.handlingMaterialOperation(id, operation,null);

        verify(materialDomainService, times(1)).handlingMaterialOperation(id, operation,null);
    }

    @Test
    public void testBatchHandlingMaterial() {
        List<String> ids = Arrays.asList("123", "456", "789");
        String operation = "remove";

        materialMaintenanceCommandService.batchHandlingMaterial(ids, operation,null);

        verify(materialDomainService, times(1)).batchHandlingMaterial(ids, operation,null);
    }
    /* Ended by AICoder, pid:39b53ba628c4383146ec0b0fd00cce4e86b5f9dd */

    @Test(expected = BusinessException.class)
    public void importTemplateTest() {
        materialMaintenanceCommandService.importTemplate(file,"ca");
    }

    @Test
    public void importTemplateTest1() {
        when(file.getField("file")).thenReturn(part);
        materialMaintenanceCommandService.importTemplate(file,"ca");
        verify(materialDomainService, times(1)).importTemplate(part,"ca");
    }

    /* Started by AICoder, pid:4d1647095139b10147c50ac0a01bf0586519f1c5 */
    @Test
    public void specialBatchImport_empty() {
        when(file.getField("file")).thenReturn(part);
        materialMaintenanceCommandService.specialBatchImport(file,"ca");
        verify(materialDomainService, times(1)).importTemplate(part,"ca");
    }

    @Test
    public void specialBatchImport_fail() {
        when(file.getField("file")).thenReturn(part);
        TemplateImportVo vo= new TemplateImportVo();
        vo.setMaterialName("name");
        vo.setCheckResult("1");
        List<TemplateImportVo> templateImportVos = new ArrayList<>();
        templateImportVos.add(vo);
        when(materialDomainService.importTemplate(part,"ca")).thenReturn(templateImportVos);

        List<TemplateImportVo> importVoList = materialMaintenanceCommandService.specialBatchImport(file, "ca");
        assertFalse(importVoList.isEmpty());
        verify(materialDomainService, times(1)).importTemplate(part,"ca");
    }
    @Test
    public void specialBatchImport_success() {
        when(file.getField("file")).thenReturn(part);
        TemplateImportVo vo= new TemplateImportVo();
        vo.setMaterialName("name");
        vo.setCheckResult("0");
        List<TemplateImportVo> templateImportVos = new ArrayList<>();
        templateImportVos.add(vo);
        when(materialDomainService.importTemplate(part,"ca")).thenReturn(templateImportVos);

        List<TemplateImportVo> importVoList = materialMaintenanceCommandService.specialBatchImport(file, "ca");
        assertTrue(importVoList.isEmpty());
        verify(materialDomainService, times(1)).importTemplate(part,"ca");
    }
    /* Ended by AICoder, pid:4d1647095139b10147c50ac0a01bf0586519f1c5 */
    @Test
    public void batchSubmitTest1()
    {
        materialMaintenanceCommandService.batchSubmit(new ArrayList<>());
        verify(materialDomainService, never()).batchSubmit(Mockito.anyList());
    }

    @Test
    public void batchSubmitTest2()
    {
        materialMaintenanceCommandService.batchSubmit(Arrays.asList(new MaterialBatchAddDto()));
        verify(materialDomainService, times(1)).batchSubmit(Mockito.anyList());
    }

    @Test(expected = BusinessException.class)
    public void testSelectResourceInfo_InvalidType() {
        List<String> ids = Arrays.asList(VALID_ID);
        materialMaintenanceCommandService.selectResourceInfo(ids, INVALID_TYPE);
    }

    @Test
    public void testSelectResourceInfo_MaterialType() {
        List<String> ids = Arrays.asList(VALID_ID);
        List<DocumentCitedVo> expected = Arrays.asList(new DocumentCitedVo());
        when(materialDomainService.selectByIds(ids)).thenReturn(expected);

        List<DocumentCitedVo> result = materialMaintenanceCommandService.selectResourceInfo(ids, MATERIAL_TYPE);

        assertEquals(expected, result);
        verify(materialDomainService, times(1)).selectByIds(ids);
    }

    @Test
    public void testSelectResourceInfo_BrandType() {
        List<String> ids = Arrays.asList(VALID_ID);
        List<DocumentCitedVo> expected = Arrays.asList(new DocumentCitedVo());
        when(productBrandQueryService.selectByIds(ids)).thenReturn(expected);

        List<DocumentCitedVo> result = materialMaintenanceCommandService.selectResourceInfo(ids, BRAND_TYPE);

        assertEquals(expected, result);
        verify(productBrandQueryService, times(1)).selectByIds(ids);
    }

    @Test
    public void testSelectResourceInfo_OtherType() {
        List<String> ids = Arrays.asList(VALID_ID);
        Integer otherType = 3; // 假设99是一个无效的类型

        List<DocumentCitedVo> result = materialMaintenanceCommandService.selectResourceInfo(ids, otherType);

        assertEquals(Collections.emptyList(), result);
    }
    /* Ended by AICoder, pid:nccbeh3975v1d3414d760a1aa035843a7f299877 */
}
/* Ended by AICoder, pid:wabfbh4ccfo338214fd7080ad0f0dc478504c243 */
