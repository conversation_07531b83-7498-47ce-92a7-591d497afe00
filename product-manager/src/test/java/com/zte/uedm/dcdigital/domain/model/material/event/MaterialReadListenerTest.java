/* Started by AICoder, pid:6791e687a0q0cc8145ab0b06c14f6a4b1d827861 */
package com.zte.uedm.dcdigital.domain.model.material.event;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.enums.TemplateCheckResultEnum;
import com.zte.uedm.dcdigital.domain.model.material.vobj.TemplateObj;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.TemplateImportVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class MaterialReadListenerTest {

    private MaterialReadListener listener;

    @Mock
    private AnalysisContext analysisContext;

    @Mock
    private ReadRowHolder readRowHolder;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        listener = new MaterialReadListener("");
        when(analysisContext.readRowHolder()).thenReturn(readRowHolder);
    }

    @Test
    public void testDoAfterAllAnalysed() {
        listener.doAfterAllAnalysed(analysisContext);
    }


    @Test
    public void testOnException() {
        Exception exception = new RuntimeException("Test Exception");
        listener.onException(exception, analysisContext);
    }

    @Test
    public void testInvoke_allPass() {
        listener.setProductCategoryName("DCIM");
        String productCategoryName = listener.getProductCategoryName();
        TemplateObj templateObj = getTestData();
        when(readRowHolder.getRowIndex()).thenReturn(1);
        listener.invoke(templateObj, analysisContext);


        when(readRowHolder.getRowIndex()).thenReturn(2);
        listener.invoke(templateObj, analysisContext);
        List<TemplateImportVo> successList = listener.getPdmSalesCodeList();
        Assert.assertTrue(!successList.isEmpty());
    }
    @Test
    public void testInvoke_checkFail() {
        listener.setProductCategoryName("DCIM");
        String productCategoryName = listener.getProductCategoryName();
        TemplateObj templateObj = getTestData();
        when(readRowHolder.getRowIndex()).thenReturn(2);
        //非18销售代码
        templateObj.setSalesCode("OTH123");
        listener.invoke(templateObj, analysisContext);
        //非18销售代码
        templateObj.setSalesCode(null);
        listener.invoke(templateObj, analysisContext);
        try(MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            //日期格式错误
            templateObj.setExpirationDate("123456");
            listener.invoke(templateObj, analysisContext);
            //日期过期
            templateObj.setExpirationDate("2024/12/12");
            listener.invoke(templateObj, analysisContext);
            //描述为空
            templateObj.setDescription("");
            listener.invoke(templateObj, analysisContext);
            //单位为空
            templateObj.setUnit("");
            listener.invoke(templateObj, analysisContext);
            //枚举为空
            templateObj.setPurchaseMode("");
            listener.invoke(templateObj, analysisContext);
            //枚举不存在
            templateObj.setPurchaseMode("测试");
            listener.invoke(templateObj, analysisContext);
            //物料名称为空
            templateObj.setMaterialName("");
            listener.invoke(templateObj, analysisContext);

            //分组2超长
            templateObj.setGroupL2("180000338711180000338711180000338711180000338711180000338711");
            listener.invoke(templateObj, analysisContext);
            //分组2不存在
            templateObj.setGroupL2("");
            listener.invoke(templateObj, analysisContext);
            //分组1超长
            templateObj.setGroupL1("180000338711180000338711180000338711180000338711180000338711");
            listener.invoke(templateObj, analysisContext);
            //分组1不存在
            templateObj.setGroupL1("");
            listener.invoke(templateObj, analysisContext);

            //产品小类名称不匹配
            templateObj.setProductName("product");
            listener.invoke(templateObj, analysisContext);
            //产品小类名称超长
            templateObj.setProductName("180000338711180000338711180000338711180000338711180000338711");
            listener.invoke(templateObj, analysisContext);
            //产品小类名称不存在
            templateObj.setProductName("");
            listener.invoke(templateObj, analysisContext);
        }
    }
    @Test
    public void testInvoke_checkRecommendedLevelFail() {
        listener.setProductCategoryName("DCIM");
        TemplateObj templateObj = getTestData();
        when(readRowHolder.getRowIndex()).thenReturn(2);
        //非18销售代码
        templateObj.setSalesCode("OTH123");
        listener.invoke(templateObj, analysisContext);
        //非18销售代码
        templateObj.setSalesCode(null);
        listener.invoke(templateObj, analysisContext);
        try(MockedStatic<I18nUtil> i18nUtilMockedStatic = Mockito.mockStatic(I18nUtil.class)) {
            //日期格式错误
            templateObj.setRecommendedLevel("123456");
            templateObj.setUnit("12345678911111");
            listener.invoke(templateObj, analysisContext);
        }
    }
    private TemplateObj getTestData() {
        TemplateObj templateObj = new TemplateObj();
        templateObj.setProductName("DCIM");
        templateObj.setGroupL1("软件模块");
        templateObj.setGroupL2(null);
        templateObj.setMaterialName("基础软件包");
        templateObj.setBrand("ZTE（中兴）");
        templateObj.setSpecificationModel(null);
        templateObj.setPurchaseMode("战采");
        templateObj.setSupplier("ZTE（中兴）");
        templateObj.setUnit("套KIT");
        templateObj.setDescription("DCIM/AI基础功能，提供工程配置，数据采集，基础监控，能效，告警，日志，安全，备份恢复，联动，自定义报表功能 。必配，一套管理系统配置一套,");
        templateObj.setSalesCode("180000338711");
        templateObj.setProductionCode("127033341003");
        templateObj.setSalesStatus("正式销售");
        templateObj.setExpirationDate("2025/06/10");
        templateObj.setService("服务跟随项目，售后提供");
        return templateObj;
    }
}
/* Ended by AICoder, pid:6791e687a0q0cc8145ab0b06c14f6a4b1d827861 */