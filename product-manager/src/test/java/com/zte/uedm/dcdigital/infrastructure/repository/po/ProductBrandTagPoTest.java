package com.zte.uedm.dcdigital.infrastructure.repository.po;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class ProductBrandTagPoTest {

    private ProductBrandTagPo productBrandTagPo;

    @Before
    public void setUp() {
        productBrandTagPo = new ProductBrandTagPo();
    }

    @Test
    public void testGettersAndSetters() {
        // Arrange
        String id = "123";
        String tagName = "Electronics";
        String brandId = "456";

        // Act
        productBrandTagPo.setId(id);
        productBrandTagPo.setTagName(tagName);
        productBrandTagPo.setBrandId(brandId);

        // Assert
        assertEquals("The ID should match", id, productBrandTagPo.getId());
        assertEquals("The Tag Name should match", tagName, productBrandTagPo.getTagName());
        assertEquals("The Brand ID should match", brandId, productBrandTagPo.getBrandId());
    }

    @Test
    public void testToString() {
        // Arrange
        productBrandTagPo.setId("123");
        productBrandTagPo.setTagName("Electronics");
        productBrandTagPo.setBrandId("456");

        // Act
        String result = productBrandTagPo.toString();

        // Assert
        assertTrue("toString should contain the ID", result.contains("id=123"));
        assertTrue("toString should contain the Tag Name", result.contains("tagName=Electronics"));
        assertTrue("toString should contain the Brand ID", result.contains("brandId=456"));
    }
}