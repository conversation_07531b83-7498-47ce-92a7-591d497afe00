{"product_name": "@ms.manager.project@", "version": "@dc-digital.service.version@", "description": "@ms.manager.project@ services.", "service_list": [{"service_name": "@ms.manager.project@", "version": "@dc-digital.service.version@", "common_service_list": [{"common_service_name": "kafka-0", "resource_name": "zenap_kafka"}, {"common_service_name": "redis-0", "resource_name": "zenap_redis"}, {"common_service_name": "postgresql-0", "resource_name": "<EMAIL>@"}], "volume_list": [], "env_list": [{"name": "db_version", "value": ""}, {"name": "scene", "value": ""}], "other_list": []}]}