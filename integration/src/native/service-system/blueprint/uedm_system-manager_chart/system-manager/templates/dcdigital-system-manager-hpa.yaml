# 定义变量
## 引用 tpl 模版
{{- $okiFullName := include "oki.fullname" .  -}}
## hpa 资源名
{{- $hpaName := printf "%s-%s" $okiFullName "hpa" -}}
## 微服务名称
{{- $msName := printf "%s-%s" $okiFullName "deploy" -}}
## hpa api version 当前需要使用的版本，默认值来源于 values.yaml 文件
{{- $apiVersion := index .Values.apiVersionMigrateTab "autoscaling/HorizontalPodAutoscaler" -}}
## hpa api version 支持的版本列表: "v2beta1" "v2beta2" "v2"
{{- $allowedApiVerisonList := list "v2beta1" "v2beta2" "v2" "v1" -}}
## 最大最小副本数配置
{{- $min := .Values.replicas.system_manager.min -}}
{{- $max := .Values.replicas.system_manager.max -}}
## 弹缩的目标值(使用率应该达到的目标值),
# 注意，
# 如果组件蓝图不支持 cpu，则 $cpuAverageValue 可以赋值为 ''
# 如果组件蓝图不支持 memory， $memoryAverageValue 可以赋值为 ''
{{- $cpuAverageValue :=  .Values.replicas.system_manager.cpuAverageValue -}}
{{- $memoryAverageValue :=  .Values.replicas.system_manager.memoryAverageValue -}}
## 
# 支持忽略 hpa, 条件语句的含义：如果$apiVersion不等于"ignore"，或者$apiVersion在$allowedApiVerisonList中，那么条件为真
# 忽略配置如下
#apiVersionMigrateTab:
#  HorizontalPodAutoscaler:
#    autoscaling: ""
{{- if and ( ne $apiVersion "ignore" ) ( has $apiVersion $allowedApiVerisonList ) }}
apiVersion: autoscaling/{{ $apiVersion }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $hpaName }}
spec:
  scaleTargetRef:
    kind: Deployment
    name:  {{ $msName }}
    apiVersion: apps/v1
  minReplicas: {{ $min }}
  maxReplicas: {{ $max }}
  metrics:
  {{- if eq $apiVersion "v2beta1" }}
    {{- if $cpuAverageValue }}
    - type: Resource
      resource:
        name: cpu
        targetAverageValue: {{ $cpuAverageValue }}
    {{- end -}}
    {{- if $memoryAverageValue }}
    - type: Resource
      resource:
        name: memory
        targetAverageValue: {{ $memoryAverageValue }}
    {{- end -}}
  {{- else }}
    {{- if $cpuAverageValue }}
    - type: Resource
      resource:
        name: cpu
        target: 
          type: AverageValue
          averageValue: {{ $cpuAverageValue }}
    {{- end -}}
    {{- if $memoryAverageValue }}
    - type: Resource
      resource:
        name: memory
        target: 
          type: AverageValue
          averageValue: {{ $memoryAverageValue }}
    {{- end -}}
  {{- end }}
{{- end -}}