# Started by AICoder, pid:6976dj2c11w6b45144830bfb30f4b00c68a20c22
apiVersion: {{ template "oki.pbc.apiVersion" . }}
kind: MsbApi
metadata:
  name: {{ include "oki.fullname" . }}document-manager-v1
  namespace: {{.Values.serviceImage.tenant}}
  labels:
    # 管理pbc组件名
    componentName: {{ include "oki.fullname" . }}-document-manager-pbc
    # 关联pbc组件api服务名
    pbc.apiServiceName: document-manager-v1
spec:
  httpRules:
    - match:
        path: /api/document-manager/v1
        protocol: REST
        rewriteTarget: /api/document-manager/v1
      backend:
        service:
          name: {{ include "oki.fullname" . }}-document-manager-v1
          portName: http
      advancedConfig:
        lbPolicy: round-robin
# Ended by AICoder, pid:6976dj2c11w6b45144830bfb30f4b00c68a20c22