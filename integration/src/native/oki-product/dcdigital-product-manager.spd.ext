releaseName: @ms.manager@
version: @dc-digital.service.version@
description: @ms.manager@ services
parameters:
  values:
    terminationGracePeriodSeconds: ${termination_grace_period_seconds}
    annotations:
      product_manager:
        k8s.v1.cni.cncf.io/networks: '[]'
        volume.hostpaths: '[{"name":"volume-for-logs","quota":""}]'
    #云原生应用 镜像规范
    serviceImage:
       repository: '${imageRepository}'
       tenant: '${imageTenant}'
    envs:
      product_manager:                     # 业务容器名称，可以自取
        - name: TZ
          value: Asia/Shanghai  
        - name: net_api_eth
          value: "eth0"      
        - name: permit_root_start
          value: "true"   
        - name: pvc_type
          value: "nfs"
      init:                            # 初始化容器
        - name: permit_root_start
          value: "true"   
        - name: pvc_type
          value: "nfs"
        - name: LOG_FILE_LOG
          value: "/home/<USER>"
        - name: LOG_FILE_GCLOG
          value: "/home/<USER>/gclog"
        - name: OES_CHOWN_DIR_GCLOG
          value: "/home/<USER>/gclog"
        - name: OES_CHOWN_DIR_LOG
          value: "/home/<USER>"
    #云原生应用使用 使用第三方公共服务 方式一
    kafkaConfig:
      OPENPALETTE_KAFKA_ADDRESS: get_property:[$(${KAFKA_INS_NAME}),${KAFKA_ADDRESS},${KAFKA_TYPE}]
      OPENPALETTE_KAFKA_PORT: get_property:[$(${KAFKA_INS_NAME}),${KAFKA_PORT},${KAFKA_TYPE}]
    redisConfig:
      OPENPALETTE_REDIS_ADDRESS: get_property:[$(${REDIS_INS_NAME}),${REDIS_ADDRESS},${REDIS_TYPE}]
      OPENPALETTE_REDIS_PORT: get_property:[$(${REDIS_INS_NAME}),${REDIS_PORT},${REDIS_TYPE}]
      OPENPALETTE_REDIS_PASSWORD: get_property:[$(${REDIS_INS_NAME}),${REDIS_PASSWORD},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_ADDRESS: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_ADDRESS},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_PORT: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_PORT},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_MASTERNAME: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_MASTERNAME},${REDIS_TYPE}]
    pgConfig:
      OPENPALETTE_PG_ADDRESS: get_property:[$(${UEDM_PRODUCT_MANAGER_PG_INS_NAME}),${PG_ADDRESS},${PG_TYPE}]
      OPENPALETTE_PG_PORT: get_property:[$(${UEDM_PRODUCT_MANAGER_PG_INS_NAME}),${PG_PORT},${PG_TYPE}]
      OPENPALETTE_PG_DBNAME: get_property:[$(${UEDM_PRODUCT_MANAGER_PG_INS_NAME}),${PG_DBNAME},${PG_TYPE}]
      OPENPALETTE_PG_USERNAME: get_property:[$(${UEDM_PRODUCT_MANAGER_PG_INS_NAME}),${PG_USERNAME},${PG_TYPE}]
      OPENPALETTE_PG_PASSWORD: get_property:[$(${UEDM_PRODUCT_MANAGER_PG_INS_NAME}),${PG_PASSWORD},${PG_TYPE}]
    pgInsConfig:
      TYPE: ${PG_TYPE}
      INSTANCE_NAME: ${UEDM_PRODUCT_MANAGER_PG_INS_NAME}
      ENTITY_NAME: postgresql
      USER_NAME: ${PG_USERNAME}
      DB_NAME: ${PG_DBNAME}
      SECRET_NAME: ${PG_SECRET_NAME}
      DB_PWD: ${PG_PASSWORD}
    #云原生应用 volume 定义和使用
    #volumes:
      #product_manager:
    #云原生应用 容器资源定义
    resources:
      product_manager:
        requests:
          cpu: 0.1
          memory: 0.1Gi
        limits:
          cpu: 3
          memory: 4Gi
      init:
        requests:
          cpu: 0.1
          memory: 0.1Gi
        limits:
          cpu: 3
          memory: 4Gi
    # 微服务自己的部署参数
    serverEnv:
      enable_ssl: ${enable_ssl}
    # 云原生应用 弹缩最大最小副本数
    replicas:
      product_manager:
        init: 1
        min: 1
        max: 1
  #云原生应用使用 CSM 公共服务 -- 使用replaces能力，向部署配置文件resources暴露变量以便应用可按需替换
  replaces:
    public:
      - name: app_network_annotation
        value: '[{"name":"net-api","namespace":"default", "interface":"eth1", "cni-args":{"dpdk": false}},{"name":"lan", "interface":"eth2", "cni-args": {"dpdk": false}}]' 
        type: default
      - name: termination_grace_period_seconds
        type: default
        value: 0
      - name: enable_ssl
        value: "false"
        type: default
      # kafka相关
      - name: KAFKA_INS_NAME
        value: kafka-0
        type: default
      - name: KAFKA_TYPE
        type: default
        value: ts_secret
      - name: KAFKA_ADDRESS
        value: OPENPALETTE_KAFKA_ADDRESS
        type: default
      - name: KAFKA_PORT
        type: default
        value: OPENPALETTE_KAFKA_PORT
      # pg相关
      - name: UEDM_PRODUCT_MANAGER_PG_INS_NAME
        value: db-product-manager
        type: default
      - name: PG_TYPE
        type: default
        value: ts_secret
      - name: PG_ADDRESS
        value: OPENPALETTE_PG_ADDRESS
        type: default
      - name: PG_PORT
        type: default
        value: OPENPALETTE_PG_PORT
      - name: PG_DBNAME
        type: default
        value: OPENPALETTE_PG_DBNAME
      - name: PG_USERNAME
        value: OPENPALETTE_PG_USERNAME
        type: default
      - name: PG_PASSWORD
        type: default
        value: OPENPALETTE_PG_PASSWORD
      - name: PG_SECRET_NAME
        value: openpalette_secret_name
        type: default
      #redis宏变量
      - name: REDIS_INS_NAME
        value: redis-0
        type: default
      - name: REDIS_SECRET_NAME
        value: openpalette_secret_name
        type: default			
      - name: REDIS_TYPE
        value: ts_secret
        type: default
      - name: REDIS_ADDRESS
        value: OPENPALETTE_REDIS_ADDRESS
        type: default
      - name: REDIS_PORT
        value: OPENPALETTE_REDIS_PORT
        type: default
      - name: REDIS_PASSWORD
        value: OPENPALETTE_REDIS_PASSWORD
        type: default
      - name: REDIS_SENTINEL_ADDRESS
        value: OPENPALETTE_REDIS_SENTINEL_ADDRESS
        type: default
      - name: REDIS_SENTINEL_PORT
        value: OPENPALETTE_REDIS_SENTINEL_PORT
        type: default
      - name: REDIS_SENTINEL_MASTERNAME
        value: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
        type: default
      # OKI在TCF场景下，自动处理 imageRepository和imageTenant
      - name: imageRepository
        type: default
        value: swr:2512
      - name: imageTenant
        section: default
        value: uedm
      # 微服务自己的资源参数
