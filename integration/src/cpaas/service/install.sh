#!/bin/bash
#上传zenap-ume服务到paas软件仓库
set -e
DIRNAME=`dirname $0`
WORK_HOME=`cd $DIRNAME/; pwd`
ZARTCLI=/root/zartcli/zartcli
VERSION_NO=@image.version@
TENANTS=@tenant.id@
SERVICE_NAME=@service.name@

uploadMicroserviceBP() {
	if [ $# -ne 1 ]
	then
		echo usage:uploadSlAndMs MICROSERVICE_NAME
		exit 1
	fi
	local MICROSERVICE_NAME=$1
	local microserviceBPPath=$WORK_HOME/blueprint/"$TENANTS"_"$MICROSERVICE_NAME"_microservice
    if [ ! -d ${microserviceBPPath} ]
    then 
        echo "MS blueprint dir doesnot exist,path:${microserviceBPPath},please check."
	    exit 2
    fi
	$ZARTCLI -o delete -i $TENANTS -m bp -t microservice -n $MICROSERVICE_NAME -v $VERSION_NO || true >/dev/null
	$ZARTCLI -o upload -i $TENANTS -m bp -t microservice -n $MICROSERVICE_NAME -v $VERSION_NO -b yes -p $microserviceBPPath
}

uploadServiceBP() {
	local serviceBPPath=$WORK_HOME/blueprint/"$TENANTS"_"$SERVICE_NAME"_service
    if [ ! -d ${serviceBPPath} ]
    then 
        echo "Service blueprint dir doesnot exist,path:${serviceBPPath},please check."
	    exit 2
    fi
	$ZARTCLI -o delete -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO || true >/dev/null
	$ZARTCLI -o upload -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO -b yes -p $serviceBPPath
}

checkImageState() {
	if [ $# -ne 2 ]
	then
		echo usage:checkImageState IMAGE_NAME IMAGE_NO
		exit 1
	fi
	local IMAGE_NAME=$1
	local IMAGE_NO=$2
	local image_state=""
	for i in {1..60}
	do
		image_state=`$ZARTCLI -o query -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO | grep '"status":' | awk '{print $2}'`
		image_state=`echo $image_state | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g'`
		if [ $image_state = "available" ];then
			break
		else
			sleep 3
			continue
		fi
	done
	echo $image_state
}

buildImage() {
	if [ $# -ne 2 ]
	then
		echo usage:buildImage IMAGE_NAME IMAGE_NO
		exit 1
	fi
	local IMAGE_NAME=$1
	local IMAGE_NO=$2
	local IMAGE_STAT=""
	local dockerFilePath=$WORK_HOME/"$TENANTS"-"$IMAGE_NAME"
    if [ ! -d ${dockerFilePath} ]
    then 
        echo "The directory for Dockerfile doesnot exist,path:${dockerFilePath},please check."
	    exit 2
    fi
	for i in {1..2}
	do
		$ZARTCLI -o delete -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO || true >/dev/null
		$ZARTCLI -o build -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO -b yes -p $dockerFilePath
		IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_NO)
		if [ $IMAGE_STAT = "available" ];then
			break
		fi
	done
	if [ $IMAGE_STAT = "available" ];then
		echo "Build image $IMAGE_NAME success."
	else
		echo "Build image $IMAGE_NAME failed."
		exit 1
	fi
}

#Begin
echo "================================"
echo @WORK_HOME@ $WORK_HOME
echo @VERSION_NO@ $VERSION_NO
echo @TENANTS@ $TENANTS
echo "================================"

echo "=========Begin Upload $SERVICE_NAME========="
cd $WORK_HOME
#构建组件的基础镜像，无dependency的组件可以删除此段
#for dir in $(ls)
#do
#	if [ -d $dir ] && [ $dir == "$TENANTS"-"$SERVICE_NAME"-dependency ];then
#		ms=${dir#"$TENANTS"-}
#		buildImage $ms $VERSION_NO
#	fi
#done
#构建组件的微服务镜像和上传微服务蓝图
for dir in $(ls)
do
	if [ -d $dir ] && [ $dir != blueprint ] && [ $dir != "$TENANTS"-"$SERVICE_NAME"-dependency ];then
		ms=${dir#"$TENANTS"-}
		buildImage $ms $VERSION_NO
		uploadMicroserviceBP $ms
	fi
done
#上传组件服务蓝图
uploadServiceBP

exit 0
